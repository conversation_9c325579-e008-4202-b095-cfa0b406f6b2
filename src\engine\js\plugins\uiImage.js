(()=>{
    //=============================================================================
    // UIImage Class - 图片组件，参考UIButton实现
    //=============================================================================

    /**
     * 图片组件类 - 继承自 Sprite，参考UIButton的尺寸设置实现
     */
    class UIImage extends Sprite {
        constructor(properties = {}) {
            super();

            console.log('🖼️ UIImage: 创建图片组件', properties);

            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'UIImage';

            // 🔑 使用UIComponentUtils添加UIComponent功能
            if (window.UIComponentUtils) {
                window.UIComponentUtils.applyToSprite(this, properties);
            }

            // 🔑 使用UIScriptManager添加脚本功能
            if (window.UIScriptManager) {
                window.UIScriptManager.applyToObject(this, properties);
                console.log('🔧 UIImage: 脚本系统初始化完成', {
                    hasComponentScripts: !!this.componentScripts,
                    scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                    scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                });
            }

            // 初始化图片组件
            this.initializeImage(properties);

            // 🔑 标记为已创建，onStart会在添加到父容器时执行
            this._isCreated = true;
        }

        /**
         * 初始化图片组件
         * @param {Object} properties 图片属性
         */
        initializeImage(properties) {
            // 基础属性
            this._width = properties.width || 100;
            this._height = properties.height || 100;
            // 🔑 统一使用 src 属性，兼容 imagePath
            this._src = properties.src || properties.imagePath || '';

            // 回调
            this.onLoadCallback = null;

            // 源图片bitmap
            this.sourceBitmap = null;

            // 🔑 缓存键，用于避免重复绘制
            this._lastRegionKey = null;

            // 裁切相关属性
            this.regions = properties.regions || [];
            this.currentRegionIndex = properties.currentRegionIndex || 0;
            this.gridRows = properties.gridRows || 1;
            this.gridCols = properties.gridCols || 1;

            // 智能裁切相关属性
            this.smartCropData = properties.smartCropData || null;

            // 🔑 九宫格相关属性
            this.enableNinePatch = properties.enableNinePatch || false;
            this.ninePatch = properties.ninePatch || { left: 10, top: 10, right: 10, bottom: 10 };

            // 创建bitmap并加载图片
            this.createBitmap();
            if (this._src) {
                this.loadImage();
            }
        }

        /**
         * 获取宽度
         */
        get width() {
            return this._width;
        }

        /**
         * 设置宽度 - 触发重绘
         */
        set width(value) {
            if (this._width !== value) {
                this._width = value;
                // 🔑 只有在图片已加载且尺寸真正改变时才刷新，避免不必要的闪烁
                if (this.sourceBitmap && this.sourceBitmap.isReady && this.sourceBitmap.isReady()) {
                    this.refresh();
                }
                console.log('📏 UIImage: 设置宽度', value);
            }
        }

        /**
         * 获取高度
         */
        get height() {
            return this._height;
        }

        /**
         * 设置高度 - 触发重绘
         */
        set height(value) {
            if (this._height !== value) {
                this._height = value;
                // 🔑 只有在图片已加载且尺寸真正改变时才刷新，避免不必要的闪烁
                if (this.sourceBitmap && this.sourceBitmap.isReady && this.sourceBitmap.isReady()) {
                    this.refresh();
                }
                console.log('📏 UIImage: 设置高度', value);
            }
        }

        /**
         * 获取图片源路径
         */
        get src() {
            return this._src;
        }

        /**
         * 设置图片源路径 - 自动加载图片
         */
        set src(value) {
            if (this._src !== value) {
                console.log('🖼️ UIImage: 设置图片源', {
                    old: this._src,
                    new: value
                });

                this._src = value;

                // 自动加载新图片
                if (value) {
                    this.loadImage();
                } else {
                    // 如果设置为空，清除图片
                    this.clearImage();
                }
            }
        }

        /**
         * 🔑 兼容性：imagePath 属性（映射到 src）
         */
        get imagePath() {
            return this._src;
        }

        set imagePath(value) {
            this.src = value;
        }

        /**
         * 创建bitmap - 参考UIButton实现
         */
        createBitmap() {
            this.bitmap = new Bitmap(this.width, this.height);
            console.log('🎨 UIImage: 创建bitmap', this.width, 'x', this.height);
        }

        /**
         * 加载图片 - 参考UIButton的图片加载
         */
        loadImage() {
            if (typeof ImageManager !== 'undefined' && this._src) {
                console.log('📥 UIImage: 加载图片', this._src);
                this.sourceBitmap = ImageManager.loadBitmapFromUrl(this._src);
                this.sourceBitmap.addLoadListener(() => {
                    this.onImageLoaded();
                });
            }
        }

        /**
         * 图片加载完成回调
         */
        onImageLoaded() {
            console.log('✅ UIImage: 图片加载完成', this.sourceBitmap.width, 'x', this.sourceBitmap.height);

            const originalWidth = this.sourceBitmap.width;
            const originalHeight = this.sourceBitmap.height;

            // 🔧 区分不同情况的尺寸处理
            if (this.regions.length === 0) {
                // 情况1: 没有预设区域 - 自动设置为图片原始尺寸（新建或更换图片）
                console.log('📏 UIImage: 没有预设区域，设置为图片原始尺寸', {
                    old: `${this._width}x${this._height}`,
                    new: `${originalWidth}x${originalHeight}`
                });

                this._width = originalWidth;
                this._height = originalHeight;

                // 创建默认区域（整张图片）
                this.createDefaultRegion();

                // 通知ImageModel尺寸变化
                if (this.onLoadCallback) {
                    this.onLoadCallback(originalWidth, originalHeight, 1, 1);
                }
            } else {
                // 情况2: 有预设区域 - 保持当前尺寸（Clone或已有裁切）
                console.log('📏 UIImage: 有预设区域，保持当前尺寸', {
                    currentSize: `${this._width}x${this._height}`,
                    imageSize: `${originalWidth}x${originalHeight}`,
                    regions: this.regions.length
                });

                // 不改变尺寸，只通知ImageModel图片已加载
                if (this.onLoadCallback) {
                    this.onLoadCallback(this._width, this._height, 1, 1);
                }
            }

            // 刷新显示 - 参考UIButton
            this.refresh();
        }

        /**
         * 创建默认区域（整张图片）
         */
        createDefaultRegion() {
            if (this.sourceBitmap) {
                this.regions = [{
                    sx: 0,
                    sy: 0,
                    sw: this.sourceBitmap.width,
                    sh: this.sourceBitmap.height
                }];
                this.currentRegionIndex = 0;
                console.log('🔧 UIImage: 创建默认区域', this.regions[0]);
            }
        }

        /**
         * 刷新图片显示 - 参考UIButton的refresh实现
         */
        refresh() {
            if (!this.bitmap) {
                console.log('⚠️ UIImage: bitmap不存在，重新创建');
                this.createBitmap();
            }

            if (!this.sourceBitmap) {
                console.log('⚠️ UIImage: 源图片不存在');
                return;
            }

            if (!this.sourceBitmap.isReady || !this.sourceBitmap.isReady()) {
                console.log('⚠️ UIImage: 图片未准备好，等待加载');
                return;
            }

            // 🔑 检查bitmap尺寸是否需要重新创建
            const needsResize = !this.bitmap ||
                               this.bitmap.width !== this.width ||
                               this.bitmap.height !== this.height;

            if (needsResize) {
                // 只有在尺寸变化时才清空并重新创建
                if (this.bitmap) {
                    this.bitmap.clear();
                }
                this.bitmap = new Bitmap(this.width, this.height);
                console.log('🔧 UIImage: 重新创建bitmap', this.width, 'x', this.height);
            }

            console.log('🎨 UIImage: 刷新显示', this.width, 'x', this.height);
            this.setImageBitmap(this.sourceBitmap);
        }

        /**
         * 设置图片bitmap - 支持裁切功能
         */
        setImageBitmap(sourceBitmap) {
            if (!sourceBitmap || !sourceBitmap.canvas) {
                console.warn('UIImage: 无效的sourceBitmap');
                return;
            }

            // 确保bitmap存在且尺寸正确
            if (!this.bitmap || this.bitmap.width !== this.width || this.bitmap.height !== this.height) {
                this.bitmap = new Bitmap(this.width, this.height);
            }

            // 获取当前区域信息
            const region = this.getCurrentRegion();
            if (!region) {
                console.warn('UIImage: 没有可用的区域');
                return;
            }

            // 🔑 检查是否需要重新绘制（避免重复绘制相同内容）
            // 包含图片源信息，确保更换图片时能正确重绘
            const imageSource = sourceBitmap.url || sourceBitmap._url || this._src || 'unknown';
            const ninePatchKey = this.enableNinePatch ? `_np_${this.ninePatch.left}_${this.ninePatch.top}_${this.ninePatch.right}_${this.ninePatch.bottom}` : '';
            const currentRegionKey = `${imageSource}_${region.sx}_${region.sy}_${region.sw}_${region.sh}_${this.width}_${this.height}${ninePatchKey}`;

            if (this._lastRegionKey === currentRegionKey && this.bitmap._loadingState === 'loaded') {
                console.log('🔘 UIImage: 内容未变化，跳过重绘');
                return;
            }
            this._lastRegionKey = currentRegionKey;

            // 清除bitmap内容（只在需要重绘时清除）
            this.bitmap.clear();

            // 使用高质量的Canvas绘制 - 支持裁切和九宫格
            const context = this.bitmap.canvas.getContext('2d');
            if (context) {
                // 设置高质量缩放
                context.imageSmoothingEnabled = true;
                context.imageSmoothingQuality = 'high';

                // 🔑 根据是否启用九宫格选择绘制方式
                if (this.enableNinePatch) {
                    this.drawNinePatch(context, sourceBitmap, region);
                } else {
                    // 标准绘制：绘制指定区域到目标尺寸
                    context.drawImage(
                        sourceBitmap.canvas,
                        region.sx, region.sy, region.sw, region.sh,  // 源区域
                        0, 0, this.width, this.height                // 目标尺寸
                    );
                }

                // 标记bitmap已更新 - 使用RPG Maker MZ方法
                if (this.bitmap._baseTexture && this.bitmap._baseTexture.update) {
                    this.bitmap._baseTexture.update();
                } else if (this.bitmap._onLoad) {
                    this.bitmap._onLoad();
                }
            }

            // 🔧 设置bitmap的url属性，让属性面板能够检测到
            if (this.sourceBitmap && this.sourceBitmap.url) {
                // 使用Object.defineProperty来设置只读属性
                Object.defineProperty(this.bitmap, 'url', {
                    value: this.sourceBitmap.url,
                    writable: false,
                    enumerable: true,
                    configurable: true
                });
                this.bitmap._loadingState = 'loaded';
            }

            console.log('🎨 UIImage: 设置图片', `区域[${region.sx},${region.sy},${region.sw},${region.sh}]`, '->', this.width, 'x', this.height);
        }

        /**
         * 🔑 九宫格绘制方法
         * @param {CanvasRenderingContext2D} context 绘制上下文
         * @param {Bitmap} sourceBitmap 源图片
         * @param {Object} region 当前区域
         */
        drawNinePatch(context, sourceBitmap, region) {
            const { sx, sy, sw, sh } = region;
            const { left, top, right, bottom } = this.ninePatch;
            const targetWidth = this.width;
            const targetHeight = this.height;

            console.log('🔲 UIImage: 九宫格绘制', {
                region: `${sx},${sy},${sw},${sh}`,
                ninePatch: this.ninePatch,
                target: `${targetWidth}x${targetHeight}`
            });

            // 🔧 确保九宫格参数不超过区域尺寸
            const maxLeft = Math.min(left, sw - right - 1);
            const maxTop = Math.min(top, sh - bottom - 1);
            const maxRight = Math.min(right, sw - maxLeft - 1);
            const maxBottom = Math.min(bottom, sh - maxTop - 1);

            // 计算中间区域尺寸
            const centerWidth = sw - maxLeft - maxRight;
            const centerHeight = sh - maxTop - maxBottom;
            const targetCenterWidth = targetWidth - maxLeft - maxRight;
            const targetCenterHeight = targetHeight - maxTop - maxBottom;

            // 🎨 绘制九个区域

            // 1. 左上角 (固定尺寸)
            if (maxLeft > 0 && maxTop > 0) {
                context.drawImage(sourceBitmap.canvas,
                    sx, sy, maxLeft, maxTop,
                    0, 0, maxLeft, maxTop);
            }

            // 2. 上边 (水平拉伸)
            if (centerWidth > 0 && maxTop > 0 && targetCenterWidth > 0) {
                context.drawImage(sourceBitmap.canvas,
                    sx + maxLeft, sy, centerWidth, maxTop,
                    maxLeft, 0, targetCenterWidth, maxTop);
            }

            // 3. 右上角 (固定尺寸)
            if (maxRight > 0 && maxTop > 0) {
                context.drawImage(sourceBitmap.canvas,
                    sx + sw - maxRight, sy, maxRight, maxTop,
                    targetWidth - maxRight, 0, maxRight, maxTop);
            }

            // 4. 左边 (垂直拉伸)
            if (maxLeft > 0 && centerHeight > 0 && targetCenterHeight > 0) {
                context.drawImage(sourceBitmap.canvas,
                    sx, sy + maxTop, maxLeft, centerHeight,
                    0, maxTop, maxLeft, targetCenterHeight);
            }

            // 5. 中心 (双向拉伸)
            if (centerWidth > 0 && centerHeight > 0 && targetCenterWidth > 0 && targetCenterHeight > 0) {
                context.drawImage(sourceBitmap.canvas,
                    sx + maxLeft, sy + maxTop, centerWidth, centerHeight,
                    maxLeft, maxTop, targetCenterWidth, targetCenterHeight);
            }

            // 6. 右边 (垂直拉伸)
            if (maxRight > 0 && centerHeight > 0 && targetCenterHeight > 0) {
                context.drawImage(sourceBitmap.canvas,
                    sx + sw - maxRight, sy + maxTop, maxRight, centerHeight,
                    targetWidth - maxRight, maxTop, maxRight, targetCenterHeight);
            }

            // 7. 左下角 (固定尺寸)
            if (maxLeft > 0 && maxBottom > 0) {
                context.drawImage(sourceBitmap.canvas,
                    sx, sy + sh - maxBottom, maxLeft, maxBottom,
                    0, targetHeight - maxBottom, maxLeft, maxBottom);
            }

            // 8. 下边 (水平拉伸)
            if (centerWidth > 0 && maxBottom > 0 && targetCenterWidth > 0) {
                context.drawImage(sourceBitmap.canvas,
                    sx + maxLeft, sy + sh - maxBottom, centerWidth, maxBottom,
                    maxLeft, targetHeight - maxBottom, targetCenterWidth, maxBottom);
            }

            // 9. 右下角 (固定尺寸)
            if (maxRight > 0 && maxBottom > 0) {
                context.drawImage(sourceBitmap.canvas,
                    sx + sw - maxRight, sy + sh - maxBottom, maxRight, maxBottom,
                    targetWidth - maxRight, targetHeight - maxBottom, maxRight, maxBottom);
            }
        }

        /**
         * 更新显示尺寸 - 完全参考UIButton的updateDisplaySize实现
         */
        updateDisplaySize(newWidth, newHeight) {
            if (this._width !== newWidth || this._height !== newHeight) {
                this._width = newWidth;
                this._height = newHeight;

                // 重新绘制当前状态 - 参考UIButton
                this.refresh();

                console.log('📏 UIImage: 更新显示尺寸', newWidth, 'x', newHeight);
            }
        }

        /**
         * 设置尺寸 - 参考UIButton实现
         */
        setSize(width, height) {
            this.updateDisplaySize(width, height);
        }

        /**
         * 设置显示尺寸 - 兼容方法
         */
        setDisplaySize(width, height) {
            this.updateDisplaySize(width, height);
        }

        /**
         * 设置图片路径（兼容方法，推荐使用 src 属性）
         */
        setImagePath(path) {
            this.src = path;
        }

        /**
         * 🔑 设置九宫格启用状态
         * @param {boolean} enabled 是否启用九宫格
         */
        setNinePatchEnabled(enabled) {
            if (this.enableNinePatch !== enabled) {
                this.enableNinePatch = enabled;
                this.refresh(); // 重新绘制
                console.log('🔲 UIImage: 九宫格状态', enabled ? '启用' : '禁用');
            }
        }

        /**
         * 🔑 设置九宫格参数
         * @param {Object} ninePatch 九宫格参数 {left, top, right, bottom}
         */
        setNinePatch(ninePatch) {
            if (ninePatch && typeof ninePatch === 'object') {
                this.ninePatch = {
                    left: ninePatch.left || 0,
                    top: ninePatch.top || 0,
                    right: ninePatch.right || 0,
                    bottom: ninePatch.bottom || 0
                };

                // 如果九宫格已启用，重新绘制
                if (this.enableNinePatch) {
                    this.refresh();
                }

                console.log('🔲 UIImage: 设置九宫格参数', this.ninePatch);
            }
        }

        /**
         * 🔑 获取九宫格参数
         * @returns {Object} 九宫格参数
         */
        getNinePatch() {
            return { ...this.ninePatch };
        }

        /**
         * 清除图片
         */
        clearImage() {
            console.log('🗑️ UIImage: 清除图片');

            // 清除源图片
            this.sourceBitmap = null;

            // 清除当前显示的图片
            if (this.bitmap) {
                this.bitmap.clear();
            }

            // 重置尺寸为默认值
            this._width = 100;
            this._height = 100;

            // 刷新显示
            this.refresh();
        }

        /**
         * 设置角色 Face 图片
         * @param {string} faceName Face 文件名（不含扩展名）
         * @param {number} faceIndex Face 索引 (0-7)
         */
        setFace(faceName, faceIndex) {
            console.log('👤 UIImage: 设置 Face 图片', {
                faceName,
                faceIndex
            });

            if (!faceName) {
                console.warn('👤 UIImage: faceName 不能为空');
                return;
            }

            // 设置 face 图片路径
            this.src = `img/faces/${faceName}.png`;

            // 设置 face 裁切区域
            this.setFaceRegion(faceIndex || 0);
        }

        /**
         * 设置 Face 裁切区域
         * @param {number} faceIndex Face 索引 (0-7)
         */
        setFaceRegion(faceIndex) {
            const faceWidth = 144;   // RPG Maker MZ 标准 face 宽度
            const faceHeight = 144;  // RPG Maker MZ 标准 face 高度

            // 计算在 face 图片中的位置 (4列×2行布局)
            const col = faceIndex % 4;
            const row = Math.floor(faceIndex / 4);

            const region = {
                sx: col * faceWidth,
                sy: row * faceHeight,
                sw: faceWidth,
                sh: faceHeight,
                label: `Face ${faceIndex}`
            };

            console.log('👤 UIImage: 设置 Face 区域', {
                faceIndex,
                col,
                row,
                region
            });

            // 设置为单区域模式，显示指定的 face
            this.regions = [region];
            this.currentRegionIndex = 0;

            // 如果图片已加载，立即刷新显示
            if (this.sourceBitmap) {
                this.refresh();
            }
        }

        /**
         * 设置加载完成回调
         */
        setOnLoadCallback(callback) {
            this.onLoadCallback = callback;
        }

        // ==================== 裁切功能 ====================

        /**
         * 获取当前区域信息
         */
        getCurrentRegion() {
            return this.regions[this.currentRegionIndex] || null;
        }

        /**
         * 设置当前显示区域
         */
        setCurrentRegion(index) {
            if (index >= 0 && index < this.regions.length) {
                this.currentRegionIndex = index;
                this.refresh(); // 重新绘制
                console.log('🔧 UIImage: 切换到区域', index, this.regions[index]);
                return true;
            }
            return false;
        }

        /**
         * 生成网格区域
         */
        generateGridRegions(rows, cols) {
            if (!this.sourceBitmap) {
                console.warn('🖼️ UIImage: 图片未加载，无法生成网格区域');
                return;
            }

            this.gridRows = rows;
            this.gridCols = cols;
            this.regions = [];

            const cellWidth = Math.floor(this.sourceBitmap.width / cols);
            const cellHeight = Math.floor(this.sourceBitmap.height / rows);

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    this.regions.push({
                        sx: col * cellWidth,
                        sy: row * cellHeight,
                        sw: cellWidth,
                        sh: cellHeight
                    });
                }
            }

            this.currentRegionIndex = 0;
            console.log('🔧 UIImage: 生成网格区域', `${rows}x${cols}`, '共', this.regions.length, '个区域');

            // 重新绘制
            this.refresh();
        }

        /**
         * 添加自定义区域
         */
        addRegion(sx, sy, sw, sh) {
            this.regions.push({ sx, sy, sw, sh });
            console.log('🔧 UIImage: 添加区域', { sx, sy, sw, sh });
        }

        /**
         * 重置为默认区域（完整图片）
         */
        resetToDefaultRegion() {
            if (!this.sourceBitmap) {
                console.warn('🖼️ UIImage: 图片未加载，无法重置区域');
                return;
            }

            // 重置网格设置
            this.gridRows = 1;
            this.gridCols = 1;

            // 🔧 重置尺寸为图片原始尺寸
            const originalWidth = this.sourceBitmap.width;
            const originalHeight = this.sourceBitmap.height;

            console.log('📏 UIImage: 重置尺寸为图片原始尺寸', {
                old: `${this._width}x${this._height}`,
                new: `${originalWidth}x${originalHeight}`
            });

            this._width = originalWidth;
            this._height = originalHeight;

            // 创建默认区域（完整图片）
            this.regions = [{
                sx: 0,
                sy: 0,
                sw: originalWidth,
                sh: originalHeight
            }];
            this.currentRegionIndex = 0;

            // 重新绘制
            this.refresh();

            // 🔧 通知ImageModel尺寸已重置
            if (this.onLoadCallback) {
                this.onLoadCallback(originalWidth, originalHeight, 1, 1);
            }

            console.log('🔄 UIImage: 重置为默认区域', {
                size: `${originalWidth}x${originalHeight}`,
                regions: this.regions.length,
                gridSize: `${this.gridRows}x${this.gridCols}`
            });
        }

        /**
         * 是否为多区域裁切
         */
        isMultiRegion() {
            return this.regions.length > 1;
        }

        /**
         * 获取属性
         */
        getProperties() {
            return {
                // 基础属性
                x: this.x,
                y: this.y,
                width: this.width,
                height: this.height,
                // 图片属性
                src: this._src,
                // 裁切属性
                regions: this.regions,
                currentRegionIndex: this.currentRegionIndex,
                gridRows: this.gridRows,
                gridCols: this.gridCols,
                // 智能裁切属性
                smartCropData: this.smartCropData,

                // 🔑 九宫格属性
                enableNinePatch: this.enableNinePatch,
                ninePatch: this.ninePatch
            };
        }

        /**
         * 每帧更新
         */
        update() {
            super.update();

            // 🔑 执行更新脚本
            if (this.executeScript) {
                this.executeScript('onUpdate');
            }
        }

        /**
         * 销毁图片组件
         */
        destroy() {
            // 🔑 安全执行销毁脚本
            try {
                if (this.executeScript && !this._isDestroying) {
                    this._isDestroying = true; // 防止重复销毁
                    this.executeScript('onDestroy');
                }
            } catch (error) {
                console.warn('⚠️ UIImage: 销毁脚本执行失败', error);
            }

            // 清理资源
            if (this.sourceBitmap) {
                this.sourceBitmap.destroy();
                this.sourceBitmap = null;
            }

            super.destroy();
        }

        /**
         * 克隆当前 UIImage 对象
         * @param {Object} options 克隆选项
         * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
         * @param {number} options.offsetX 水平偏移量 (默认: 20)
         * @param {number} options.offsetY 垂直偏移量 (默认: 20)
         * @returns {UIImage} 克隆的 UIImage 对象
         */
        clone(options = {}) {
            console.log('🔄 UIImage: 开始克隆对象');

            const {
                offsetPosition = false,
                offsetX = 0,
                offsetY = 0
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = {
                // 基础属性
                width: this.width,
                height: this.height,
                visible: this.visible,

                // 🔑 UIComponent 属性（安全访问）
                name: this.name || '',
                enabled: this.enabled !== false,

                // 🔑 深度克隆组件脚本数组
                componentScripts: this.componentScripts ?
                    this.componentScripts.map(script => ({
                        id: script.id,
                        name: script.name,
                        type: script.type,
                        enabled: script.enabled,
                        code: script.code,
                        description: script.description
                    })) : [],

                // UIImage 特有属性
                src: this._src,
                scaleMode: this.scaleMode,
                preserveAspectRatio: this.preserveAspectRatio,
                regions: JSON.parse(JSON.stringify(this.regions)), // 深拷贝区域数据
                currentRegionIndex: this.currentRegionIndex,
                gridRows: this.gridRows,
                gridCols: this.gridCols,
                // 深拷贝智能裁切数据
                smartCropData: this.smartCropData ? JSON.parse(JSON.stringify(this.smartCropData)) : null,

                // 🔑 九宫格属性
                enableNinePatch: this.enableNinePatch,
                ninePatch: { ...this.ninePatch } // 深拷贝九宫格参数
            };

            // 2. 创建克隆对象
            const clonedImage = new UIImage(cloneProperties);

            // 🔧 调试：验证脚本是否被克隆
            // console.log('🔄 UIImage: 克隆验证', {
            //     原始组件名称: this.name,
            //     克隆组件名称: clonedImage.name,
            //     原始脚本数量: this.componentScripts ? this.componentScripts.length : 0,
            //     克隆脚本数量: clonedImage.componentScripts ? clonedImage.componentScripts.length : 0,
            //     原始脚本详情: this.componentScripts ? this.componentScripts.map(s => `${s.name}(${s.enabled ? '启用' : '禁用'})`) : [],
            //     克隆脚本详情: clonedImage.componentScripts ? clonedImage.componentScripts.map(s => `${s.name}(${s.enabled ? '启用' : '禁用'})`) : []
            // });

            // 3. 设置位置和变换属性
            clonedImage.x = this.x + (offsetPosition ? offsetX : 0);
            clonedImage.y = this.y + (offsetPosition ? offsetY : 0);
            clonedImage.scale.x = this.scale.x;
            clonedImage.scale.y = this.scale.y;
            clonedImage.rotation = this.rotation;
            clonedImage.alpha = this.alpha;
            clonedImage.anchor.x = this.anchor.x;
            clonedImage.anchor.y = this.anchor.y;
            clonedImage.pivot.x = this.pivot.x;
            clonedImage.pivot.y = this.pivot.y;
            clonedImage.skew.x = this.skew.x;
            clonedImage.skew.y = this.skew.y;
            clonedImage.zIndex = this.zIndex;

            // 4. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                    clonedImage.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                }
            }

            console.log('✅ UIImage: 克隆完成，包含', clonedChildren.length, '个子对象');
            return clonedImage;
        }
    }

    // 导出到全局
    window.UIImage = UIImage;
})()