// 导入窗口管理模块
mod window_manager;
// 导入项目管理模块
mod project_manager;
// 导入文件操作模块
mod file_operations;
// 导入图像分析模块
mod image_analyzer;
// 导入智能裁切模块
mod smart_crop;

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            window_manager::minimize_window,
            window_manager::maximize_window,
            window_manager::unmaximize_window,
            window_manager::close_window,
            window_manager::is_window_maximized,
            window_manager::toggle_maximize_window,
            window_manager::set_window_size,
            window_manager::set_window_position,
            window_manager::get_window_info,
            project_manager::get_project_config,
            project_manager::scan_project_scripts,
            project_manager::read_project_file,
            project_manager::read_project_binary_file,
            project_manager::validate_project,
            project_manager::get_recent_projects,
            project_manager::select_project_file,
            project_manager::get_project_directory_from_file,
            project_manager::save_current_project_info,
            project_manager::get_current_project_info,
            project_manager::clear_current_project_info,
            file_operations::save_operation_records,
            file_operations::load_operation_records,
            file_operations::get_operation_records_file_path,
            file_operations::save_plugin_file,
            smart_crop::analyze_image_smart_crop,
            smart_crop::analyze_image_from_path,
            smart_crop::get_default_crop_options,
            smart_crop::validate_image_data
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
