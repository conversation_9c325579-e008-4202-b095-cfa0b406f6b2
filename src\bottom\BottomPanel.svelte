<script lang="ts">
  /**
   * 底部面板组件
   * 使用 Tab 组件管理多个页面
   */

  import Tab from '../components/Tab.svelte';
  import type { TabItem } from '../components/Tab.svelte';
  import PrefabPanel from './prefab/PrefabPanel.svelte';

  // Tab 配置
  const tabs: TabItem[] = [
    { id: 'prefab', label: '预制体', icon: '🧩' },
    { id: 'resources', label: '资源', icon: '📁' },
    { id: 'console', label: '控制台', icon: '💻' },
    { id: 'output', label: '输出', icon: '📄' }
  ];

  let activeTab = $state('prefab');

  function handleTabChange(tabId: string) {
    activeTab = tabId;
  }
</script>

<div class="bottom-panel">
  <div class="content">
    <Tab
      {tabs}
      {activeTab}
      ontabchange={handleTabChange}
    >
      {#snippet children(currentTab: string)}
        {#if currentTab === 'prefab'}
          <PrefabPanel />
        {:else if currentTab === 'resources'}
          <div class="placeholder">资源管理功能开发中...</div>
        {:else if currentTab === 'console'}
          <div class="placeholder">控制台功能开发中...</div>
        {:else if currentTab === 'output'}
          <div class="placeholder">输出功能开发中...</div>
        {/if}
      {/snippet}
    </Tab>
  </div>
</div>

<style>
  .bottom-panel {
    height: 100%;
    background: var(--theme-surface, #ffffff);
    color: var(--theme-text-primary, #1a202c);
    display: flex;
    flex-direction: column;
    border-top: 1px solid var(--theme-border, #e2e8f0);
  }
  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--theme-text-secondary, #718096);
    font-size: 14px;
    background: var(--theme-surface-light, #f8f9fa);
  }

  /* 确保 Tab 组件占满剩余空间 */
  .content :global(.tab-container) {
    height: 100%;
  }

  .content :global(.tab-content) {
    flex: 1;
    overflow: hidden;
  }
</style>
