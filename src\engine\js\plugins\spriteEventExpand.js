/*:
----------------------->编辑器本地插件
 * @target MZ
 * @plugindesc Sprite Expand v2.0.0
 * <AUTHOR>
 * @version 2.0.0
 * @description 扩展 Sprite 功能，支持 bitmap 数据对象和事件处理
 *
 * @help spriteExpand.js
 *
 * 这个插件扩展了 Sprite 类的功能：
 * 1. 支持传入 bitmap 数据对象自动创建 Bitmap
 * 2. 支持传入 events 数据对象自动设置事件处理
 * 3. 支持组合数据对象 { bitmap: {...}, events: {...} }
 *
 * 使用方法：
 * // 方式1：只传入 bitmap 数据对象
 * const sprite1 = new Sprite(bitmapData);
 *
 * // 方式2：传入组合数据对象
 * const sprite2 = new Sprite({
 *   bitmap: bitmapData,
 *   events: {
 *     onClick: "SceneManager.goto(Scene_Map);",
 *     onHover: "this.tint = 0xcccccc;"
 *   }
 * });
 *
 * 版本历史：
 * 2.0.0 - 添加事件处理支持
 * 1.0.0 - 初始版本
 */

(() => {
    'use strict';
    //=============================================================================
    // 重写 Sprite 初始化，处理 bitmap 数据对象
    //=============================================================================
    const _Sprite_initialize = Sprite.prototype.initialize;

    Sprite.prototype.initialize = function (data) {
               //     // 初始化组件系统
            this._components = new Map();           // 组件映射表
            this._componentList = [];               // 组件列表（用于更新）
            this._componentUpdateEnabled = true;    // 是否启用组件更新
            this._componentDebugMode = false;       // 调试模式
        // 如果是真正的 Bitmap 对象（有 canvas 属性），直接调用原始方法
        console.log('🔧 Sprite.initialize 被调用，参数:', data);
        let bitmapData = null;
        let eventsData = null;

        // 检查是否是组合数据对象 { bitmap: {...}, events: {...} }
        if (data && typeof data === 'object' && !data._canvas) {
            if (data.bitmap || data.events) {
                console.log('✅ 检测到组合数据对象 (bitmap + events)');
                bitmapData = data.bitmap;
                eventsData = data.events;
            } else {
                // 单纯的 bitmap 数据对象
                console.log('✅ 检测到 bitmap 数据对象', data);
                bitmapData = data;
            }
        }

        // // 处理 bitmap 数据对象（能到这里的都是数据对象，不是真正的 Bitmap）
        let processedBitmap = bitmapData;

        if (bitmapData) {
            console.log('🔄 转换 bitmap 数据对象:', bitmapData);
            if (bitmapData.url && typeof bitmapData.url === 'string') {
                processedBitmap = ImageManager.loadBitmapFromUrl(bitmapData.url);
                //   console.log("============================================================.1  utl",processedBitmap)
            } else if (bitmapData && bitmapData.elements && Array.isArray(bitmapData.elements)) {
                console.log('从 elements 创建 bitmap');
                const newBitmap = new Bitmap(Graphics.width, Graphics.height);
                // 设置其他属性
                Object.assign(newBitmap, {
                    fontBold: bitmapData.fontBold || false,
                    fontFace: bitmapData.fontFace || 'GameFont',
                    fontItalic: bitmapData.fontItalic || false,
                    fontSize: bitmapData.fontSize || 28,
                    outlineColor: bitmapData.outlineColor || 'rgba(0, 0, 0, 0.5)',
                    outlineWidth: bitmapData.outlineWidth || 4,
                    textColor: bitmapData.textColor || '#ffffff',
                    _paintOpacity: bitmapData._paintOpacity || 255,
                    _smooth: bitmapData._smooth !== undefined ? bitmapData._smooth : true
                });
                // 处理 elements 数组，收集需要加载的图片
                const imagesToLoad = [];
                const processedElements = bitmapData.elements.map(element => {
                    if (element.type === 'image' && element.sourceUrl) {
                        const processedElement = { ...element };
                        const sourceBitmap = ImageManager.loadBitmapFromUrl(element.sourceUrl);
                        processedElement.source = sourceBitmap;

                        // 收集需要等待加载的图片
                        imagesToLoad.push(sourceBitmap);
                        return processedElement;
                    }
                    return element;
                });

                newBitmap.elements = processedElements;

                // 等待所有图片加载完成后重新绘制
                if (imagesToLoad.length > 0) {
                    let loadedCount = 0;
                    const checkAllLoaded = () => {
                        loadedCount++;
                        if (loadedCount >= imagesToLoad.length) {
                            // 所有图片都加载完成，重新绘制
                            console.log('所有图片加载完成，重新绘制 bitmap');
                            if (newBitmap.redrawing) {
                                newBitmap.redrawing();
                            }
                        }
                    };

                    // 为每个图片添加加载完成监听器
                    imagesToLoad.forEach(sourceBitmap => {
                        if (sourceBitmap.isReady && sourceBitmap.isReady()) {
                            // 图片已经加载完成
                            checkAllLoaded();
                        } else {
                            // 图片还在加载中，添加监听器
                            sourceBitmap.addLoadListener(checkAllLoaded);
                        }
                    });
                } else {
                    // 没有图片需要加载，直接重新绘制
                    if (newBitmap.redrawing) {
                        newBitmap.redrawing();
                    }
                }



                processedBitmap = newBitmap;
            } else {
                // 处理只有字体属性的 bitmap 数据对象
                console.log('处理字体属性 bitmap 数据对象:', bitmapData);
                const newBitmap = new Bitmap(Graphics.width, Graphics.height);

                // 应用字体相关属性
                if (bitmapData.fontBold !== undefined) newBitmap.fontBold = bitmapData.fontBold;
                if (bitmapData.fontFace) newBitmap.fontFace = bitmapData.fontFace;
                if (bitmapData.fontItalic !== undefined) newBitmap.fontItalic = bitmapData.fontItalic;
                if (bitmapData.fontSize !== undefined) newBitmap.fontSize = bitmapData.fontSize;
                if (bitmapData.outlineColor) newBitmap.outlineColor = bitmapData.outlineColor;
                if (bitmapData.outlineWidth !== undefined) newBitmap.outlineWidth = bitmapData.outlineWidth;
                if (bitmapData.textColor) newBitmap.textColor = bitmapData.textColor;
                if (bitmapData._paintOpacity !== undefined) newBitmap._paintOpacity = bitmapData._paintOpacity;
                if (bitmapData._smooth !== undefined) newBitmap._smooth = bitmapData._smooth;

                console.log('创建的新 bitmap 对象:', newBitmap);
                processedBitmap = newBitmap;
            }
        } else { processedBitmap = data }



        // //         // 处理事件数据对象
        //         if (eventsData) {
        //             console.log('🎯 设置 Sprite 事件:', eventsData);
        //             this.setupEvents(eventsData);
        //         }
        console.log("✅ 检测到 Bitmap 对象，直接使用:", processedBitmap);
        _Sprite_initialize.call(this, processedBitmap);
    };

    //=============================================================================
    // 为 Sprite 添加事件处理系统
    //=============================================================================



})();