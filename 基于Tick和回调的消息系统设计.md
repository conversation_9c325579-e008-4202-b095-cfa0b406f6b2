# 🎯 基于Tick和回调的消息系统设计

## 📋 **设计理念**

你的建议非常棒！使用Tick监听数据变化并通过回调机制处理不同类型的消息，这样设计更加高效和灵活。

## 🔧 **架构设计**

```
MessageInterceptor (拦截器)
├── 拦截 $gameMessage.add() 和 $gameMessage.setChoices()
├── 使用 requestAnimationFrame 实现高频Tick监听
├── 维护回调函数列表
└── 根据消息类型调用相应回调

UILayout组件 (在编辑器中)
├── onStart() 注册回调函数
├── 回调函数根据type处理不同消息类型
├── onDestroy() 清理回调函数
└── 专注于UI显示逻辑
```

## 🚀 **核心实现**

### **1. MessageInterceptor的Tick机制**

```javascript
class MessageInterceptor {
    constructor() {
        this.callbacks = [];  // 回调函数列表
        this.messageData = null;  // 当前消息数据
        this.choiceData = null;   // 当前选择数据
        this.startTick();  // 启动Tick监听
    }
    
    startTick() {
        const tick = () => {
            if (!this.isTickRunning) return;
            
            // 🔑 每帧检查数据变化
            if (this.messageData) {
                this.triggerCallbacks('message', this.messageData);
                this.messageData = null;  // 处理完后清除
            }
            
            if (this.choiceData) {
                this.triggerCallbacks('choice', this.choiceData);
                this.choiceData = null;  // 处理完后清除
            }
            
            requestAnimationFrame(tick);
        };
        
        requestAnimationFrame(tick);
    }
}
```

### **2. 回调机制**

```javascript
// 添加回调
addCallback(callback) {
    this.callbacks.push(callback);
}

// 触发所有回调
triggerCallbacks(type, data) {
    this.callbacks.forEach(callback => {
        try {
            callback(type, data);  // 🔑 传递类型和数据
        } catch (error) {
            console.error('回调执行失败:', error);
        }
    });
}
```

### **3. UILayout组件的回调处理**

```javascript
// 在onStart中注册回调
function onStart() {
    self.messageCallback = function(type, data) {
        switch (type) {
            case 'message':
                self.showCustomMessage(data);
                break;
            case 'choice':
                self.showCustomChoices(data);
                break;
            default:
                console.warn("未知的消息类型:", type);
        }
    };
    
    window.MessageInterceptor.addCallback(self.messageCallback);
}

// 在onDestroy中清理回调
function onDestroy() {
    if (window.MessageInterceptor && self.messageCallback) {
        window.MessageInterceptor.removeCallback(self.messageCallback);
    }
}
```

## 🎯 **消息类型处理**

### **消息类型定义**
- `'message'` - 普通文本消息
- `'choice'` - 选择项消息
- 可扩展其他类型：`'input'`、`'item'`等

### **数据结构**
```javascript
// message类型数据
{
    type: 'message',
    text: "消息内容",
    faceName: "角色头像",
    faceIndex: 0,
    background: 0,
    positionType: 2
}

// choice类型数据
{
    type: 'choice',
    choices: ["选项1", "选项2", "选项3"],
    defaultType: 0,
    cancelType: 2,
    background: 0
}
```

## 🔄 **工作流程**

### **消息显示流程**
```
1. 游戏调用: $gameMessage.add("Hello")
2. 拦截器: 保存到 this.messageData
3. Tick检测: 发现数据变化
4. 触发回调: callback('message', data)
5. UILayout: 根据type='message'显示消息
6. 清理数据: this.messageData = null
```

### **选择项流程**
```
1. 游戏调用: $gameMessage.setChoices(["A", "B"])
2. 拦截器: 保存到 this.choiceData
3. Tick检测: 发现数据变化
4. 触发回调: callback('choice', data)
5. UILayout: 根据type='choice'创建按钮
6. 清理数据: this.choiceData = null
```

## ✅ **优势分析**

### **1. 高性能**
- 使用 `requestAnimationFrame` 实现60FPS监听
- 数据处理完立即清除，避免重复处理
- 回调机制避免了全局变量轮询

### **2. 类型安全**
- 通过type参数明确消息类型
- 每种类型有独立的处理逻辑
- 易于扩展新的消息类型

### **3. 解耦设计**
- 拦截器只负责数据传递
- UI组件只负责显示逻辑
- 通过回调机制松耦合

### **4. 易于维护**
- 回调函数集中管理
- 自动清理机制防止内存泄漏
- 错误处理机制保证稳定性

## 🎨 **编辑器使用方法**

### **1. 创建消息容器**
1. 在Scene中添加UILayout组件
2. 设置name为"MessageContainer"
3. 配置位置、尺寸、背景图片

### **2. 编写组件脚本**
复制 `UILayout消息容器脚本示例.js` 中的代码到组件脚本面板

### **3. 自定义消息类型**
```javascript
// 扩展新的消息类型
self.messageCallback = function(type, data) {
    switch (type) {
        case 'message':
            self.showCustomMessage(data);
            break;
        case 'choice':
            self.showCustomChoices(data);
            break;
        case 'input':  // 🔑 新增类型
            self.showCustomInput(data);
            break;
        case 'item':   // 🔑 新增类型
            self.showCustomItemChoice(data);
            break;
    }
};
```

## 🚀 **使用示例**

### **基本使用**
```javascript
// 正常使用RPG Maker的消息系统
$gameMessage.add("这是自定义消息！");
$gameMessage.setChoices(["选项1", "选项2"], 0, 1);

// 会自动通过回调机制显示自定义UI
```

### **调试控制**
```javascript
// 禁用自定义UI
window.MessageInterceptor.isEnabled = false;

// 查看回调数量
console.log('回调数量:', window.MessageInterceptor.callbacks.length);

// 停止Tick监听
window.MessageInterceptor.stopTick();
```

## 🎯 **总结**

这种基于Tick和回调的设计完美实现了：

1. **高效监听**：60FPS的数据变化检测
2. **类型处理**：根据不同类型执行不同逻辑
3. **回调机制**：松耦合的事件驱动架构
4. **易于扩展**：新增消息类型只需添加case分支

你的建议让整个系统变得更加优雅和高效！
