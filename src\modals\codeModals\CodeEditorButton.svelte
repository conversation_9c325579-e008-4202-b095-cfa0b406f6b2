<script lang="ts">
  import { codeEditorManager } from '../../windows/codeEditor/codeEditorManager';

  export let title: string = '编辑代码';
  export let code: string = '';
  export let onSave: (code: string) => void;
  export let buttonText: string = '📝 编辑代码';
  export let buttonClass: string = '';
  export let disabled: boolean = false;

  async function handleClick() {
    if (disabled) return;

    console.log('🔧 CodeEditorButton点击:', {
      title,
      codeLength: code?.length || 0,
      hasOnSave: !!onSave
    });

    try {
      await codeEditorManager.openEditor({
        title,
        code,
        onSave: (savedCode) => {
          console.log('🔧 CodeEditorButton保存回调被调用:', {
            title,
            savedCodeLength: savedCode?.length || 0
          });
          onSave(savedCode);
        }
      });
    } catch (error) {
      console.error('❌ 打开代码编辑器失败:', error);
      alert('打开代码编辑器失败: ' + error);
    }
  }
</script>

<button 
  class="code-editor-button {buttonClass}" 
  class:disabled
  on:click={handleClick}
  {disabled}
>
  {buttonText}
</button>

<style>
  .code-editor-button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: var(--theme-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
  }
  
  .code-editor-button:hover:not(.disabled) {
    background: var(--theme-primary-dark, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }
  
  .code-editor-button:active:not(.disabled) {
    transform: translateY(0);
  }
  
  .code-editor-button.disabled {
    background: var(--theme-surface-light, #6b7280);
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  /* 变体样式 */
  .code-editor-button.secondary {
    background: var(--theme-surface-light, #6b7280);
    color: var(--theme-text, #ffffff);
  }
  
  .code-editor-button.secondary:hover:not(.disabled) {
    background: var(--theme-surface-lighter, #9ca3af);
  }
  
  .code-editor-button.small {
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .code-editor-button.large {
    padding: 12px 16px;
    font-size: 14px;
  }
</style>
