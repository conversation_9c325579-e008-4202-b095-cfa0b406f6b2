/**
 * 绑定规则服务
 * 处理字段类型检测和绑定规则管理
 */

import { ItemType, BINDING_RULES, COLOR_THEMES, type BindingRule } from '../types/containerTypes';

/**
 * 检测值的类型
 */
export function detectItemType(value: any): ItemType {
  if (value === null || value === undefined) {
    return ItemType.FIELD;
  }
  
  if (Array.isArray(value)) {
    return ItemType.ARRAY;
  }
  
  if (typeof value === 'object') {
    return ItemType.OBJECT;
  }
  
  // string, number, boolean 等基础类型
  return ItemType.FIELD;
}

/**
 * 获取绑定规则
 */
export function getBindingRule(type: ItemType): BindingRule {
  return BINDING_RULES[type];
}

/**
 * 获取类型颜色主题
 */
export function getColorTheme(type: ItemType) {
  const rule = getBindingRule(type);
  return COLOR_THEMES[rule.color as keyof typeof COLOR_THEMES];
}

/**
 * 检查组件是否可以绑定指定类型
 */
export function canBindToComponent(itemType: ItemType, componentType: string): boolean {
  const rule = getBindingRule(itemType);
  return rule.allowedComponents.includes(componentType);
}

/**
 * 获取类型的 CSS 类名
 */
export function getTypeClassName(type: ItemType): string {
  return `item-${type}`;
}

/**
 * 获取类型的图标
 */
export function getTypeIcon(type: ItemType): string {
  switch (type) {
    case ItemType.OBJECT:
      return '📦';
    case ItemType.ARRAY:
      return '📋';
    case ItemType.FIELD:
      return '🏷️';
    default:
      return '❓';
  }
}

/**
 * 格式化绑定提示文本
 */
export function formatBindingHint(type: ItemType): string {
  const rule = getBindingRule(type);
  const icon = getTypeIcon(type);
  return `${icon} ${rule.hint}`;
}
