// RPG Maker MZ API 类型定义

export interface RPGProperty {
  name: string;
  type: 'property';
  description: string;
  code: string;
}

export interface RPGMethod {
  name: string;
  type: 'method';
  description: string;
  code: string;
}

export interface RPGObject {
  name: string;
  description: string;
  category: 'data' | 'game' | 'manager';
  properties: RPGProperty[];
  methods: RPGMethod[];
}

export interface RPGExample {
  name: string;
  type: 'method';
  description: string;
  code: string;
}

// 兼容旧的接口名称
export type RPGItem = RPGProperty | RPGMethod;

// 场景常量
export const SCENE_CONSTANTS = {
  Scene_Title: 'Scene_Title',
  Scene_Map: 'Scene_Map',
  Scene_Menu: 'Scene_Menu',
  Scene_Item: 'Scene_Item',
  Scene_Skill: 'Scene_Skill',
  Scene_Equip: 'Scene_Equip',
  Scene_Status: 'Scene_Status',
  Scene_Options: 'Scene_Options',
  Scene_File: 'Scene_File',
  Scene_Save: 'Scene_Save',
  Scene_Load: 'Scene_Load',
  Scene_GameEnd: 'Scene_GameEnd',
  Scene_Shop: 'Scene_Shop',
  Scene_Name: 'Scene_Name',
  Scene_Debug: 'Scene_Debug',
  Scene_Battle: 'Scene_Battle',
  Scene_Gameover: 'Scene_Gameover'
} as const;

export type SceneType = typeof SCENE_CONSTANTS[keyof typeof SCENE_CONSTANTS];
