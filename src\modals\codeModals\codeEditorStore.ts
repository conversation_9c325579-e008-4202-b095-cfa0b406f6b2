import { writable } from 'svelte/store';

export interface CodeEditorState {
  isOpen: boolean;
  title: string;
  code: string;
  language: string;
  onSave?: (code: string) => void;
  onCancel?: () => void;
}

const initialState: CodeEditorState = {
  isOpen: false,
  title: '代码编辑器',
  code: '',
  language: 'javascript',
  onSave: undefined,
  onCancel: undefined
};

function createCodeEditorStore() {
  const { subscribe, set, update } = writable<CodeEditorState>(initialState);

  return {
    subscribe,
    
    /**
     * 打开代码编辑器
     */
    open: (options: {
      title?: string;
      code?: string;
      language?: string;
      onSave?: (code: string) => void;
      onCancel?: () => void;
    }) => {
      update(state => ({
        ...state,
        isOpen: true,
        title: options.title || '代码编辑器',
        code: options.code || '',
        language: options.language || 'javascript',
        onSave: options.onSave,
        onCancel: options.onCancel
      }));
    },
    
    /**
     * 保存代码并关闭编辑器
     */
    save: (code: string) => {
      update(state => {
        if (state.onSave) {
          state.onSave(code);
        }
        return {
          ...state,
          isOpen: false,
          code: '',
          onSave: undefined,
          onCancel: undefined
        };
      });
    },
    
    /**
     * 取消编辑并关闭编辑器
     */
    close: () => {
      update(state => {
        if (state.onCancel) {
          state.onCancel();
        }
        return {
          ...state,
          isOpen: false,
          code: '',
          onSave: undefined,
          onCancel: undefined
        };
      });
    },
    
    /**
     * 更新代码内容（不关闭编辑器）
     */
    updateCode: (code: string) => {
      update(state => ({
        ...state,
        code
      }));
    },
    
    /**
     * 重置状态
     */
    reset: () => {
      set(initialState);
    }
  };
}

export const codeEditorStore = createCodeEditorStore();

// 便捷函数：打开生命周期脚本编辑器
export function openLifecycleEditor(
  functionName: string,
  currentCode: string,
  onSave: (code: string) => void
) {
  codeEditorStore.open({
    title: `生命周期脚本 - ${functionName}`,
    code: currentCode,
    language: 'javascript',
    onSave
  });
}

// 便捷函数：打开数据事件脚本编辑器
export function openDataEventEditor(
  eventName: string,
  currentCode: string,
  onSave: (code: string) => void
) {
  codeEditorStore.open({
    title: `数据事件脚本 - ${eventName}`,
    code: currentCode,
    language: 'javascript',
    onSave
  });
}

// 便捷函数：打开交互事件脚本编辑器
export function openInteractionEditor(
  eventName: string,
  currentCode: string,
  onSave: (code: string) => void
) {
  codeEditorStore.open({
    title: `交互事件脚本 - ${eventName}`,
    code: currentCode,
    language: 'javascript',
    onSave
  });
}

// 便捷函数：打开自定义函数编辑器
export function openCustomFunctionEditor(
  functionName: string,
  currentCode: string,
  onSave: (code: string) => void
) {
  codeEditorStore.open({
    title: `自定义函数 - ${functionName}`,
    code: currentCode,
    language: 'javascript',
    onSave
  });
}
