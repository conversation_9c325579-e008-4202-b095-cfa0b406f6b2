<script lang="ts">
  import type { ButtonModel } from '../../../type/ui/buttonModel.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import { AccordionPanel } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';

  import DropTarget from '../../../components/drop/DropTarget.svelte';
  import type { BaseObjectModel } from '../../../type/baseObjectModel.svelte';

  let { model }: { model: ButtonModel } = $props();

  // 获取绑定状态信息
  let bindingInfo = $derived(() => {
    if (!model) return null;
    return model.getButtonInfo();
  });

  // 🔑 检查对象是否可以绑定到UIButton
  function canBindToButton(object: BaseObjectModel): boolean {
    // 检查是否为UIImage类型
    if (object.className !== 'UIImage') {
      console.warn('🚨 UIButton面板: 只能绑定UIImage类型的对象', {
        provided: object.className,
        expected: 'UIImage'
      });
      return false;
    }

    // 检查是否已经是Button的子对象
    if (!model.children.includes(object)) {
      console.warn('🚨 UIButton面板: 绑定对象必须是当前UIButton的子对象', {
        objectName: object.name || 'unnamed',
        buttonName: model.name || 'unnamed',
        isChild: false,
        hint: '请先将UIImage拖拽到UIButton下作为子对象，然后再进行绑定'
      });
      return false;
    }

    return true;
  }

  // 绑定处理函数
  function handleDefaultSpriteDrop(object: BaseObjectModel) {
    if (!model) return;
    console.log('🎯 尝试绑定默认状态精灵:', object.className);

    // � 检查是否可以绑定
    if (!canBindToButton(object)) {
      console.error('❌ 默认状态精灵绑定失败：不满足绑定条件');
      return;
    }

    // 🔑 绑定模型对象
    model.boundDefaultSprite = object;
    console.log('✅ 默认状态精灵绑定完成', {
      spriteName: object.name || 'unnamed',
      spriteType: object.className
    });
  }

  function handleHoverSpriteDrop(object: BaseObjectModel) {
    if (!model) return;
    console.log('🎯 尝试绑定悬停状态精灵:', object.className);

    // 🔑 检查是否可以绑定
    if (!canBindToButton(object)) {
      console.error('❌ 悬停状态精灵绑定失败：不满足绑定条件');
      return;
    }

    // 🔑 绑定模型对象
    model.boundHoverSprite = object;
    console.log('✅ 悬停状态精灵绑定完成', {
      spriteName: object.name || 'unnamed',
      spriteType: object.className
    });
  }

  function handlePressedSpriteDrop(object: BaseObjectModel) {
    if (!model) return;
    console.log('🎯 尝试绑定按下状态精灵:', object.className);

    // 🔑 检查是否可以绑定
    if (!canBindToButton(object)) {
      console.error('❌ 按下状态精灵绑定失败：不满足绑定条件');
      return;
    }

    // 🔑 绑定模型对象
    model.boundPressedSprite = object;
    console.log('✅ 按下状态精灵绑定完成', {
      spriteName: object.name || 'unnamed',
      spriteType: object.className
    });
  }

  // 解绑函数
  function unbindDefaultSprite() {
    if (!model) return;
    model.boundDefaultSprite = null;
    console.log('🔓 默认状态精灵绑定已解除，模型已更新');
  }

  function unbindHoverSprite() {
    if (!model) return;
    model.boundHoverSprite = null;
    console.log('🔓 悬停状态精灵绑定已解除，模型已更新');
  }

  function unbindPressedSprite() {
    if (!model) return;
    model.boundPressedSprite = null;
    console.log('🔓 按下状态精灵绑定已解除，模型已更新');
  }




</script>

<AccordionPanel title="🔘 UIButton 属性" expanded={true}>

  <!-- 🔑 子组件绑定状态 -->
  <div class="property-section">
    <h4>🔗 子组件绑定</h4>

    <!-- 默认状态精灵绑定 -->
    <div class="binding-item">
      <div class="binding-header">
        <span class="binding-label">默认状态精灵 (UIImage):</span>
        <span class="binding-status-badge {bindingInfo()?.bindings?.boundDefaultSprite ? 'bound' : 'unbound'}">
          {bindingInfo()?.bindings?.boundDefaultSprite ? '已绑定' : '未绑定'}
        </span>
        {#if bindingInfo()?.bindings?.boundDefaultSprite}
          <button class="unbind-btn" onclick={unbindDefaultSprite} title="解除绑定">✕</button>
        {/if}
      </div>

      {#if bindingInfo()?.bindings?.boundDefaultSprite}
        <div class="bound-object-info">
          已绑定: {bindingInfo()?.bindings?.boundDefaultSprite?.className || '未知对象'}
        </div>
      {:else}
        <DropTarget
          onDrop={handleDefaultSpriteDrop}
          placeholder="拖拽 UIImage 对象到此处绑定为默认状态"
          targetObject={model}
          fieldName="boundDefaultSprite"
          enableHistory={true}
          operationName="绑定按钮默认状态精灵"
        />
      {/if}
    </div>

    <!-- 悬停状态精灵绑定 -->
    <div class="binding-item">
      <div class="binding-header">
        <span class="binding-label">悬停状态精灵 (UIImage):</span>
        <span class="binding-status-badge {bindingInfo()?.bindings?.boundHoverSprite ? 'bound' : 'unbound'}">
          {bindingInfo()?.bindings?.boundHoverSprite ? '已绑定' : '未绑定'}
        </span>
        {#if bindingInfo()?.bindings?.boundHoverSprite}
          <button class="unbind-btn" onclick={unbindHoverSprite} title="解除绑定">✕</button>
        {/if}
      </div>

      {#if bindingInfo()?.bindings?.boundHoverSprite}
        <div class="bound-object-info">
          已绑定: {bindingInfo()?.bindings?.boundHoverSprite?.className || '未知对象'}
        </div>
      {:else}
        <DropTarget
          onDrop={handleHoverSpriteDrop}
          placeholder="拖拽 UIImage 对象到此处绑定为悬停状态"
          targetObject={model}
          fieldName="boundHoverSprite"
          enableHistory={true}
          operationName="绑定按钮悬停状态精灵"
        />
      {/if}
    </div>

    <!-- 按下状态精灵绑定 -->
    <div class="binding-item">
      <div class="binding-header">
        <span class="binding-label">按下状态精灵 (UIImage):</span>
        <span class="binding-status-badge {bindingInfo()?.bindings?.boundPressedSprite ? 'bound' : 'unbound'}">
          {bindingInfo()?.bindings?.boundPressedSprite ? '已绑定' : '未绑定'}
        </span>
        {#if bindingInfo()?.bindings?.boundPressedSprite}
          <button class="unbind-btn" onclick={unbindPressedSprite} title="解除绑定">✕</button>
        {/if}
      </div>

      {#if bindingInfo()?.bindings?.boundPressedSprite}
        <div class="bound-object-info">
          已绑定: {bindingInfo()?.bindings?.boundPressedSprite?.className || '未知对象'}
        </div>
      {:else}
        <DropTarget
          onDrop={handlePressedSpriteDrop}
          placeholder="拖拽 UIImage 对象到此处绑定为按下状态"
          targetObject={model}
          fieldName="boundPressedSprite"
          enableHistory={true}
          operationName="绑定按钮按下状态精灵"
        />
      {/if}
    </div>



    <div class="binding-info">
      <p class="binding-hint">
        💡 绑定流程：<br>
        1️⃣ 先将 UIImage 拖拽到 UIButton 下作为子对象<br>
        2️⃣ 再将该 UIImage 拖拽到上方绑定区域进行状态绑定
      </p>
      <p class="binding-warning">
        ⚠️ 只能绑定当前 UIButton 的子对象，不能绑定外部对象
      </p>
    </div>
  </div>

  <!-- 状态配置 -->
  <div class="property-section">
    <h4>⚙️ 状态配置：</h4>
    <div class="property-row">
      <Checkbox bind:checked={model.enabled} label="启用按钮" />
    </div>
    <div class="property-row">
      <Checkbox bind:checked={model.executeEventsInEditor} label="🔧 编辑器中执行事件（调试用）" />
    </div>
  </div>

  <!-- 🔑 脚本管理将由统一的脚本面板处理 -->

  <!-- 🔑 脚本管理将由统一的脚本面板处理 -->
</AccordionPanel>



<style>
  .property-section {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--theme-border-light, #f1f5f9);
  }

  .property-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .property-section h4 {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: var(--theme-text, #1a202c);
    font-weight: 600;
  }

  .property-row {
    margin-bottom: 8px;
  }







  /* 🔑 绑定状态样式 */
  .binding-item {
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
  }

  .binding-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    font-size: 10px;
  }

  .bound-object-info {
    font-size: 9px;
    color: var(--theme-text-secondary, #666);
    padding: 4px 6px;
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 3px;
    border: 1px solid var(--theme-border-light, #e9ecef);
  }

  .unbind-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 2px;
    padding: 2px 4px;
    font-size: 8px;
    cursor: pointer;
    transition: background-color 0.15s ease;
  }

  .unbind-btn:hover {
    background: #c82333;
  }

  .binding-label {
    font-weight: 500;
    color: var(--theme-text, #1a202c);
  }

  .binding-status-badge {
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 9px;
    font-weight: 500;
    text-transform: uppercase;
  }

  .binding-status-badge.bound {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .binding-status-badge.unbound {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  .binding-info {
    margin-top: 8px;
  }

  .binding-hint {
    font-size: 9px;
    color: var(--theme-text-secondary, #718096);
    margin: 0 0 6px 0;
    line-height: 1.4;
    font-style: italic;
  }

  .binding-warning {
    font-size: 9px;
    color: #dc3545;
    margin: 0;
    line-height: 1.4;
    font-weight: 500;
    background: #f8d7da;
    padding: 4px 6px;
    border-radius: 3px;
    border: 1px solid #f5c6cb;
  }
</style>
