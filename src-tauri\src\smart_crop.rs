use crate::image_analyzer::{ImageAnalyzer, CropOptions, SmartCropResponse};
use base64::{engine::general_purpose, Engine as _};
use serde::{Deserialize, Serialize};
use tauri::command;

/// 智能裁切请求
#[derive(Debug, Serialize, Deserialize)]
pub struct SmartCropRequest {
    pub image_data: String, // base64编码的图像数据
    pub options: Option<CropOptions>,
}

/// 智能裁切命令
#[command]
pub async fn analyze_image_smart_crop(request: SmartCropRequest) -> Result<SmartCropResponse, String> {
    println!("🔍 开始智能裁切分析...");
    
    // 解码base64图像数据
    let image_bytes = general_purpose::STANDARD
        .decode(&request.image_data)
        .map_err(|e| format!("Base64解码失败: {}", e))?;
    
    // 加载图像
    let image = image::load_from_memory(&image_bytes)
        .map_err(|e| format!("图像加载失败: {}", e))?;
    
    println!("✅ 图像加载成功，尺寸: {}x{}", image.width(), image.height());
    
    // 使用提供的选项或默认选项
    let options = request.options.unwrap_or_default();
    
    // 创建分析器并分析图像
    let analyzer = ImageAnalyzer::new(options);
    let result = analyzer.analyze_image(&image);
    
    println!("🎯 分析完成，检测到 {} 个UI元素", result.elements.len());
    
    Ok(result)
}

/// 从文件路径分析图像
#[command]
pub async fn analyze_image_from_path(file_path: String, options: Option<CropOptions>) -> Result<SmartCropResponse, String> {
    println!("🔍 从文件分析图像: {}", file_path);
    
    // 加载图像文件
    let image = image::open(&file_path)
        .map_err(|e| format!("无法打开图像文件 {}: {}", file_path, e))?;
    
    println!("✅ 图像文件加载成功，尺寸: {}x{}", image.width(), image.height());
    
    // 使用提供的选项或默认选项
    let options = options.unwrap_or_default();
    
    // 创建分析器并分析图像
    let analyzer = ImageAnalyzer::new(options);
    let result = analyzer.analyze_image(&image);
    
    println!("🎯 文件分析完成，检测到 {} 个UI元素", result.elements.len());
    
    Ok(result)
}

/// 获取默认裁切选项
#[command]
pub async fn get_default_crop_options() -> CropOptions {
    CropOptions::default()
}

/// 验证图像数据
#[command]
pub async fn validate_image_data(image_data: String) -> Result<ImageInfo, String> {
    // 解码base64图像数据
    let image_bytes = general_purpose::STANDARD
        .decode(&image_data)
        .map_err(|e| format!("Base64解码失败: {}", e))?;
    
    // 加载图像获取基本信息
    let image = image::load_from_memory(&image_bytes)
        .map_err(|e| format!("图像加载失败: {}", e))?;
    
    Ok(ImageInfo {
        width: image.width(),
        height: image.height(),
        format: detect_image_format(&image_bytes),
        size_bytes: image_bytes.len(),
    })
}

/// 图像信息
#[derive(Debug, Serialize, Deserialize)]
pub struct ImageInfo {
    pub width: u32,
    pub height: u32,
    pub format: String,
    pub size_bytes: usize,
}

/// 检测图像格式
fn detect_image_format(bytes: &[u8]) -> String {
    if bytes.len() < 8 {
        return "unknown".to_string();
    }
    
    // PNG signature
    if bytes[0..8] == [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A] {
        return "PNG".to_string();
    }
    
    // JPEG signature
    if bytes[0..2] == [0xFF, 0xD8] {
        return "JPEG".to_string();
    }
    
    // GIF signature
    if bytes[0..6] == [0x47, 0x49, 0x46, 0x38, 0x37, 0x61] || 
       bytes[0..6] == [0x47, 0x49, 0x46, 0x38, 0x39, 0x61] {
        return "GIF".to_string();
    }
    
    // WebP signature
    if bytes.len() >= 12 && 
       bytes[0..4] == [0x52, 0x49, 0x46, 0x46] && 
       bytes[8..12] == [0x57, 0x45, 0x42, 0x50] {
        return "WebP".to_string();
    }
    
    "unknown".to_string()
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_detect_image_format() {
        // PNG signature
        let png_bytes = vec![0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A];
        assert_eq!(detect_image_format(&png_bytes), "PNG");
        
        // JPEG signature
        let jpeg_bytes = vec![0xFF, 0xD8, 0xFF, 0xE0];
        assert_eq!(detect_image_format(&jpeg_bytes), "JPEG");
        
        // Unknown format
        let unknown_bytes = vec![0x00, 0x01, 0x02, 0x03];
        assert_eq!(detect_image_format(&unknown_bytes), "unknown");
    }
    
    #[test]
    fn test_get_default_crop_options() {
        let options = CropOptions::default();
        assert_eq!(options.min_width, 16);
        assert_eq!(options.min_height, 16);
        assert_eq!(options.alpha_tolerance, 10);
    }
}
