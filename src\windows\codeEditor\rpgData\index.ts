// RPG Maker MZ API 参考 - 主入口文件

import type { RPGObject, RPGExample } from './types';
import { SCENE_CONSTANTS } from './types';
import { dataObjects } from './dataObjects';
import { gameObjects } from './gameObjects';
import { managerObjects } from './managers';
import { coreClasses } from './coreClasses';
import { basicExamples } from './examples/basic';
import { advancedExamples } from './examples/advanced';
import { utilityExamples } from './examples/utils';
import { objectExamples } from './objectExamples';

// 场景常量对象
const sceneConstants: RPGObject = {
  name: '场景常量',
  description: '所有可用的场景类常量',
  category: 'manager',
  properties: Object.entries(SCENE_CONSTANTS).map(([key, value]) => ({
    name: key,
    type: 'property' as const,
    description: `${key}场景`,
    code: value
  })),
  methods: []
};

// 合并所有对象
export const allObjects: RPGObject[] = [
  ...dataObjects,
  ...gameObjects,
  ...coreClasses,
  ...managerObjects,
  sceneConstants
];

// 合并所有示例
export const allExamples: RPGExample[] = [
  ...basicExamples,
  ...advancedExamples,
  ...utilityExamples
];

// 按类别分组的对象
export const objectsByCategory = {
  data: allObjects.filter(obj => obj.category === 'data'),
  game: allObjects.filter(obj => obj.category === 'game'),
  manager: allObjects.filter(obj => obj.category === 'manager')
};

// 导出类型和对象示例
export * from './types';
export { objectExamples };

// 默认导出（保持向后兼容）
export default {
  dataObjects: objectsByCategory.data,
  gameObjects: objectsByCategory.game,
  managerObjects: objectsByCategory.manager,
  examples: allExamples,
  objectExamples
};

// 搜索功能
export function searchObjects(query: string): RPGObject[] {
  const lowerQuery = query.toLowerCase();
  return allObjects.filter(obj =>
    obj.name.toLowerCase().includes(lowerQuery) ||
    obj.description.toLowerCase().includes(lowerQuery) ||
    obj.properties.some(prop =>
      prop.name.toLowerCase().includes(lowerQuery) ||
      prop.description.toLowerCase().includes(lowerQuery)
    ) ||
    obj.methods.some(method =>
      method.name.toLowerCase().includes(lowerQuery) ||
      method.description.toLowerCase().includes(lowerQuery)
    )
  );
}

// 搜索示例
export function searchExamples(query: string): RPGExample[] {
  const lowerQuery = query.toLowerCase();
  return allExamples.filter(example =>
    example.name.toLowerCase().includes(lowerQuery) ||
    example.description.toLowerCase().includes(lowerQuery) ||
    example.code.toLowerCase().includes(lowerQuery)
  );
}

// 获取对象的所有方法和属性
export function getObjectMembers(objectName: string) {
  const obj = allObjects.find(o => o.name === objectName);
  if (!obj) return null;

  return {
    properties: obj.properties,
    methods: obj.methods,
    all: [...obj.properties, ...obj.methods]
  };
}

// 统计信息
export const stats = {
  totalObjects: allObjects.length,
  totalExamples: allExamples.length,
  totalProperties: allObjects.reduce((sum, obj) => sum + obj.properties.length, 0),
  totalMethods: allObjects.reduce((sum, obj) => sum + obj.methods.length, 0),
  byCategory: {
    data: objectsByCategory.data.length,
    game: objectsByCategory.game.length,
    manager: objectsByCategory.manager.length
  }
};
