/**
 * 所有场景数据流的统一导出
 */

import { SceneTitleDataFlow } from './SceneTitle';
import { SceneMapDataFlow } from './SceneMap';
import { SceneMenuDataFlow } from './SceneMenu';
import { SceneOptionsDataFlow } from './SceneOptions';
import type { SceneDataFlow } from '../types';

/**
 * 场景数据流映射表
 */
export const sceneDataFlows: Record<string, SceneDataFlow> = {
  'Scene_Title': SceneTitleDataFlow,
  'Scene_Map': SceneMapDataFlow,
  'Scene_Menu': SceneMenuDataFlow,
  'Scene_Options': SceneOptionsDataFlow,
};

/**
 * 根据场景名称获取数据流
 */
export function getSceneDataFlow(sceneName: string): SceneDataFlow | null {
  return sceneDataFlows[sceneName] || null;
}

/**
 * 获取所有支持的场景名称
 */
export function getSupportedScenes(): string[] {
  return Object.keys(sceneDataFlows);
}

// 重新导出
export { SceneTitleDataFlow, SceneMapDataFlow, SceneMenuDataFlow, SceneOptionsDataFlow };
