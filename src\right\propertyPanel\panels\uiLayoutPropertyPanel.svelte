<script lang="ts">
    import { PropertyContainer, AccordionPanel } from '../../../components/accordionPanel';
    import Checkbox from '../../../components/Checkbox.svelte';
    import SafeInput from '../../../components/SafeInput.svelte';
    import Select from '../../../components/Select.svelte';
    import Label from '../../../components/Label.svelte';
    import { CodeEditorButton } from '../../../modals/codeModals';
    import type { LayoutModel } from '../../../type/ui/layoutModel.svelte';

    interface Props {
        model: LayoutModel;
    }

    let { model }: Props = $props();

    // 布局类型选项
    const layoutTypeOptions = [
        { value: 'vertical', label: '垂直布局' },
        { value: 'horizontal', label: '水平布局' },
        { value: 'grid', label: '网格布局' }
    ];

    // 是否显示网格特定选项
    let showGridOptions = $derived(model?.layoutType === 'grid');

    // 展开状态
    let isExpanded = $state(true);

    // 🔑 数据绑定状态 - 直接使用模型的响应式字段，无需中间变量

    // 脚本编辑处理函数
    function handleLifecycleEdit(functionName: string, newCode: string) {
        console.log(`🔧 UILayoutPropertyPanel: handleLifecycleEdit被调用`, {
            functionName,
            newCode,
            codeLength: newCode?.length || 0
        });

        // 确保componentScript对象存在
        if (!model.componentScript) {
            model.componentScript = {
                lifecycle: {},
                dataEvents: {},
                interactionEvents: {},
                customFunctions: {},
                variables: {}
            };
        }
        if (!model.componentScript.lifecycle) {
            model.componentScript.lifecycle = {};
        }

        // 更新脚本代码
        model.componentScript.lifecycle[functionName] = newCode;
        console.log(`🔧 UILayoutPropertyPanel: ${functionName} 脚本已更新`, newCode);

        // 🔑 清除原始对象的脚本初始化状态，强制重新初始化
        const originalObject = model.getOriginalObject();
        if (originalObject && originalObject._lifecycleInitialized) {
            console.log(`🔧 UILayoutPropertyPanel: 清除${functionName}的初始化状态，强制重新初始化`);
            delete originalObject._lifecycleInitialized[functionName];
        }
    }

    // 数据事件脚本编辑处理函数
    function handleDataEventEdit(functionName: string, newCode: string) {
        console.log(`🔧 UILayoutPropertyPanel: handleDataEventEdit被调用`, {
            functionName,
            newCode,
            codeLength: newCode?.length || 0
        });

        // 确保componentScript对象存在
        if (!model.componentScript) {
            model.componentScript = {
                lifecycle: {},
                dataEvents: {},
                interactionEvents: {},
                customFunctions: {},
                variables: {}
            };
        }
        if (!model.componentScript.dataEvents) {
            model.componentScript.dataEvents = {};
        }

        // 更新脚本代码
        model.componentScript.dataEvents[functionName] = newCode;
        console.log(`🔧 UILayoutPropertyPanel: ${functionName} 数据事件脚本已更新`, newCode);

        // 🔑 同步到原始对象
        const originalObject = model.getOriginalObject();
        if (originalObject) {
            if (!originalObject.componentScript) {
                originalObject.componentScript = {
                    lifecycle: {},
                    dataEvents: {},
                    interactionEvents: {},
                    customFunctions: {},
                    variables: {}
                };
            }
            if (!originalObject.componentScript.dataEvents) {
                originalObject.componentScript.dataEvents = {};
            }
            originalObject.componentScript.dataEvents[functionName] = newCode;
            console.log(`🔧 UILayoutPropertyPanel: ${functionName} 数据事件脚本已同步到原始对象`);
        }
    }







    // 获取布局状态信息
    function getLayoutInfo(): string {
        if (!model) return '未选择布局';

        const typeLabel = layoutTypeOptions.find(opt => opt.value === model.layoutType)?.label || '未知类型';
        const childrenCount = model.children?.length || 0;

        return `${typeLabel} | 子对象: ${childrenCount} 个 | 间距: ${model.spacing}px`;
    }


</script>

{#if model}
<AccordionPanel title="📐 布局属性" bind:expanded={isExpanded}>

    <!-- 布局状态信息 -->
    <div class="layout-status">
        <Label text={getLayoutInfo()} />
    </div>



    <!-- 布局类型 -->
<PropertyContainer>
    <Label text="布局类型:" />
    <Select
        options={layoutTypeOptions}
        bind:value={model.layoutType}
    />
</PropertyContainer>

<!-- 间距设置 -->
<PropertyContainer>
    <Label text="间距:" />
    <SafeInput
        value={model.spacing.toString()}
        type="number"
        onchange={(e: any) => {
            const newValue = parseInt(e.target.value) || 0;
            console.log('📐 LayoutPropertyPanel: 间距变更', model.spacing, '→', newValue);
            model.spacing = newValue;
        }}
    />
</PropertyContainer>

<!-- 内边距 -->
<PropertyContainer>
    <Label text="内边距:" />
    <SafeInput
        value={model.padding.toString()}
        type="number"
        onchange={(e: any) => model.padding = parseInt(e.target.value) || 0}
    />
</PropertyContainer>

<!-- 网格布局特定设置 -->
{#if showGridOptions}
    <PropertyContainer>
        <Label text="网格列数:" />
        <SafeInput
            value={model.columns.toString()}
            type="number"
            onchange={(e: any) => model.columns = parseInt(e.target.value) || 1}
        />
    </PropertyContainer>
{/if}

<!-- 自动更新 -->
<PropertyContainer>
    <Label text="自动更新:" />
    <Checkbox bind:checked={model.autoUpdate} />
</PropertyContainer>

<!-- 🔑 滚动设置 -->
<PropertyContainer>
    <Label text="启用滚动:" />
    <Checkbox bind:checked={model.scrollEnabled} />
</PropertyContainer>

<PropertyContainer>
    <Label text="滚动速度:" />
    <SafeInput
        type="number"
        bind:value={model.scrollSpeed}
        min="1"
        max="200"
        step="1"
        class="number-input"
    />
</PropertyContainer>
</AccordionPanel>
{/if}

<style>
    .layout-status {
        background: var(--theme-surface);
        border: 1px solid var(--theme-border);
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 12px;
        color: var(--theme-text-secondary);
        font-size: 12px;
    }
</style>