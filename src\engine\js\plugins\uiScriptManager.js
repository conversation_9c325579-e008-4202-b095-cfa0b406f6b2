/**
 * UIScriptManager - 统一的脚本管理器
 * 专门处理对象的脚本功能，与UIComponent分离
 */

(() => {
    'use strict';

    window.UIScriptManager = {

        /**
         * 为对象添加脚本功能
         */
        applyToObject(obj, properties = {}) {
            console.log(`🔧 UIScriptManager: 为${obj.constructor.name}添加脚本功能`);

            // 获取组件类型
            const componentType = obj.uiComponentType || obj.constructor.name;

            // 添加脚本数组
            obj.componentScripts = properties.componentScripts || this.createDefaultScripts(componentType);

            // 添加脚本管理方法
            this.addScriptMethods(obj);

            console.log(`✅ UIScriptManager: ${obj.constructor.name}脚本功能已添加`);

            return obj;
        },

        /**
         * 创建默认脚本数组
         */
        createDefaultScripts(componentType = 'default') {
            return [
                {
                    id: 'default_script',
                    name: '默认脚本',
                    enabled: true,
                    code: this.generateScriptTemplate(componentType),
                    description: '默认脚本，包含常用方法模板'
                }
            ];
        },

        /**
         * 根据组件类型生成脚本模板
         */
        generateScriptTemplate(componentType) {
            // 基础生命周期模板
            let template = `/**
 * 🚀 onStart - 对象启动生命周期
 * 触发时机: 对象创建并添加到父容器时自动触发
 * 作用: 初始化对象状态、设置默认值、绑定数据等
 * 配合方法: 无需手动调用，系统自动触发
 */
function onStart() {
  console.log("对象启动:", self.name || "unnamed");
  // 在这里添加初始化逻辑
  // 例如: 设置初始文本、绑定数据源、初始化变量等
}

/**
 * 🔄 onUpdate - 每帧更新生命周期
 * 触发时机: 每帧自动触发（约60FPS），只要对象存在就会持续调用
 * 作用: 实现动画、实时数据更新、状态检查等
 * 配合方法: 无需手动调用，系统自动触发
 * 注意: 避免在此方法中执行耗时操作
 */
// function onUpdate() {
//   console.log("每帧更新:", self.name);
//   // 在这里添加每帧更新逻辑
//   // 例如: 动画更新、实时数据显示、状态检查等
// }

/**
 * 📝 onFieldUpdate - 字段更新生命周期
 * 触发时机: 调用 updateFieldsToChildren(ctx) 或 onFieldUpdate(ctx) 时触发
 * 作用: 响应数据变化、更新UI显示、同步状态等
 * 配合方法: parentObject.updateFieldsToChildren(ctx) 或 object.onFieldUpdate(ctx)
 * 参数: ctx - 包含字段更新上下文信息的对象
 */
// function onFieldUpdate(ctx) {
//   console.log("字段更新:", self.name, ctx);
//   // 在这里添加字段更新时的处理逻辑
//   // ctx 包含: { field, oldValue, newValue, source, timestamp 等 }
//   // 例如: 根据 ctx.field 判断更新哪个属性
// }

/**
 * 🗑️ onDestroy - 对象销毁生命周期
 * 触发时机: 对象从父容器移除时自动触发
 * 作用: 清理资源、保存数据、解除绑定等
 * 配合方法: 无需手动调用，系统自动触发
 */
// function onDestroy() {
//   console.log("对象销毁:", self.name);
//   // 在这里添加清理逻辑
//   // 例如: 清理定时器、保存状态、解除事件绑定等
// }`;

            // 根据组件类型添加特定事件
            if (componentType === 'Button') {
                template += this.getButtonEvents();
            } else if (componentType === 'Switch' || componentType === 'Slider') {
                template += this.getChangeEvent(componentType);
            } else if (componentType === 'UIInput') {
                template += this.getInputEvents();
            }

            return template;
        },

        /**
         * 获取Button组件的交互事件模板
         */
        getButtonEvents() {
            return `

/**
 * 👆 onClick - 单击交互事件
 * 触发时机: 用户单击对象时触发
 * 作用: 处理点击逻辑、切换状态、执行操作等
 * 配合方法: 无需手动调用，用户交互自动触发
 */
// function onClick() {
//   console.log("对象被点击:", self.name);
//   // 在这里添加点击逻辑
//   // 例如: 切换状态、打开菜单、执行命令等
// }

/**
 * 👆👆 onDoubleClick - 双击交互事件
 * 触发时机: 用户双击对象时触发
 * 作用: 处理双击特殊逻辑、快捷操作等
 * 配合方法: 无需手动调用，用户交互自动触发
 */
// function onDoubleClick() {
//   console.log("对象被双击:", self.name);
//   // 在这里添加双击逻辑
//   // 例如: 快速编辑、全屏显示、快捷操作等
// }

/**
 * 🖱️ onHover - 鼠标悬停事件
 * 触发时机: 鼠标进入对象区域时触发
 * 作用: 显示提示信息、改变外观、预览效果等
 * 配合方法: 通常与 onHoverOut 配对使用
 */
// function onHover() {
//   console.log("鼠标悬停:", self.name);
//   // 在这里添加悬停逻辑
//   // 例如: 显示工具提示、改变颜色、显示预览等
// }

/**
 * 🖱️ onHoverOut - 鼠标离开事件
 * 触发时机: 鼠标离开对象区域时触发
 * 作用: 隐藏提示信息、恢复外观、清理预览等
 * 配合方法: 通常与 onHover 配对使用
 */
// function onHoverOut() {
//   console.log("鼠标离开:", self.name);
//   // 在这里添加鼠标离开逻辑
//   // 例如: 隐藏工具提示、恢复颜色、清理预览等
// }

/**
 * ⬇️ onPress - 按下事件
 * 触发时机: 鼠标按下（但未释放）时触发
 * 作用: 开始拖拽、显示按下效果、记录按下状态等
 * 配合方法: 通常与 onRelease 配对使用
 */
// function onPress() {
//   console.log("对象按下:", self.name);
//   // 在这里添加按下逻辑
//   // 例如: 开始拖拽、改变外观、记录状态等
// }

/**
 * ⬆️ onRelease - 释放事件
 * 触发时机: 鼠标释放时触发
 * 作用: 结束拖拽、恢复外观、完成操作等
 * 配合方法: 通常与 onRelease 配对使用
 */
// function onRelease() {
//   console.log("对象释放:", self.name);
//   // 在这里添加释放逻辑
//   // 例如: 结束拖拽、恢复外观、完成操作等
// }`;
        },

        /**
         * 获取Switch/Slider组件的onChange事件模板
         */
        getChangeEvent(componentType) {
            const isSwitch = componentType === 'Switch';
            return `

/**
 * 🔄 onChange - 值变化事件
 * 触发时机: ${isSwitch ? '开关状态改变时触发' : '滑动条值改变时触发'}
 * 作用: 响应${isSwitch ? '开关状态' : '数值'}变化、更新相关UI、执行相应逻辑等
 * 配合方法: 无需手动调用，${isSwitch ? '状态变化' : '值变化'}时自动触发
 * 参数: ${isSwitch ? 'newValue - 新的开关状态 (true/false), oldValue - 旧的开关状态' : 'newValue - 新的数值, oldValue - 旧的数值'}
 */
// function onChange(newValue, oldValue) {
//   console.log("${isSwitch ? '开关状态变化' : '数值变化'}:", self.name, "从", oldValue, "到", newValue);
//   // 在这里添加${isSwitch ? '状态变化' : '值变化'}处理逻辑
//   // 例如: 更新其他UI组件、保存设置、触发相关操作等
// }`;
        },

        /**
         * 获取UIInput组件的输入事件模板
         */
        getInputEvents() {
            return `

/**
 * 🔄 onChange - 文本内容改变事件
 * 触发时机: 用户输入文本导致值改变时触发
 * 作用: 处理文本变化、实时验证、数据同步等
 * 参数: newValue - 新的文本值, oldValue - 旧的文本值
 */
function onChange(newValue, oldValue) {
  console.log("文本改变:", oldValue, "→", newValue);
  // 在这里添加文本变化处理逻辑
  // 例如: 实时验证、数据同步、格式化等
}

/**
 * 🎯 onFocus - 获得焦点事件
 * 触发时机: 输入框获得焦点时触发
 * 作用: 处理焦点获得时的逻辑
 */
function onFocus() {
  console.log("输入框获得焦点:", self.name || "unnamed");
  // 在这里添加获得焦点时的处理逻辑
  // 例如: 显示提示信息、改变样式等
}

/**
 * 🎯 onBlur - 失去焦点事件
 * 触发时机: 输入框失去焦点时触发
 * 作用: 处理焦点失去时的逻辑、数据验证等
 */
function onBlur() {
  console.log("输入框失去焦点:", self.name || "unnamed");
  // 在这里添加失去焦点时的处理逻辑
  // 例如: 数据验证、保存数据等
}

/**
 * ⌨️ onEnterPressed - 回车键按下事件
 * 触发时机: 用户在输入框中按下回车键时触发
 * 作用: 处理回车键确认操作
 */
function onEnterPressed() {
  console.log("回车键按下:", self.value);
  // 在这里添加回车键处理逻辑
  // 例如: 提交表单、确认输入等
}

/**
 * ❌ onValidationFailed - 输入验证失败事件
 * 触发时机: 输入内容不符合验证规则时触发
 * 作用: 处理验证失败的情况
 * 参数: errorMessage - 错误信息
 */
function onValidationFailed(errorMessage) {
  console.log("输入验证失败:", errorMessage);
  // 在这里添加验证失败处理逻辑
  // 例如: 显示错误提示、重置输入等
}`;
        },

        /**
         * 为对象添加脚本管理方法
         */
        addScriptMethods(obj) {
            // 添加脚本
            obj.addScript = function(script) {
                if (!this.componentScripts) {
                    this.componentScripts = [];
                }
                this.componentScripts.push(script);
                console.log(`📝 添加脚本: ${script.name}`);
            };

            // 删除脚本
            obj.removeScript = function(scriptId) {
                if (!this.componentScripts) return false;
                const index = this.componentScripts.findIndex(s => s.id === scriptId);
                if (index !== -1) {
                    const removed = this.componentScripts.splice(index, 1)[0];
                    console.log(`🗑️ 删除脚本: ${removed.name}`);
                    return true;
                }
                return false;
            };

            // 🔑 更新脚本（关键方法）
            obj.updateScript = function(scriptId, updates) {
                if (!this.componentScripts) return false;
                const script = this.componentScripts.find(s => s.id === scriptId);
                if (script) {
                    // 更新脚本属性
                    Object.assign(script, updates);
                    // console.log(`📝 更新脚本: ${script.name}`, updates);
                    return true;
                }
                console.warn(`⚠️ 未找到脚本: ${scriptId}`);
                return false;
            };

            // 启用/禁用脚本
            obj.setScriptEnabled = function(scriptId, enabled) {
                if (!this.componentScripts) return false;
                const script = this.componentScripts.find(s => s.id === scriptId);
                if (script) {
                    script.enabled = enabled;
                    console.log(`${enabled ? '✅' : '❌'} ${enabled ? '启用' : '禁用'}脚本: ${script.name}`);
                    return true;
                }
                return false;
            };

            // 🔑 切换脚本启用状态（ScriptPanel需要的方法）
            obj.toggleScript = function(scriptId, enabled) {
                return this.setScriptEnabled(scriptId, enabled);
            };

            // 获取脚本
            obj.getScript = function(scriptId) {
                if (!this.componentScripts) return null;
                return this.componentScripts.find(s => s.id === scriptId) || null;
            };

            // 获取所有启用的脚本
            obj.getEnabledScripts = function() {
                if (!this.componentScripts) return [];
                return this.componentScripts.filter(s => s.enabled);
            };

            // 执行脚本中的方法
            obj.executeScriptMethod = function(methodName, ...args) {
                const enabledScripts = this.getEnabledScripts();
                const results = [];

                // console.log(`🔧 UIScriptManager: 执行脚本方法 ${methodName}`, {
                //     componentType: this.constructor.name,
                //     enabledScriptsCount: enabledScripts.length,
                //     args: args
                // });

                for (const script of enabledScripts) {
                    try {
                        // 🔑 修复：创建正确的脚本执行环境
                        // 使用with语句和eval来确保函数定义能够正确添加到上下文
                        const scriptContext = { self: this };

                        // 创建一个包装函数，在其中执行脚本代码
                        const wrappedCode = `
                            (function() {
                                ${script.code}

                                // 将所有函数添加到上下文对象
                                // 🔑 通用生命周期方法
                                if (typeof onStart === 'function') this.onStart = onStart;
                                if (typeof onUpdate === 'function') this.onUpdate = onUpdate;
                                if (typeof onFieldUpdate === 'function') this.onFieldUpdate = onFieldUpdate;
                                if (typeof onDestroy === 'function') this.onDestroy = onDestroy;

                                // 🔑 按钮相关方法
                                if (typeof onClick === 'function') this.onClick = onClick;
                                if (typeof onDoubleClick === 'function') this.onDoubleClick = onDoubleClick;
                                if (typeof onHover === 'function') this.onHover = onHover;
                                if (typeof onHoverOut === 'function') this.onHoverOut = onHoverOut;
                                if (typeof onPress === 'function') this.onPress = onPress;
                                if (typeof onRelease === 'function') this.onRelease = onRelease;

                                // 🔑 滑动条/开关相关方法
                                if (typeof onChange === 'function') this.onChange = onChange;

                                // 🔑 UIInput特有方法
                                if (typeof onFocus === 'function') this.onFocus = onFocus;
                                if (typeof onBlur === 'function') this.onBlur = onBlur;
                                if (typeof onEnterPressed === 'function') this.onEnterPressed = onEnterPressed;
                                if (typeof onValidationFailed === 'function') this.onValidationFailed = onValidationFailed;
                            }).call(scriptContext);
                        `;

                        // 执行包装后的代码
                        eval(wrappedCode);

                        // 执行指定方法
                        if (typeof scriptContext[methodName] === 'function') {
                            // console.log(`✅ UIScriptManager: 执行脚本 [${script.name}::${methodName}]`);
                            const result = scriptContext[methodName].apply(this, args);
                            results.push(result);
                        } else {
                            //  console.log(`⚠️ UIScriptManager: 脚本 [${script.name}] 中未找到方法 ${methodName}`);
                        }
                    } catch (error) {
                        console.error(`❌ 脚本执行错误 [${script.name}::${methodName}]:`, error);
                    }
                }

                return results;
            };

            // 🔑 添加executeScript方法作为executeScriptMethod的别名（兼容性）
            obj.executeScript = function(methodName, ...args) {
                return this.executeScriptMethod(methodName, ...args);
            };
        }
    };

    console.log('✅ UIScriptManager: 脚本管理器已加载');

})();
