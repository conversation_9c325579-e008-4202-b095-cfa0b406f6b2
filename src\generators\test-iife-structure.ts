/**
 * 测试新的立即执行函数结构
 */

import { generatePluginCode } from './pluginGenerator';
import { SceneModel } from '../type/senceModel.svelte';

/**
 * 创建测试用的场景模型
 */
function createTestSceneModel(sceneName: string): SceneModel {
  // 创建一个模拟的场景对象
  const mockScene = {
    constructor: { name: sceneName },
    className: sceneName,
    name: sceneName,
    x: 0,
    y: 0,
    width: 816,
    height: 624,
    alpha: 1,
    visible: true,
    children: [],
    // 模拟方法
    addChild: () => {},
    removeChild: () => {},
    refresh: () => {}
  };

  return new SceneModel(mockScene);
}

/**
 * 测试立即执行函数结构
 */
export async function testIIFEStructure(): Promise<void> {
  console.log('=== 测试立即执行函数结构 ===');

  try {
    // 创建测试场景
    const sceneModels = new Map<string, SceneModel>();
    sceneModels.set('Scene_Title', createTestSceneModel('Scene_Title'));
    sceneModels.set('Scene_Map', createTestSceneModel('Scene_Map'));

    // 生成插件代码
    const pluginCode = await generatePluginCode(sceneModels, {
      pluginName: 'TestPlugin',
      pluginDescription: 'Test plugin for IIFE structure',
      author: 'Test Author',
      version: '1.0.0',
      includePlugins: ['uiScriptManager.js', 'uiComponent.js'] // 简化测试
    });

    console.log('=== 生成的插件代码结构分析 ===');
    
    // 分析代码结构
    const lines = pluginCode.split('\n');
    let iifeCount = 0;
    let iifeStarts: number[] = [];
    let iifeEnds: number[] = [];

    lines.forEach((line, index) => {
      if (line.includes('(() => {')) {
        iifeCount++;
        iifeStarts.push(index + 1);
        console.log(`发现立即执行函数开始 #${iifeCount} 在第 ${index + 1} 行: ${line.trim()}`);
      }
      if (line.includes('})();')) {
        iifeEnds.push(index + 1);
        console.log(`发现立即执行函数结束在第 ${index + 1} 行: ${line.trim()}`);
      }
    });

    console.log(`\n总共发现 ${iifeCount} 个立即执行函数`);
    console.log(`预期：1个插件代码IIFE + 2个场景IIFE = 3个`);
    console.log(`实际：${iifeCount} 个`);

    // 检查特定的标记
    const hasPluginIIFE = pluginCode.includes('// ===== 插件代码立即执行方法 =====');
    const hasSceneTitleIIFE = pluginCode.includes('// ===== Scene_Title 场景立即执行方法 =====');
    const hasSceneMapIIFE = pluginCode.includes('// ===== Scene_Map 场景立即执行方法 =====');

    console.log('\n=== 结构检查 ===');
    console.log(`✓ 插件代码IIFE: ${hasPluginIIFE ? '存在' : '缺失'}`);
    console.log(`✓ Scene_Title IIFE: ${hasSceneTitleIIFE ? '存在' : '缺失'}`);
    console.log(`✓ Scene_Map IIFE: ${hasSceneMapIIFE ? '存在' : '缺失'}`);

    // 检查日志输出
    const hasPluginStartLog = pluginCode.includes('console.log("RPG Editor: 开始加载插件代码");');
    const hasPluginEndLog = pluginCode.includes('console.log("RPG Editor: 插件代码加载完成");');
    const hasSceneTitleStartLog = pluginCode.includes('console.log("RPG Editor: 开始处理 Scene_Title");');
    const hasSceneTitleEndLog = pluginCode.includes('console.log("RPG Editor: Scene_Title 处理完成");');

    console.log('\n=== 日志检查 ===');
    console.log(`✓ 插件开始日志: ${hasPluginStartLog ? '存在' : '缺失'}`);
    console.log(`✓ 插件结束日志: ${hasPluginEndLog ? '存在' : '缺失'}`);
    console.log(`✓ Scene_Title开始日志: ${hasSceneTitleStartLog ? '存在' : '缺失'}`);
    console.log(`✓ Scene_Title结束日志: ${hasSceneTitleEndLog ? '存在' : '缺失'}`);

    // 输出代码片段用于验证
    console.log('\n=== 代码片段预览 ===');
    const previewLines = lines.slice(0, 50); // 前50行
    console.log(previewLines.join('\n'));

    if (iifeCount >= 3 && hasPluginIIFE && hasSceneTitleIIFE && hasSceneMapIIFE) {
      console.log('\n✅ 立即执行函数结构测试通过！');
    } else {
      console.log('\n❌ 立即执行函数结构测试失败！');
    }

  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testIIFEStructure();
}
