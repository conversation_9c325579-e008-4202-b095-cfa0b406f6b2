/**
 * Scene_Title 场景数据流定义
 */

import type { SceneDataFlow } from '../types';

/**
 * Scene_Title 场景的数据流
 */
export const SceneTitleDataFlow: SceneDataFlow = {
  sceneName: "Scene_Title",
  dataResources: [
    {
      dataPath: "$dataSystem.title1Name",
      description: "标题背景图1文件名"
    },
    {
      dataPath: "$dataSystem.title2Name",
      description: "标题背景图2文件名"
    },
    {
      dataPath: "$dataSystem.gameTitle",
      description: "游戏标题文本"
    }
  ],
  buttons: [
    {
      buttonName: "NewGame",
      triggerMethods: [
        "DataManager.setupNewGame()",
        "this.fadeOutAll()",
        "SceneManager.goto(Scene_Map)"
      ]
    },
    {
      buttonName: "Continue",
      triggerMethods: [
        "SceneManager.push(Scene_Load)"
      ]
    },
    {
      buttonName: "Options",
      triggerMethods: [
        "SceneManager.push(Scene_Options)"
      ]
    }
  ]
};
