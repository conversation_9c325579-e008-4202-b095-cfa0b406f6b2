



(() => {
    'use strict';

    /**
     * 🎯 消息系统拦截器 - 简化回调设计
     *
     * 职责：
     * 1. 拦截 $gameMessage.add() 和 $gameMessage.setChoices()
     * 2. 提供简单的回调方法，UI组件直接赋值即可
     * 3. 不同类型的消息调用不同的回调方法
     */

    /**
     * 🔧 消息拦截器
     */
    class MessageInterceptor {
        constructor() {
            this.isEnabled = true;
            this.isSetup = false;

            // 🔑 回调方法 - UI组件直接赋值这些方法
            this._onMessage = null;      // 内部存储
            this._onChoice = null;       // 内部存储
            this._onNumberInput = null;  // 内部存储
            this._onItemChoice = null;   // 内部存储
        }

        // 🔑 设置消息回调时自动初始化拦截器
        set onMessage(callback) {
            console.log('🔧 MessageInterceptor: 设置 onMessage 回调');
            this._onMessage = callback;
            this.ensureSetup();
        }

        get onMessage() {
            return this._onMessage;
        }

        // 🔑 设置选择回调时自动初始化拦截器
        set onChoice(callback) {
            this._onChoice = callback;
            this.ensureSetup();
        }

        get onChoice() {
            return this._onChoice;
        }

        // 🔑 设置数字输入回调时自动初始化拦截器
        set onNumberInput(callback) {
            this._onNumberInput = callback;
            this.ensureSetup();
        }

        get onNumberInput() {
            return this._onNumberInput;
        }

        // 🔑 设置物品选择回调时自动初始化拦截器
        set onItemChoice(callback) {
            this._onItemChoice = callback;
            this.ensureSetup();
        }

        get onItemChoice() {
            return this._onItemChoice;
        }

        /**
         * 🎯 确保拦截器已设置（延迟初始化）
         */
        ensureSetup() {
            if (!this.isSetup) {
                this.setupInterception();
            }
        }

        /**
         * 🎯 设置拦截
         */
        setupInterception() {
            // 检查 $gameMessage 是否存在
            if (typeof window.$gameMessage === 'undefined' || window.$gameMessage === null) {
                console.log('MessageInterceptor: $gameMessage 尚未初始化，延迟设置拦截');
                // 延迟重试
                setTimeout(() => {
                    this.isSetup = false; // 重置状态
                    this.ensureSetup();
                }, 100);
                return;
            }

            if (this.isSetup) {
                return; // 避免重复设置
            }

            // 保存原始方法
            const originalAdd = window.$gameMessage.add;
            const originalSetChoices = window.$gameMessage.setChoices;
            const self = this;

            this.isSetup = true;

            // 🔑 拦截消息添加
            window.$gameMessage.add = function(text) {
                if (self.isEnabled) {
                    // 🔑 关键：先调用原生方法保持消息状态
                    originalAdd.call(this, text);

                    // 触发自定义消息事件
                    self.triggerCustomMessageEvent({
                        type: 'message',
                        text: text,
                        faceName: window.$gameMessage.faceName(),
                        faceIndex: window.$gameMessage.faceIndex(),
                        background: window.$gameMessage.background(),
                        positionType: window.$gameMessage.positionType()
                    });

                    // 注意：不阻止原生消息，让$gameMessage.isBusy()正常工作
                } else {
                    // 使用原生系统
                    originalAdd.call(this, text);
                }
            };

            // 🔑 拦截选择项设置
            window.$gameMessage.setChoices = function(choices, defaultType, cancelType) {
                if (self.isEnabled) {
                    // 触发自定义选择事件
                    self.triggerCustomChoiceEvent({
                        type: 'choices',
                        choices: choices,
                        defaultType: defaultType,
                        cancelType: cancelType,
                        background: window.$gameMessage.choiceBackground()
                    });

                    // 阻止原生选择显示
                    return;
                } else {
                    // 使用原生系统
                    originalSetChoices.call(this, choices, defaultType, cancelType);
                }
            };

            console.log('✅ 消息拦截器已启用');
        }

        /**
         * 🎯 触发消息回调
         */
        triggerCustomMessageEvent(messageData) {
            console.log('🎯 MessageInterceptor: 触发消息回调:', messageData);
            console.log('🔍 MessageInterceptor: 当前回调状态:', {
                hasCallback: !!this.onMessage,
                callbackType: typeof this.onMessage,
                isEnabled: this.isEnabled,
                isSetup: this.isSetup
            });

            // 🔑 直接调用消息回调方法
            if (this.onMessage && typeof this.onMessage === 'function') {
                try {
                    console.log('✅ MessageInterceptor: 执行消息回调');
                    this.onMessage(messageData);
                } catch (error) {
                    console.error('❌ MessageInterceptor: 消息回调执行失败:', error);
                }
            } else {
                console.warn('⚠️ MessageInterceptor: 没有设置消息回调方法');
            }
        }

        /**
         * 🎯 触发选择回调
         */
        triggerCustomChoiceEvent(choiceData) {
            console.log('🎯 触发选择回调:', choiceData);

            // � 直接调用选择回调方法
            if (this.onChoice && typeof this.onChoice === 'function') {
                try {
                    this.onChoice(choiceData);
                } catch (error) {
                    console.error('❌ MessageInterceptor: 选择回调执行失败:', error);
                }
            }
        }

        /**
         * 🎯 处理选择项选中（由UILayout中的按钮调用）
         */
        handleChoiceSelected(index) {
            console.log('选择项被选中:', index);

            // 调用原生的选择处理
            if (window.$gameMessage && window.$gameMessage.onChoice) {
                window.$gameMessage.onChoice(index);
            }
        }

        /**
         * 🎯 完成消息显示（由UI组件调用）
         */
        completeMessage() {
            console.log('🎯 MessageInterceptor: 完成消息显示');

            // 清除消息文本，让事件继续
            if (window.$gameMessage) {
                window.$gameMessage.clear();
                console.log('✅ MessageInterceptor: 消息已清除，事件可以继续');
            }
        }

        /**
         * 🎯 完成选择项（由UI组件调用）
         */
        completeChoice() {
            console.log('🎯 MessageInterceptor: 完成选择项');

            // 清除选择项，让事件继续
            if (window.$gameMessage) {
                window.$gameMessage._choices = [];
                window.$gameMessage._choiceDefaultType = 0;
                window.$gameMessage._choiceCancelType = 0;
                window.$gameMessage._choiceBackground = 0;
                console.log('✅ MessageInterceptor: 选择项已清除，事件可以继续');
            }
        }
    }

    // 🚀 创建全局拦截器实例
    window.MessageInterceptor = new MessageInterceptor();

    console.log('✅ 消息拦截器已加载');

})();
