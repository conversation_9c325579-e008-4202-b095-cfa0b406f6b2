<script lang="ts">
  import ScriptItem from './ScriptItem.svelte';

  /**
   * 脚本接口定义
   */
  interface Script {
    id: string;
    name: string;           // 脚本名称，如 "主要逻辑脚本"
    enabled: boolean;       // 是否启用整个脚本
    code: string;          // 完整的脚本代码
    description?: string;   // 脚本描述
  }

  /**
   * 组件属性
   */
  let { selectedObject }: { selectedObject: any } = $props();

  // 响应式状态
  let scriptCounter = $state(1);

  /**
   * 默认脚本模板
   */
  const DEFAULT_SCRIPT_TEMPLATE = `/**
 * 🚀 onStart - 对象启动生命周期
 * 触发时机: 对象创建并添加到父容器时自动触发
 * 作用: 初始化对象状态、设置默认值、绑定数据等
 * 配合方法: 无需手动调用，系统自动触发
 */
function onStart() {
  console.log("对象启动:", self.name || "unnamed");
  // 在这里添加初始化逻辑
  // 例如: 设置初始文本、绑定数据源、初始化变量等
}

/**
 * 🔄 onUpdate - 每帧更新生命周期
 * 触发时机: 每帧自动触发（约60FPS），只要对象存在就会持续调用
 * 作用: 实现动画、实时数据更新、状态检查等
 * 配合方法: 无需手动调用，系统自动触发
 * 注意: 避免在此方法中执行耗时操作
 */
// function onUpdate() {
//   console.log("每帧更新:", self.name);
//   // 在这里添加每帧更新逻辑
//   // 例如: 动画更新、实时数据显示、状态检查等
// }

/**
 * 📝 onFieldUpdate - 字段更新生命周期
 * 触发时机: 调用 updateFieldsToChildren(ctx) 或 onFieldUpdate(ctx) 时触发
 * 作用: 响应数据变化、更新UI显示、同步状态等
 * 配合方法: parentObject.updateFieldsToChildren(ctx) 或 object.onFieldUpdate(ctx)
 * 参数: ctx - 包含字段更新上下文信息的对象
 */
// function onFieldUpdate(ctx) {
//   console.log("字段更新:", self.name, ctx);
//   // 在这里添加字段更新时的处理逻辑
//   // ctx 包含: { field, oldValue, newValue, source, timestamp 等 }
//   // 例如: 根据 ctx.field 判断更新哪个属性
// }

/**
 * 🗑️ onDestroy - 对象销毁生命周期
 * 触发时机: 对象从父容器移除时自动触发
 * 作用: 清理资源、保存数据、解除绑定等
 * 配合方法: 无需手动调用，系统自动触发
 */
// function onDestroy() {
//   console.log("对象销毁:", self.name);
//   // 在这里添加清理逻辑
//   // 例如: 清理定时器、保存状态、解除事件绑定等
// }

/**
 * 👆 onClick - 单击交互事件
 * 触发时机: 用户单击对象时触发
 * 作用: 处理点击逻辑、切换状态、执行操作等
 * 配合方法: 无需手动调用，用户交互自动触发
 */
// function onClick() {
//   console.log("对象被点击:", self.name);
//   // 在这里添加点击逻辑
//   // 例如: 切换状态、打开菜单、执行命令等
// }

/**
 * 👆👆 onDoubleClick - 双击交互事件
 * 触发时机: 用户双击对象时触发
 * 作用: 处理双击特殊逻辑、快捷操作等
 * 配合方法: 无需手动调用，用户交互自动触发
 */
// function onDoubleClick() {
//   console.log("对象被双击:", self.name);
//   // 在这里添加双击逻辑
//   // 例如: 快速编辑、全屏显示、快捷操作等
// }

/**
 * 🖱️ onHover - 鼠标悬停事件
 * 触发时机: 鼠标进入对象区域时触发
 * 作用: 显示提示信息、改变外观、预览效果等
 * 配合方法: 通常与 onHoverOut 配对使用
 */
// function onHover() {
//   console.log("鼠标悬停:", self.name);
//   // 在这里添加悬停逻辑
//   // 例如: 显示工具提示、改变颜色、显示预览等
// }

/**
 * 🖱️ onHoverOut - 鼠标离开事件
 * 触发时机: 鼠标离开对象区域时触发
 * 作用: 隐藏提示信息、恢复外观、清理预览等
 * 配合方法: 通常与 onHover 配对使用
 */
// function onHoverOut() {
//   console.log("鼠标离开:", self.name);
//   // 在这里添加鼠标离开逻辑
//   // 例如: 隐藏工具提示、恢复颜色、清理预览等
// }

/**
 * ⬇️ onPress - 按下事件
 * 触发时机: 鼠标按下（但未释放）时触发
 * 作用: 开始拖拽、显示按下效果、记录按下状态等
 * 配合方法: 通常与 onRelease 配对使用
 */
// function onPress() {
//   console.log("对象按下:", self.name);
//   // 在这里添加按下逻辑
//   // 例如: 开始拖拽、改变外观、记录状态等
// }

/**
 * ⬆️ onRelease - 释放事件
 * 触发时机: 鼠标释放时触发
 * 作用: 结束拖拽、恢复外观、完成操作等
 * 配合方法: 通常与 onPress 配对使用
 */
// function onRelease() {
//   console.log("对象释放:", self.name);
//   // 在这里添加释放逻辑
//   // 例如: 结束拖拽、恢复外观、完成操作等
// }`;

  /**
   * 获取当前对象的脚本数组
   */
  function getScripts(): Script[] {
    // 如果是模型对象，获取其原始对象
    const targetObject = selectedObject?._originalObject || selectedObject;

    if (!targetObject || !targetObject.componentScripts) {
      return [];
    }
    return targetObject.componentScripts;
  }



  /**
   * 直接添加新脚本
   */
  function addNewScript() {
    // 获取目标对象（原始对象）
    const targetObject = selectedObject?._originalObject || selectedObject;

    if (!targetObject || !targetObject.addScript) {
      console.warn('ScriptPanel: 对象不支持脚本功能');
      return;
    }

    try {
      const newScript = {
        name: `脚本 ${scriptCounter}`,
        type: 'custom',
        defaultCode: DEFAULT_SCRIPT_TEMPLATE
      };

      targetObject.addScript(newScript);
      console.log(`✅ ScriptPanel: 已添加脚本 ${newScript.name}`);

      // 同步到模型
      if (selectedObject && selectedObject.componentScripts) {
        selectedObject.componentScripts = [...targetObject.componentScripts];
      }

      // 增加计数器
      scriptCounter++;
    } catch (error) {
      console.error('❌ ScriptPanel: 添加脚本失败:', error);
    }
  }

  /**
   * 更新脚本代码
   */
  function updateScript(scriptId: string, newCode: string) {
    // 获取目标对象（原始对象）
    const targetObject = selectedObject?._originalObject || selectedObject;

    if (!targetObject || !targetObject.updateScript) {
      console.warn('ScriptPanel: 对象不支持脚本功能');
      return;
    }

    try {
      targetObject.updateScript(scriptId, { code: newCode });
      console.log(`✅ ScriptPanel: 已更新脚本 ${scriptId}`);

      // 同步到模型
      if (selectedObject && selectedObject.componentScripts) {
        selectedObject.componentScripts = [...targetObject.componentScripts];
      }
    } catch (error) {
      console.error('❌ ScriptPanel: 更新脚本失败:', error);
    }
  }

  /**
   * 删除脚本
   */
  function deleteScript(scriptId: string) {
    if (!confirm('确定要删除这个脚本吗？')) {
      return;
    }

    // 获取目标对象（原始对象）
    const targetObject = selectedObject?._originalObject || selectedObject;

    if (!targetObject || !targetObject.removeScript) {
      console.warn('ScriptPanel: 对象不支持脚本功能');
      return;
    }

    try {
      targetObject.removeScript(scriptId);
      console.log(`✅ ScriptPanel: 已删除脚本 ${scriptId}`);

      // 同步到模型
      if (selectedObject && selectedObject.componentScripts) {
        selectedObject.componentScripts = [...targetObject.componentScripts];
      }
    } catch (error) {
      console.error('❌ ScriptPanel: 删除脚本失败:', error);
    }
  }

  /**
   * 切换脚本启用状态
   */
  function toggleScript(scriptId: string) {
    // 获取目标对象（原始对象）
    const targetObject = selectedObject?._originalObject || selectedObject;

    if (!targetObject || !targetObject.toggleScript) {
      console.warn('ScriptPanel: 对象不支持脚本功能');
      return;
    }

    try {
      const script = targetObject.componentScripts?.find((s: Script) => s.id === scriptId);
      if (script) {
        const newEnabled = !script.enabled;
        targetObject.toggleScript(scriptId, newEnabled);
        console.log(`✅ ScriptPanel: 已${newEnabled ? '启用' : '禁用'}脚本 ${scriptId}`);

        // 同步到模型
        if (selectedObject && selectedObject.componentScripts) {
          selectedObject.componentScripts = [...targetObject.componentScripts];
        }
      }
    } catch (error) {
      console.error('❌ ScriptPanel: 切换脚本状态失败:', error);
    }
  }



  /**
   * 检查对象是否支持脚本
   */
  function supportsScripts(): boolean {
    // 获取目标对象（原始对象）
    const targetObject = selectedObject?._originalObject || selectedObject;

    // 🔧 调试日志
    console.log('🔍 ScriptPanel: 检查脚本支持', {
      selectedObject: selectedObject?.className,
      targetObject: targetObject?.constructor?.name,
      hasComponentScripts: !!targetObject?.componentScripts,
      componentScriptsLength: targetObject?.componentScripts?.length,
      hasAddScript: typeof targetObject?.addScript === 'function',
      hasUpdateScript: typeof targetObject?.updateScript === 'function',
      hasRemoveScript: typeof targetObject?.removeScript === 'function',
      hasToggleScript: typeof targetObject?.toggleScript === 'function'
    });

    return targetObject &&
           targetObject.componentScripts &&
           typeof targetObject.addScript === 'function';
  }
</script>

<!-- 脚本管理面板 -->
<div class="script-panel">
  {#if !selectedObject}
    <div class="no-selection">
      <span class="no-selection-icon">📝</span>
      <span class="no-selection-text">请选择一个对象来管理脚本</span>
    </div>
  {:else if !supportsScripts()}
    <div class="no-scripts-support">
      <span class="no-support-icon">⚠️</span>
      <span class="no-support-text">当前对象不支持脚本功能</span>
    </div>
  {:else}
    <!-- 面板头部 -->
    <div class="panel-header">
      <h3 class="panel-title">
        <span class="title-icon">📝</span>
        组件脚本
      </h3>

      <div class="header-actions">
        <button
          class="add-script-btn"
          onclick={addNewScript}
        >
          + 添加新脚本
        </button>
      </div>
    </div>

    <!-- 脚本统计 -->
    <!-- <div class="script-stats">
      📊 脚本统计: {getScripts().length}个脚本
      ({getEnabledScripts().length}个启用, {getDisabledScripts().length}个禁用)
    </div> -->

    <!-- 脚本列表 -->
    <div class="scripts-list">
      {#each getScripts() as script (script.id)}
        <ScriptItem
          {script}
          onUpdate={updateScript}
          onDelete={deleteScript}
          onToggle={toggleScript}
        />
      {/each}

      {#if getScripts().length === 0}
        <div class="no-scripts">
          <span class="no-scripts-icon">📄</span>
          <span class="no-scripts-text">暂无脚本，点击"添加新脚本"开始</span>
        </div>
      {/if}
    </div>
  {/if}
</div>



<style>
  .script-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-surface, #ffffff);
  }

  /* 无选择状态 */
  .no-selection,
  .no-scripts-support {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: var(--theme-text-secondary, #64748b);
  }

  .no-selection-icon,
  .no-support-icon {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
  }

  .no-selection-text,
  .no-support-text {
    font-size: 14px;
    line-height: 1.5;
  }

  /* 面板头部 */
  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid var(--theme-border-light, #e2e8f0);
    background: var(--theme-surface, #ffffff);
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .panel-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--theme-text, #1a202c);
  }

  .title-icon {
    font-size: 18px;
  }

  .add-script-btn {
    padding: 8px 16px;
    background: var(--theme-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .add-script-btn:hover:not(:disabled) {
    background: var(--theme-primary-dark, #2563eb);
  }

  .add-script-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* 脚本列表 */
  .scripts-list {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow-y: auto;
  }

  .no-scripts {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: var(--theme-text-secondary, #64748b);
  }

  .no-scripts-icon {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
  }

  .no-scripts-text {
    font-size: 14px;
    line-height: 1.5;
  }


</style>
