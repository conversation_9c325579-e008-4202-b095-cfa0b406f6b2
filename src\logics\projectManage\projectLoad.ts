/**
 * 项目加载逻辑模块
 * 负责处理项目选择、验证、资源扫描和路径配置
 */

import { TauriAPI, type ScriptInfo } from '../../lib/tauriAPI';
import { projectActions } from '../../stores/projectStore';

/**
 * 项目资源配置接口
 */
export interface ProjectResources {
  projectPath: string;
  projectName: string;
  projectFilePath: string;
  scripts: ScriptInfo[];
  resourcePaths: {
    js: string;
    data: string;
    img: string;
    audio: string;
  };
}

/**
 * 项目加载结果接口
 */
export interface ProjectLoadResult {
  success: boolean;
  data?: ProjectResources;
  error?: string;
}

/**
 * 项目加载器类
 */
export class ProjectLoader {
  private static instance: ProjectLoader;
  private currentProject: ProjectResources | null = null;

  private constructor() { }

  /**
   * 获取单例实例
   */
  static getInstance(): ProjectLoader {
    if (!ProjectLoader.instance) {
      ProjectLoader.instance = new ProjectLoader();
    }
    return ProjectLoader.instance;
  }

  /**
   * 选择并加载项目
   * @returns Promise<ProjectLoadResult> 加载结果
   */
  async selectAndLoadProject(): Promise<ProjectLoadResult> {
    try {
      console.log('=== 开始项目选择和加载流程 ===');
      // location.reload()
      // 更新全局状态：开始加载
      projectActions.startLoading();

      // 1. 选择项目文件
      const projectFilePath = await this.selectProjectFile();
      if (!projectFilePath) {
        const error = '用户取消选择项目文件';
        projectActions.setError(error);
        return { success: false, error };
      }

      // 2. 获取项目目录
      const projectPath = await this.getProjectDirectory(projectFilePath);
      if (!projectPath) {
        const error = '无法获取项目目录';
        projectActions.setError(error);
        return { success: false, error };
      }

      // 3. 验证项目
      const isValid = await this.validateProject(projectPath);
      if (!isValid) {
        const error = '这不是一个有效的 RPG Maker 项目';
        projectActions.setError(error);
        return { success: false, error };
      }

      // 4. 保存项目信息到后端
      console.log('保存项目信息到后端...');
      const saveResult = await TauriAPI.Project.saveCurrentProjectInfo(projectPath, projectFilePath);
      if (!saveResult.success) {
        const error = `保存项目信息失败: ${saveResult.error}`;
        projectActions.setError(error);
        return { success: false, error };
      }

      console.log('项目信息已保存到后端，准备刷新页面...');

      // 5. 在刷新页面前关闭代码编辑器窗口
      console.log('关闭代码编辑器窗口...');
      try {
        const { codeEditorManager } = await import('../../windows/codeEditor/codeEditorManager');
        await codeEditorManager.closeEditor();
        console.log('代码编辑器窗口已关闭');
      } catch (error) {
        console.log('关闭代码编辑器窗口失败:', error);
        // 继续执行，不阻塞页面刷新
      }

      // 6. 刷新页面（这会清理所有前端状态）
      location.reload();

      // 注意：这里的代码不会执行，因为页面已经刷新
      return { success: true };

    } catch (error) {
      console.error('项目加载失败:', error);
      const errorMsg = `项目加载失败: ${error}`;
      projectActions.setError(errorMsg);
      return { success: false, error: errorMsg };
    }
  }

  /**
   * 选择项目文件
   */
  private async selectProjectFile(): Promise<string | null> {
    console.log('选择项目文件...');

    const result = await TauriAPI.Project.selectProjectFile();

    if (result.success && result.data) {
      console.log('选择的项目文件:', result.data);
      return result.data;
    } else {
      console.log('用户取消选择或选择失败:', result.error);
      return null;
    }
  }

  /**
   * 获取项目目录
   */
  private async getProjectDirectory(projectFilePath: string): Promise<string | null> {
    console.log('获取项目目录...');

    const result = await TauriAPI.Project.getProjectDirectoryFromFile(projectFilePath);

    if (result.success && result.data) {
      console.log('项目目录:', result.data);
      return result.data;
    } else {
      console.error('获取项目目录失败:', result.error);
      return null;
    }
  }

  /**
   * 验证项目
   */
  private async validateProject(projectPath: string): Promise<boolean> {
    console.log('验证项目有效性...');

    const result = await TauriAPI.Project.validateProject(projectPath);

    if (result.success && result.data) {
      console.log('项目验证通过');
      return true;
    } else {
      console.error('项目验证失败:', result.error);
      return false;
    }
  }

  /**
   * 扫描项目资源
   */
  private async scanProjectResources(projectPath: string, projectFilePath: string): Promise<ProjectResources | null> {
    console.log('扫描项目资源...');

    const result = await TauriAPI.Project.scanProjectScripts(projectPath);

    if (result.success && result.data) {
      const projectName = projectPath.split(/[/\\]/).pop() || 'Unknown Project';

      const resources: ProjectResources = {
        projectPath,
        projectName,
        projectFilePath,
        scripts: result.data,
        resourcePaths: {
          js: `${projectPath}/js`,
          data: `${projectPath}/data`,
          img: `${projectPath}/img`,
          audio: `${projectPath}/audio`
        }
      };

      console.log('资源扫描完成:');
      console.log('- 脚本文件:', result.data.length, '个');
      console.log('- JS路径:', resources.resourcePaths.js);
      console.log('- 数据路径:', resources.resourcePaths.data);
      console.log('- 图片路径:', resources.resourcePaths.img);
      console.log('- 音频路径:', resources.resourcePaths.audio);

      return resources;
    } else {
      console.error('扫描项目资源失败:', result.error);
      return null;
    }
  }

  /**
   * 配置资源路径到全局变量
   */
  private configureResourcePaths(resources: ProjectResources): void {
    console.log('配置全局资源路径...');

    // 设置全局项目配置
    (window as any).PROJECT_CONFIG = {
      projectPath: resources.projectPath,
      projectName: resources.projectName,
      resourcePaths: resources.resourcePaths,
      scripts: resources.scripts,
      // 重要：统一使用外部资源路径
      useExternalResources: true
    };

    // 兼容旧的 PROJECT_BASE_PATH
    (window as any).PROJECT_BASE_PATH = resources.projectPath;

    console.log('全局资源路径配置完成');
    console.log('PROJECT_CONFIG:', (window as any).PROJECT_CONFIG);
  }

  /**
   * 获取当前项目
   */
  getCurrentProject(): ProjectResources | null {
    return this.currentProject;
  }

  /**
   * 清除当前项目
   */
  clearProject(): void {
    console.log('清除当前项目...');

    this.currentProject = null;

    // 清除全局配置
    delete (window as any).PROJECT_CONFIG;
    delete (window as any).PROJECT_BASE_PATH;

    // 更新全局状态
    projectActions.clearProject();

    console.log('项目已清除');
  }

  /**
   * 从后端加载保存的项目信息
   */
  async loadFromBackend(): Promise<ProjectLoadResult> {
    try {
      console.log('=== 从后端加载项目信息 ===');

      // 1. 从后端获取项目信息
      const result = await TauriAPI.Project.getCurrentProjectInfo();
      if (!result.success || !result.data) {
        console.log('后端没有保存的项目信息');
        return { success: false, error: '没有保存的项目信息' };
      }

      const { project_path: projectPath, project_file_path: projectFilePath } = result.data;
      console.log('从后端获取到项目信息:');
      console.log('- 项目路径:', projectPath);
      console.log('- 项目文件:', projectFilePath);

      // 2. 验证项目是否仍然有效
      const isValid = await this.validateProject(projectPath);
      if (!isValid) {
        // 清除无效的项目信息
        await TauriAPI.Project.clearCurrentProjectInfo();
        const error = '保存的项目已无效或不存在';
        return { success: false, error };
      }

      // 3. 扫描项目资源
      const resources = await this.scanProjectResources(projectPath, projectFilePath);
      if (!resources) {
        await TauriAPI.Project.clearCurrentProjectInfo();
        const error = '扫描项目资源失败';
        return { success: false, error };
      }

      // 4. 配置资源路径
      this.configureResourcePaths(resources);

      // 5. 更新全局状态
      this.currentProject = resources;
      projectActions.setProject(resources.projectPath, resources.projectFilePath, resources.scripts);

      // 6. 清除后端保存的项目信息（已经加载完成）
      await TauriAPI.Project.clearCurrentProjectInfo();

      console.log('=== 项目从后端加载完成 ===');
      console.log('项目名称:', resources.projectName);
      console.log('项目路径:', resources.projectPath);
      console.log('脚本数量:', resources.scripts.length);

      return { success: true, data: resources };

    } catch (error) {
      console.error('从后端加载项目失败:', error);
      // 清除可能损坏的项目信息
      await TauriAPI.Project.clearCurrentProjectInfo();
      const errorMsg = `从后端加载项目失败: ${error}`;
      return { success: false, error: errorMsg };
    }
  }

  /**
   * 重新加载当前项目
   */
  async reloadCurrentProject(): Promise<ProjectLoadResult> {
    if (!this.currentProject) {
      return { success: false, error: '没有当前项目可以重新加载' };
    }

    console.log('重新加载当前项目:', this.currentProject.projectName);

    // 重新扫描资源
    const resources = await this.scanProjectResources(
      this.currentProject.projectPath,
      this.currentProject.projectFilePath
    );

    if (resources) {
      this.configureResourcePaths(resources);
      this.currentProject = resources;
      projectActions.setProject(resources.projectPath, resources.projectFilePath, resources.scripts);

      return { success: true, data: resources };
    } else {
      return { success: false, error: '重新加载项目资源失败' };
    }
  }
}

/**
 * 导出单例实例
 */
export const projectLoader = ProjectLoader.getInstance();