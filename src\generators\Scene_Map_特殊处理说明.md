# Scene_Map 特殊处理说明

## 概述

在RPG Maker MZ插件生成器中，我们对`Scene_Map`进行了特殊处理，主要是重写了`createDisplayObjects`方法，并跳过了`this.createAllWindows()`的调用。

## 修改内容

### 1. 重写 createDisplayObjects 方法

**原始方法：**
```javascript
Scene_Map.prototype.createDisplayObjects = function() {
    this.createSpriteset();      // 创建精灵集
    this.createWindowLayer();    // 创建窗口层
    this.createAllWindows();     // 创建所有窗口 ← 这个会被跳过
    this.createButtons();        // 创建按钮
};
```

**修改后的方法：**
```javascript
Scene_Map.prototype.createDisplayObjects = function() {
    console.log('RPG Editor: 使用自定义的 createDisplayObjects 方法');
    this.createSpriteset();
    this.createWindowLayer();
    // 注意：不调用 this.createAllWindows(); - 跳过原生窗口创建
    this.createButtons();
    console.log('RPG Editor: createDisplayObjects 完成，已跳过原生窗口创建');
};
```

### 2. 其他相关方法重写

- `createSpriteset()`: 跳过原生精灵集创建
- `createAllWindows()`: 跳过原生窗口创建  
- `update()`: 安全检查精灵集
- `updateTransferPlayer()`: 安全检查传送

## 为什么要这样做？

### 1. 避免原生窗口冲突
- 原生的`createAllWindows()`会创建消息窗口、菜单窗口等
- 这些窗口可能与编辑器中自定义的UI组件冲突
- 跳过原生窗口创建，使用编辑器的自定义UI

### 2. 保持必要的初始化
- 保留`createSpriteset()`：确保基础精灵系统正常
- 保留`createWindowLayer()`：确保窗口层存在
- 保留`createButtons()`：确保按钮系统正常

### 3. 完全自定义控制
- 编辑器可以完全控制地图场景的UI
- 可以创建独特的游戏界面
- 避免原生系统的限制

## 代码生成流程

1. **场景识别**: 在`generateSceneSpecificMethodOverrides()`中识别`Scene_Map`
2. **方法重写**: 调用`generateSceneMapMethodOverrides()`生成重写代码
3. **代码注入**: 将重写的方法注入到生成的插件中

## 使用示例

```typescript
// 生成包含Scene_Map特殊处理的插件
const sceneModels = new Map();
sceneModels.set('Scene_Map', mySceneMapModel);

const pluginCode = await generatePluginCode(sceneModels, {
    pluginName: 'CustomSceneMapPlugin',
    pluginDescription: 'Scene_Map with custom UI components'
});
```

## 生成的代码结构

```javascript
// ===== Scene_Map 场景立即执行方法 =====
(() => {
  "use strict";
  console.log("RPG Editor: 开始处理 Scene_Map");

  // Scene_Map 场景创建方法
  Scene_Map.prototype.create = function() {
    Scene_Base.prototype.create.call(this);
    // ... 自定义对象创建代码
  };

  // ===== Scene_Map 方法重写 =====
  Scene_Map.prototype.createDisplayObjects = function() {
    // 自定义的创建流程，跳过 createAllWindows()
  };
  
  // 其他方法重写...
})();
```

## 注意事项

1. **兼容性**: 确保跳过原生窗口不会影响游戏的基本功能
2. **调试**: 添加了详细的console.log用于调试
3. **安全性**: 在update等方法中添加了安全检查
4. **扩展性**: 可以根据需要添加更多的方法重写

## 相关文件

- `src/generators/pluginGenerator.ts`: 主要修改文件
- `src/generators/scene-map-test.js`: 测试示例
- `src/generators/scene-map-usage-example.ts`: 使用示例
- `Scene_Map 的创建流.txt`: 原始创建流程分析
