// 工具函数示例 - 实用的辅助函数

import type { RPGExample } from '../types';

export const utilityExamples: RPGExample[] = [
  {
    name: '调试工具集',
    type: 'method',
    description: '开发调试用的工具函数',
    code: `// 调试工具集
function debugInfo() {
  if (!$gameTemp.isPlaytest()) {
    console.log('只能在测试模式下使用');
    return;
  }

  console.log('=== 调试信息 ===');
  console.log('当前场景:', SceneManager._scene?.constructor.name);
  console.log('地图ID:', $gameMap.mapId());
  console.log('玩家位置:', \`(\${$gamePlayer.x}, \${$gamePlayer.y})\`);
  console.log('队伍金钱:', $gameParty.gold());
  console.log('队伍成员:', $gameParty.members().length);
}

function quickSetup() {
  if (!$gameTemp.isPlaytest()) return;

  // 给予资源
  $gameParty.gainGold(9999);
  $gameParty.gainItem($dataItems[1], 99);

  // 设置开关变量
  $gameSwitches.setValue(1, true);
  $gameVariables.setValue(1, 100);

  console.log('调试环境设置完成');
}

// 使用示例
debugInfo();
quickSetup();`
  },
  {
    name: '角色状态检查器',
    type: 'method',
    description: '检查角色状态的工具函数',
    code: `// 角色状态检查器
function checkActorStatus(actorId) {
  const actor = $gameActors.actor(actorId);
  if (!actor) {
    console.log('角色不存在');
    return null;
  }

  const status = {
    name: actor.name(),
    level: actor.level,
    hp: \`\${actor.hp}/\${actor.mhp}\`,
    mp: \`\${actor.mp}/\${actor.mmp}\`,
    isDead: actor.isDead(),
    states: actor.states().map(state => state.name),
    skills: actor.skills().length,
    equips: actor.equips().filter(item => item).length
  };

  console.log('=== 角色状态 ===');
  Object.entries(status).forEach(([key, value]) => {
    console.log(\`\${key}: \${value}\`);
  });

  return status;
}

function healActor(actorId) {
  const actor = $gameActors.actor(actorId);
  if (actor) {
    actor._hp = actor.mhp;
    actor._mp = actor.mmp;
    console.log(\`\${actor.name()} 已完全恢复\`);
  }
}

// 使用示例
checkActorStatus(1);`
  },
  {
    name: '物品管理器',
    type: 'method',
    description: '管理物品的实用工具',
    code: `// 物品管理器
function getInventoryInfo() {
  const info = {
    gold: $gameParty.gold(),
    items: [],
    weapons: [],
    armors: []
  };

  // 统计物品
  for (let i = 1; i < $dataItems.length; i++) {
    const item = $dataItems[i];
    if (item) {
      const num = $gameParty.numItems(item);
      if (num > 0) {
        info.items.push({ name: item.name, count: num });
      }
    }
  }

  // 统计武器
  for (let i = 1; i < $dataWeapons.length; i++) {
    const weapon = $dataWeapons[i];
    if (weapon) {
      const num = $gameParty.numItems(weapon);
      if (num > 0) {
        info.weapons.push({ name: weapon.name, count: num });
      }
    }
  }

  console.log('=== 背包信息 ===');
  console.log('金钱:', info.gold);
  console.log('物品数量:', info.items.length);
  console.log('武器数量:', info.weapons.length);

  return info;
}

function clearInventory() {
  const gold = $gameParty.gold();
  $gameParty.loseGold(gold);
  console.log('背包已清空');
}

// 使用示例
getInventoryInfo();`
  },
  {
    name: '场景管理助手',
    type: 'method',
    description: '场景切换的辅助工具',
    code: `// 场景管理助手
function safeSceneTransition(targetScene, fadeTime = 1000) {
  console.log(\`准备切换到: \${targetScene.name}\`);

  // 保存当前状态
  if ($gameSystem.isSaveEnabled()) {
    $gameSystem.onBeforeSave();
  }

  // 淡出音频
  AudioManager.fadeOutBgm(fadeTime / 1000);

  // 切换场景
  SceneManager.goto(targetScene);

  console.log('场景切换完成');
}

function returnToMap() {
  // 确保地图数据已加载
  if (!DataManager.isMapLoaded()) {
    DataManager.loadMapData($gameMap.mapId());
  }

  // 刷新玩家和地图
  $gamePlayer.refresh();
  $gameMap.refresh();

  // 跳转到地图
  SceneManager.goto(Scene_Map);

  console.log('返回地图');
}

// 使用示例
safeSceneTransition(Scene_Menu);`
  }
];
