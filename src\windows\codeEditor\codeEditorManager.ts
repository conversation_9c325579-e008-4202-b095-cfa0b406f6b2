// 代码编辑器窗口管理器
import { WebviewWindow } from '@tauri-apps/api/webviewWindow';

export interface CodeEditorOptions {
  title: string;
  code: string;
  onSave: (code: string) => void;
}

class CodeEditorManager {
  private static instance: CodeEditorManager;
  private currentWindow: WebviewWindow | null = null;
  private currentOnSave: ((code: string) => void) | null = null;

  private constructor() {}

  public static getInstance(): CodeEditorManager {
    if (!CodeEditorManager.instance) {
      CodeEditorManager.instance = new CodeEditorManager();
    }
    return CodeEditorManager.instance;
  }

  /**
   * 打开代码编辑器窗口
   */
  public async openEditor(options: CodeEditorOptions): Promise<void> {
    console.log('🚀 [DEBUG] openEditor被调用:', {
      title: options.title,
      codeLength: options.code?.length || 0,
      hasOnSave: !!options.onSave,
      stackTrace: new Error().stack
    });

    try {
      // 检查窗口是否存在且存活
      if (this.currentWindow && await this.isWindowAlive()) {
        console.log('🔧 检测到编辑器窗口已存在，尝试更新内容');
        try {
          await this.updateEditorContent(options);
          return;
        } catch (error) {
          console.log('🔧 更新内容失败，窗口可能已关闭，将创建新窗口:', error);
          // 继续执行创建新窗口的逻辑
        }
      }

      // 窗口不存在或更新失败，创建新窗口
      console.log('🔧 创建新的编辑器窗口');

      // 创建新的编辑器窗口，使用时间戳确保唯一性
      const windowLabel = `code-editor-${Date.now()}`;
      console.log('🔧 准备创建窗口:', windowLabel);

      // 使用Promise包装窗口创建过程
      const windowCreationPromise = new Promise<WebviewWindow>((resolve, reject) => {
        const editorWindow = new WebviewWindow(windowLabel, {
          url: 'http://localhost:1420/code-editor',
          title: `代码编辑器 - ${options.title}`,
          width: 900,
          height: 700,
          resizable: true,
          center: true,
          decorations: true,
          transparent: false,
          alwaysOnTop: false
        });

        console.log('🔧 窗口对象创建:', editorWindow.label);

        // 监听窗口创建成功事件
        editorWindow.once('tauri://created', () => {
          console.log('✅ 窗口创建事件触发');
          resolve(editorWindow);
        });

        // 监听窗口创建失败事件
        editorWindow.once('tauri://error', (error) => {
          console.error('❌ 窗口创建失败:', error);
          reject(error);
        });

        // 设置超时，如果5秒内没有创建成功就报错
        setTimeout(() => {
          reject(new Error('窗口创建超时'));
        }, 5000);
      });

      // 等待窗口创建完成
      const editorWindow = await windowCreationPromise;
      this.currentWindow = editorWindow;

      console.log('🔧 窗口创建完成，开始显示和初始化');

      // 现在安全地操作窗口
      try {
        await editorWindow.show();
        console.log('✅ 窗口显示成功，等待编辑器主动请求初始化数据');

      } catch (error) {
        console.error('❌ 窗口操作失败:', error);
      }

      // 监听初始化数据请求
      editorWindow.listen('request-init-data', async () => {
        console.log('📥 收到初始化数据请求，发送数据');
        try {
          await editorWindow.emit('init-code', {
            code: options.code,
            title: options.title
          });
          console.log('✅ 响应初始化数据请求成功');
        } catch (error) {
          console.error('❌ 响应初始化数据请求失败:', error);
        }
      });

      // 监听保存事件
      editorWindow.listen('code-saved', (event: any) => {
        console.log('📥 接收到保存的代码:', event.payload);
        const { code: savedCode, title } = event.payload;
        console.log('🔧 当前保存回调存在:', !!this.currentOnSave);
        console.log('🔧 保存的标题:', title);

        // 使用当前的保存回调
        if (this.currentOnSave) {
          this.currentOnSave(savedCode);
        } else {
          console.warn('⚠️ 没有可用的保存回调');
        }
      }).catch((error) => {
        console.log('设置保存事件监听器失败:', error);
      });

      // 初始化当前保存回调
      this.currentOnSave = options.onSave;

      // 监听窗口关闭事件，清理管理器状态
      editorWindow.onCloseRequested(() => {
        console.log('🔧 编辑器窗口即将关闭，清理管理器状态');
        this.currentWindow = null;
        this.currentOnSave = null;
      });

      console.log('🔧 窗口设置完成，已监听关闭事件');

    } catch (error) {
      console.error('❌ 打开代码编辑器失败:', error);
      this.currentWindow = null;
      throw error;
    }
  }

  /**
   * 关闭当前编辑器窗口
   */
  public async closeEditor(): Promise<void> {
    console.log('🔧 关闭编辑器窗口');

    if (this.currentWindow) {
      try {
        // 尝试关闭窗口
        await this.currentWindow.close();
        console.log('✅ 编辑器窗口已关闭');
      } catch (error) {
        console.log('🔧 关闭窗口失败（可能已关闭）:', error);
        // 继续清理引用
      }
    }

    // 清理引用
    this.currentWindow = null;
    this.currentOnSave = null;
  }

  /**
   * 检查编辑器窗口是否打开
   */
  public isEditorOpen(): boolean {
    return this.currentWindow !== null;
  }

  /**
   * 检查窗口是否存活
   */
  private async isWindowAlive(): Promise<boolean> {
    if (!this.currentWindow) return false;

    try {
      // 使用超时机制检测窗口是否存活
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('ping timeout')), 1000);
      });

      const pingPromise = this.currentWindow.emit('ping');

      await Promise.race([pingPromise, timeoutPromise]);
      return true;
    } catch (error) {
      console.log('🔧 窗口存活检测失败，窗口可能已关闭:', error);
      this.currentWindow = null;
      this.currentOnSave = null;
      return false;
    }
  }

  /**
   * 更新编辑器内容
   */
  private async updateEditorContent(options: CodeEditorOptions): Promise<void> {
    if (!this.currentWindow) {
      throw new Error('编辑器窗口不存在');
    }

    try {
      console.log('🔧 开始更新编辑器内容');

      // 先请求自动保存当前内容（使用当前的保存回调）
      await this.currentWindow.emit('auto-save-request');

      // 等待一下确保保存完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 更新保存回调
      console.log('🔧 更新保存回调，从:', this.currentOnSave ? '有回调' : '无回调', '到:', options.title);
      this.currentOnSave = options.onSave;

      // 发送内容更新事件
      await this.currentWindow.emit('update-code', {
        code: options.code,
        title: options.title
      });

      // 确保窗口可见和聚焦
      await this.currentWindow.setFocus();

      console.log('✅ 编辑器内容更新成功');
    } catch (error) {
      console.error('❌ 更新编辑器内容失败:', error);
      // 如果更新失败，清理窗口引用，下次会重新创建
      this.currentWindow = null;
      this.currentOnSave = null;
      throw new Error('更新编辑器内容失败，窗口可能已关闭: ' + error);
    }
  }

  /**
   * 聚焦到编辑器窗口
   */
  public async focusEditor(): Promise<void> {
    if (this.currentWindow && await this.isWindowAlive()) {
      try {
        // 只设置焦点，不调用show()避免闪烁
        await this.currentWindow.setFocus();
      } catch (error) {
        console.log('聚焦编辑器窗口失败:', error);
      }
    }
  }

  /**
   * 全局清理方法 - 在应用关闭或项目切换时调用
   */
  public async cleanup(): Promise<void> {
    console.log('🔧 CodeEditorManager: 执行全局清理');
    await this.closeEditor();
  }
}

// 导出单例实例
export const codeEditorManager = CodeEditorManager.getInstance();
