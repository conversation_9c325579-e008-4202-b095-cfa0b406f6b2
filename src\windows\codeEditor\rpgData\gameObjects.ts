// 游戏对象 - 运行时游戏状态（会保存到存档）

import type { RPGObject } from './types';

export const gameObjects: RPGObject[] = [
  {
    name: '$gameParty',
    description: '队伍数据',
    category: 'game',
    properties: [
      { name: 'gold', type: 'property', description: '队伍金钱', code: '$gameParty.gold()' },
      { name: 'steps', type: 'property', description: '步数', code: '$gameParty.steps()' }
    ],
    methods: [
      { name: 'members', type: 'method', description: '获取队伍成员', code: '$gameParty.members()' },
      { name: 'leader', type: 'method', description: '获取队长', code: '$gameParty.leader()' },
      { name: 'addActor', type: 'method', description: '添加角色', code: '$gameParty.addActor(1)' },
      { name: 'removeActor', type: 'method', description: '移除角色', code: '$gameParty.removeActor(1)' },
      { name: 'allMembers', type: 'method', description: '获取所有成员', code: '$gameParty.allMembers()' },
      { name: 'movableMembers', type: 'method', description: '获取可移动的队伍成员', code: '$gameParty.movableMembers()' },
      { name: 'setLastItem', type: 'method', description: '设置最后使用的物品', code: '$gameParty.setLastItem($dataItems[1])' },
      { name: 'loseGold', type: 'method', description: '失去金钱', code: '$gameParty.loseGold(100)' },
      { name: 'gainGold', type: 'method', description: '获得金钱', code: '$gameParty.gainGold(100)' },
      { name: 'gainItem', type: 'method', description: '获得物品', code: '$gameParty.gainItem($dataItems[1], 5)' },
      { name: 'loseItem', type: 'method', description: '失去物品', code: '$gameParty.loseItem($dataItems[1], 3)' },
      { name: 'numItems', type: 'method', description: '获取物品数量', code: '$gameParty.numItems($dataItems[1])' },
      { name: 'maxItems', type: 'method', description: '获取物品最大数量', code: '$gameParty.maxItems($dataItems[1])' },
      { name: 'onBattleEnd', type: 'method', description: '战斗结束处理', code: '$gameParty.onBattleEnd()' }
    ]
  },
  {
    name: '$gameSystem',
    description: '系统运行时数据',
    category: 'game',
    properties: [
      { name: 'isAutosaveEnabled', type: 'property', description: '是否启用自动保存', code: '$gameSystem.isAutosaveEnabled()' },
      { name: 'isSaveEnabled', type: 'property', description: '是否允许保存', code: '$gameSystem.isSaveEnabled()' }
    ],
    methods: [
      { name: 'isAutosaveEnabled', type: 'method', description: '检查是否启用自动保存', code: '$gameSystem.isAutosaveEnabled()' },
      { name: 'isSaveEnabled', type: 'method', description: '检查是否允许保存', code: '$gameSystem.isSaveEnabled()' },
      { name: 'onBeforeSave', type: 'method', description: '保存前处理', code: '$gameSystem.onBeforeSave()' },
      { name: 'onAfterLoad', type: 'method', description: '加载后处理', code: '$gameSystem.onAfterLoad()' },
      { name: 'isMenuEnabled', type: 'method', description: '检查是否启用菜单', code: '$gameSystem.isMenuEnabled()' },
      { name: 'savefileId', type: 'method', description: '获取当前存档ID', code: '$gameSystem.savefileId()' },
      { name: 'setSavefileId', type: 'method', description: '设置存档ID', code: '$gameSystem.setSavefileId(1)' },
      { name: 'versionId', type: 'method', description: '获取版本ID', code: '$gameSystem.versionId()' },
      { name: 'windowPadding', type: 'method', description: '获取窗口内边距', code: '$gameSystem.windowPadding()' }
    ]
  },
  {
    name: '$gamePlayer',
    description: '玩家角色对象',
    category: 'game',
    properties: [
      { name: 'isTransferring', type: 'property', description: '是否正在传送', code: '$gamePlayer.isTransferring()' },
      { name: 'canMove', type: 'property', description: '是否可以移动', code: '$gamePlayer.canMove()' },
      { name: 'isMoving', type: 'property', description: '是否正在移动', code: '$gamePlayer.isMoving()' },
      { name: 'x', type: 'property', description: '玩家X坐标', code: '$gamePlayer.x' },
      { name: 'y', type: 'property', description: '玩家Y坐标', code: '$gamePlayer.y' }
    ],
    methods: [
      { name: 'transferPlayer', type: 'method', description: '传送玩家', code: '$gamePlayer.transferPlayer(1, 5, 5, 2)' },
      { name: 'locate', type: 'method', description: '定位玩家', code: '$gamePlayer.locate(10, 10)' },
      { name: 'refresh', type: 'method', description: '刷新玩家', code: '$gamePlayer.refresh()' },
      { name: 'screenX', type: 'method', description: '获取屏幕X坐标', code: '$gamePlayer.screenX()' },
      { name: 'screenY', type: 'method', description: '获取屏幕Y坐标', code: '$gamePlayer.screenY()' },
      { name: 'straighten', type: 'method', description: '停止移动', code: '$gamePlayer.straighten()' },
      { name: 'direction', type: 'method', description: '获取玩家朝向', code: '$gamePlayer.direction()' },
      { name: 'reserveTransfer', type: 'method', description: '预约传送', code: '$gamePlayer.reserveTransfer(1, 5, 5, 2, 0)' },
      { name: 'requestMapReload', type: 'method', description: '请求地图重载', code: '$gamePlayer.requestMapReload()' }
    ]
  },
  {
    name: '$gameMap',
    description: '地图运行时对象',
    category: 'game',
    properties: [
      { name: 'mapId', type: 'property', description: '当前地图ID', code: '$gameMap.mapId()' },
      { name: 'displayName', type: 'property', description: '地图显示名', code: '$gameMap.displayName()' }
    ],
    methods: [
      { name: 'setup', type: 'method', description: '设置地图', code: '$gameMap.setup(1)' },
      { name: 'refresh', type: 'method', description: '刷新地图', code: '$gameMap.refresh()' },
      { name: 'update', type: 'method', description: '更新地图', code: '$gameMap.update(true)' },
      { name: 'event', type: 'method', description: '获取事件', code: '$gameMap.event(1)' }
    ]
  },
  {
    name: '$gameTimer',
    description: '计时器对象',
    category: 'game',
    properties: [
      { name: 'seconds', type: 'property', description: '剩余秒数', code: '$gameTimer.seconds()' }
    ],
    methods: [
      { name: 'start', type: 'method', description: '开始计时', code: '$gameTimer.start(300)' },
      { name: 'stop', type: 'method', description: '停止计时', code: '$gameTimer.stop()' },
      { name: 'update', type: 'method', description: '更新计时器', code: '$gameTimer.update(true)' }
    ]
  },
  {
    name: '$gameScreen',
    description: '屏幕效果对象',
    category: 'game',
    properties: [],
    methods: [
      { name: 'startFlash', type: 'method', description: '开始闪烁', code: '$gameScreen.startFlash([255, 255, 255, 128], 30)' },
      { name: 'startShake', type: 'method', description: '开始震动', code: '$gameScreen.startShake(5, 5, 30)' },
      { name: 'update', type: 'method', description: '更新屏幕效果', code: '$gameScreen.update()' }
    ]
  },
  {
    name: '$gameTemp',
    description: '临时数据对象',
    category: 'game',
    properties: [
      { name: 'isPlaytest', type: 'property', description: '是否为测试模式', code: '$gameTemp.isPlaytest()' }
    ],
    methods: [
      { name: 'reserveCommonEvent', type: 'method', description: '预约公共事件', code: '$gameTemp.reserveCommonEvent(1)' },
      { name: 'isCommonEventReserved', type: 'method', description: '是否有预约的公共事件', code: '$gameTemp.isCommonEventReserved()' }
    ]
  },
  {
    name: '$gameMessage',
    description: '消息系统对象',
    category: 'game',
    properties: [
      { name: 'isBusy', type: 'property', description: '是否正在显示消息', code: '$gameMessage.isBusy()' }
    ],
    methods: []
  },
  {
    name: '$gameTroop',
    description: '敌群对象 - 管理战斗中的敌人',
    category: 'game',
    properties: [
      { name: 'isAllDead', type: 'property', description: '所有敌人是否死亡', code: '$gameTroop.isAllDead()' },
      { name: 'isEventRunning', type: 'property', description: '事件是否正在运行', code: '$gameTroop.isEventRunning()' }
    ],
    methods: [
      { name: 'setup', type: 'method', description: '设置敌群', code: '$gameTroop.setup(1)' },
      { name: 'members', type: 'method', description: '获取敌人成员', code: '$gameTroop.members()' },
      { name: 'aliveMembers', type: 'method', description: '获取存活的敌人', code: '$gameTroop.aliveMembers()' },
      { name: 'deadMembers', type: 'method', description: '获取死亡的敌人', code: '$gameTroop.deadMembers()' },
      { name: 'onBattleEnd', type: 'method', description: '战斗结束处理', code: '$gameTroop.onBattleEnd()' },
      { name: 'expTotal', type: 'method', description: '获得总经验值', code: '$gameTroop.expTotal()' },
      { name: 'goldTotal', type: 'method', description: '获得总金钱', code: '$gameTroop.goldTotal()' },
      { name: 'makeDropItems', type: 'method', description: '生成掉落物品', code: '$gameTroop.makeDropItems()' }
    ]
  }
];
