// 🔍 调试脚本执行的测试脚本
// 在浏览器控制台中运行此脚本来检查脚本执行状态

function debugScriptExecution() {
    console.log('🔍 ===== 脚本执行调试信息 =====');
    
    // 1. 检查UIScriptManager是否存在
    console.log('1. UIScriptManager存在:', !!window.UIScriptManager);
    
    // 2. 检查场景中的UI组件
    if (window.SceneManager && window.SceneManager._scene) {
        const scene = window.SceneManager._scene;
        console.log('2. 当前场景:', scene.constructor.name);
        
        // 查找UI组件
        const findUIComponents = (container, depth = 0) => {
            const components = [];
            if (depth > 5) return components; // 防止无限递归
            
            if (container.children) {
                for (const child of container.children) {
                    if (child.isUIComponent) {
                        components.push({
                            type: child.constructor.name,
                            name: child.name || 'unnamed',
                            hasExecuteScript: typeof child.executeScript === 'function',
                            hasExecuteScriptMethod: typeof child.executeScriptMethod === 'function',
                            hasComponentScripts: !!child.componentScripts,
                            scriptsCount: child.componentScripts ? child.componentScripts.length : 0,
                            enabledScriptsCount: child.getEnabledScripts ? child.getEnabledScripts().length : 0
                        });
                    }
                    // 递归查找子组件
                    components.push(...findUIComponents(child, depth + 1));
                }
            }
            return components;
        };
        
        const uiComponents = findUIComponents(scene);
        console.log('3. 找到的UI组件:', uiComponents);
        
        // 3. 测试第一个UI组件的脚本执行
        if (uiComponents.length > 0) {
            const firstComponent = scene.children.find(child => child.isUIComponent);
            if (firstComponent) {
                console.log('4. 测试第一个UI组件的脚本执行:');
                console.log('   - 组件类型:', firstComponent.constructor.name);
                console.log('   - executeScript方法存在:', typeof firstComponent.executeScript === 'function');
                console.log('   - componentScripts存在:', !!firstComponent.componentScripts);
                
                if (firstComponent.componentScripts) {
                    console.log('   - 脚本数量:', firstComponent.componentScripts.length);
                    firstComponent.componentScripts.forEach((script, index) => {
                        console.log(`   - 脚本${index + 1}:`, {
                            name: script.name,
                            enabled: script.enabled,
                            hasCode: !!script.code,
                            codeLength: script.code ? script.code.length : 0
                        });
                    });
                }
                
                // 尝试执行onStart方法
                if (typeof firstComponent.executeScript === 'function') {
                    console.log('5. 尝试执行onStart方法...');
                    try {
                        firstComponent.executeScript('onStart');
                        console.log('✅ onStart执行完成');
                    } catch (error) {
                        console.error('❌ onStart执行失败:', error);
                    }
                }
            }
        }
    } else {
        console.log('2. 当前没有活动场景');
    }
    
    console.log('🔍 ===== 调试信息结束 =====');
}

// 运行调试
debugScriptExecution();
