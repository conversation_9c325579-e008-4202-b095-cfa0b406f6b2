<script lang="ts">
  /**
   * 响应式滑动条属性面板 - 简化版本
   * 直接使用响应式模型对象，无需额外的函数
   */

  import {
    sceneModelState
  } from '../../../stores/sceneModelStore';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import LabelInput from '../../../components/LabelInput.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import DropTarget from '../../../components/drop/DropTarget.svelte';

  import type { SliderModel } from '../../../type/ui/sliderModel.svelte';
  import type { BaseObjectModel } from '../../../type/baseObjectModel.svelte';

  // 监听选中的对象 - 使用第一个选中的对象
  let currentState = $derived($sceneModelState);
  let slider = $derived(currentState.selectedObjects[0] as SliderModel | null);

  // 面板展开状态
  let isExpanded = $state(true);
  let isLayoutExpanded = $state(true);

  // 方向选项已删除



  // 🔑 删除bindingInfo，直接使用模型对象中的字段

  // 处理拖拽绑定 - handleTrackDrop已删除

  function handleRangeDrop(object: BaseObjectModel) {
    if (!slider) return;
    console.log('🎯 绑定范围精灵:', object.className);

    // 🔑 绑定模型对象，不是原始对象
    slider.boundRangeSprite = object;

    // 🔑 确保绑定的对象是Slider的子对象（用于代码生成）
    if (!slider.children.includes(object)) {
      slider.addChild(object);
      console.log('🔗 Slider面板: 将范围精灵添加为子对象');
    }

    console.log('✅ 范围精灵绑定完成，模型已更新');
  }

  function handleThumbDrop(object: BaseObjectModel) {
    if (!slider) return;
    console.log('🎯 绑定滑块精灵:', object.className);

    // 🔑 绑定模型对象，不是原始对象
    slider.boundThumbSprite = object;

    // 🔑 确保绑定的对象是Slider的子对象（用于代码生成）
    if (!slider.children.includes(object)) {
      slider.addChild(object);
      console.log('🔗 Slider面板: 将滑块精灵添加为子对象');
    }

    console.log('✅ 滑块精灵绑定完成，模型已更新');
  }

  function handleProgressDrop(object: BaseObjectModel) {
    if (!slider) return;
    console.log('🎯 绑定进度精灵:', object.className);

    // 🔑 绑定模型对象，不是原始对象
    slider.boundProgressSprite = object;

    // 🔑 确保绑定的对象是Slider的子对象（用于代码生成）
    if (!slider.children.includes(object)) {
      slider.addChild(object);
      console.log('🔗 Slider面板: 将进度精灵添加为子对象');
    }

    console.log('✅ 进度精灵绑定完成，模型已更新');
  }

  function handleLabelDrop(object: BaseObjectModel) {
    if (!slider) return;
    console.log('🎯 绑定文本精灵:', object.className);

    // 🔑 绑定模型对象，不是原始对象
    slider.boundLabelSprite = object;

    // 🔑 确保绑定的对象是Slider的子对象（用于代码生成）
    if (!slider.children.includes(object)) {
      slider.addChild(object);
      console.log('🔗 Slider面板: 将文本精灵添加为子对象');
    }

    console.log('✅ 文本精灵绑定完成，模型已更新');
  }

  // ==================== 🎯 布局控制函数 ====================

  /**
   * 🎯 计算并应用Slider布局 - 直接设置绑定对象的坐标
   */
  function performSliderLayout() {
    if (!slider) {
      console.warn('🚨 Slider面板: 没有选中的Slider对象');
      return;
    }

    // 🔑 至少需要范围精灵才能执行布局
    if (!slider.boundRangeSprite) {
      console.warn('🚨 Slider面板: 需要范围精灵才能执行布局');
      return;
    }

    // 🔑 获取绑定的模型对象
    const rangeModel = slider.boundRangeSprite;
    const thumbModel = slider.boundThumbSprite;
    const progressModel = slider.boundProgressSprite;

    // 计算布局坐标
    const rangeWidth = rangeModel.width || 200;
    const rangeHeight = rangeModel.height || 20;

    // 🔑 设置范围精灵的坐标（作为基准）
    rangeModel.x = 0;
    rangeModel.y = 0;

    // 🔑 设置滑块精灵的坐标（如果有滑块的话）
    if (thumbModel) {
      const thumbWidth = thumbModel.width || 20;
      const thumbHeight = thumbModel.height || 20;
      const progress = (slider.value - slider.minValue) / (slider.maxValue - slider.minValue);
      const thumbX = progress * (rangeWidth - thumbWidth);
      const thumbY = (rangeHeight - thumbHeight) / 2;

      thumbModel.x = thumbX;
      thumbModel.y = thumbY;
    }

    // 🔑 设置进度精灵的坐标（如果有的话）
    if (progressModel) {
      progressModel.x = 0;
      progressModel.y = 0;
      // 进度精灵与范围精灵对齐，不修改宽度（由动画控制）
    }

    console.log('🎯 Slider面板: 布局完成', {
      range: `(${rangeModel.x}, ${rangeModel.y})`,
      thumb: thumbModel ? `(${thumbModel.x}, ${thumbModel.y})` : '无滑块',
      progress: progressModel ? `(${progressModel.x}, ${progressModel.y})` : '无进度条',
      currentValue: slider.value
    });
  }

  /**
   * 🎯 获取当前绑定对象的坐标信息
   */
  function getCurrentCoordinates() {
    if (!slider) return null;

    const coords: any = {};

    if (slider.boundRangeSprite) {
      coords.rangeX = slider.boundRangeSprite.x || 0;
      coords.rangeY = slider.boundRangeSprite.y || 0;
    }

    if (slider.boundThumbSprite) {
      coords.thumbX = slider.boundThumbSprite.x || 0;
      coords.thumbY = slider.boundThumbSprite.y || 0;
    }

    if (slider.boundProgressSprite) {
      coords.progressX = slider.boundProgressSprite.x || 0;
      coords.progressY = slider.boundProgressSprite.y || 0;
    }

    return Object.keys(coords).length > 0 ? coords : null;
  }

  /**
   * 🎯 设置绑定对象的坐标
   */
  function setCoordinate(objectType: 'range' | 'thumb' | 'progress', axis: 'x' | 'y', value: number) {
    if (!slider) return;

    if (objectType === 'range' && slider.boundRangeSprite) {
      slider.boundRangeSprite[axis] = value;
    } else if (objectType === 'thumb' && slider.boundThumbSprite) {
      slider.boundThumbSprite[axis] = value;
    } else if (objectType === 'progress' && slider.boundProgressSprite) {
      slider.boundProgressSprite[axis] = value;
    }
  }

  // 响应式获取当前坐标
  let currentCoords = $derived(getCurrentCoordinates());

  // 解除绑定 - unbindTrack已删除

  function unbindRange() {
    if (!slider) return;
    slider.boundRangeSprite = null;
    console.log('🔓 范围精灵绑定已解除，模型已更新');
  }

  function unbindThumb() {
    if (!slider) return;
    slider.boundThumbSprite = null;
    console.log('🔓 滑块精灵绑定已解除，模型已更新');
  }

  function unbindProgress() {
    if (!slider) return;
    slider.boundProgressSprite = null;
    console.log('🔓 进度精灵绑定已解除，模型已更新');
  }

  function unbindLabel() {
    if (!slider) return;
    slider.boundLabelSprite = null;
    console.log('🔓 文本精灵绑定已解除，模型已更新');
  }




  console.log('SliderPanel 组件已加载');
</script>

{#if slider}
  <AccordionPanel
    title="滑动条属性"
    icon="🎚️"
    badge="Slider"
    badgeVariant="info"
    bind:expanded={isExpanded}
  >
    <!-- 数值设置 -->
    <PropertyContainer>
        <Label text="当前值:" />
        <LabelInput
          bind:value={slider.value}
          type="number"
          min={slider.minValue}
          max={slider.maxValue}
          step={slider.step}
          targetObject={slider}
          fieldName="value"
          name="当前值"
        />
      </PropertyContainer>

      <PropertyContainer>
        <Label text="最小值:" />
        <LabelInput
          bind:value={slider.minValue}
          type="number"
          targetObject={slider}
          fieldName="minValue"
          name="最小值"
        />
      </PropertyContainer>

      <PropertyContainer>
        <Label text="最大值:" />
        <LabelInput
          bind:value={slider.maxValue}
          type="number"
          targetObject={slider}
          fieldName="maxValue"
          name="最大值"
        />
      </PropertyContainer>

      <PropertyContainer>
        <Label text="步长:" />
        <LabelInput
          bind:value={slider.step}
          type="number"
          min={0.1}
          step={0.1}
          targetObject={slider}
          fieldName="step"
          name="步长"
        />
      </PropertyContainer>

      <!-- 🔑 编辑器事件控制 -->
      <PropertyContainer>
        <Label text="编辑器中执行事件:" />
        <Checkbox
          checked={slider.executeEventsInEditor}
          targetObject={slider}
          fieldName="executeEventsInEditor"
          enableHistory={true}
          name="编辑器中执行事件"
          onChange={(checked) => {
            console.log("🔧 Slider面板: 编辑器事件执行变化", {from: slider.executeEventsInEditor, to: checked});
            slider.executeEventsInEditor = checked;
          }}
        />
        <Label text={slider.executeEventsInEditor ? '启用' : '禁用'} size="sm" variant="secondary" />
      </PropertyContainer>

    <!-- 🔑 子组件绑定状态 -->
    <div class="property-section">
      <h4>🔗 子组件绑定</h4>

      <!-- 轨道精灵绑定已删除 -->

      <!-- 范围精灵绑定 ⭐ 新增 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">范围精灵 (UIImage) - 定义有效范围:</span>
          <span class="binding-status-badge {slider.boundRangeSprite ? 'bound' : 'unbound'}">
            {slider.boundRangeSprite ? '已绑定' : '未绑定'}
          </span>
          {#if slider.boundRangeSprite}
            <button class="unbind-btn" onclick={unbindRange} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if slider.boundRangeSprite}
          <div class="bound-object-info">
            已绑定: {slider.boundRangeSprite?.className || '未知对象'}
            <div class="range-info">
              🔑 此精灵的长度决定滑动条的总长度
            </div>
          </div>
        {:else}
          <DropTarget
            onDrop={handleRangeDrop}
            placeholder="拖拽 UIImage 对象到此处定义滑动范围"
            targetObject={slider}
            fieldName="boundRangeSprite"
            enableHistory={true}
            operationName="绑定滑动条范围精灵"
          />
        {/if}
      </div>

      <!-- 滑块精灵绑定 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">滑块精灵 (UIImage):</span>
          <span class="binding-status-badge {slider.boundThumbSprite ? 'bound' : 'unbound'}">
            {slider.boundThumbSprite ? '已绑定' : '未绑定'}
          </span>
          {#if slider.boundThumbSprite}
            <button class="unbind-btn" onclick={unbindThumb} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if slider.boundThumbSprite}
          <div class="bound-object-info">
            已绑定: {slider.boundThumbSprite?.className || '未知对象'}
          </div>
        {:else}
          <DropTarget
            onDrop={handleThumbDrop}
            placeholder="拖拽 UIImage 对象到此处绑定为滑块"
            targetObject={slider}
            fieldName="boundThumbSprite"
            enableHistory={true}
            operationName="绑定滑动条滑块精灵"
          />
        {/if}
      </div>

      <!-- 进度精灵绑定 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">进度精灵 (UIImage):</span>
          <span class="binding-status-badge {slider.boundProgressSprite ? 'bound' : 'unbound'}">
            {slider.boundProgressSprite ? '已绑定' : '未绑定'}
          </span>
          {#if slider.boundProgressSprite}
            <button class="unbind-btn" onclick={unbindProgress} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if slider.boundProgressSprite}
          <div class="bound-object-info">
            已绑定: {slider.boundProgressSprite?.className || '未知对象'}
          </div>
        {:else}
          <DropTarget
            onDrop={handleProgressDrop}
            placeholder="拖拽 UIImage 对象到此处绑定为进度条"
            targetObject={slider}
            fieldName="boundProgressSprite"
            enableHistory={true}
            operationName="绑定滑动条进度精灵"
          />
        {/if}
      </div>

      <!-- 文本精灵绑定 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">文本精灵 (UILabel):</span>
          <span class="binding-status-badge {slider.boundLabelSprite ? 'bound' : 'unbound'}">
            {slider.boundLabelSprite ? '已绑定' : '未绑定'}
          </span>
          {#if slider.boundLabelSprite}
            <button class="unbind-btn" onclick={unbindLabel} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if slider.boundLabelSprite}
          <div class="bound-object-info">
            已绑定: {slider.boundLabelSprite?.className || '未知对象'}
          </div>
        {:else}
          <DropTarget
            onDrop={handleLabelDrop}
            placeholder="拖拽 UILabel 对象到此处绑定为标签"
            targetObject={slider}
            fieldName="boundLabelSprite"
            enableHistory={true}
            operationName="绑定滑动条标签精灵"
          />
        {/if}
      </div>

      <div class="binding-info">
        <p class="binding-hint">
          💡 从左侧对象树拖拽精灵对象到上方区域进行绑定
        </p>
      </div>
    </div>

    <!-- 行为设置 -->
    <div class="property-section">
      <h4>⚙️ 行为设置</h4>

      <PropertyContainer>
        <Label text="启用状态:" />
        <Checkbox
          checked={slider.enabled}
          targetObject={slider}
          fieldName="enabled"
          enableHistory={true}
          name="启用状态"
          onChange={(checked) => {
            console.log("🔧 Slider面板: 启用状态变化", {from: slider.enabled, to: checked});
            slider.enabled = checked;
          }}
        />
        <Label text={slider.enabled ? '启用' : '禁用'} size="sm" variant="secondary" />
      </PropertyContainer>

      <!-- 方向选择已删除 -->
    </div>

  </AccordionPanel>
{/if}

<!-- 🎯 布局控制面板 -->
{#if slider}
  <AccordionPanel title="🎯 布局控制" bind:expanded={isLayoutExpanded}>
    <div class="layout-section">
      <h4>🎯 自动布局</h4>

      <PropertyContainer>
        <button
          class="layout-btn primary"
          onclick={performSliderLayout}
          disabled={!slider.boundRangeSprite}
        >
          🎯 执行布局
        </button>
      </PropertyContainer>

      {#if !slider.boundRangeSprite}
        <div class="layout-warning">
          ⚠️ 需要绑定范围精灵才能执行布局
        </div>
      {/if}
    </div>

    {#if currentCoords}
      <div class="layout-section">
        <h4>📍 当前坐标</h4>

        <!-- 范围精灵坐标 -->
        {#if slider.boundRangeSprite}
          <PropertyContainer>
            <Label text="范围位置:" />
            <div class="coordinate-inputs">
              <input
                type="number"
                value={currentCoords.rangeX}
                onchange={(e) => setCoordinate('range', 'x', Number((e.target as HTMLInputElement)?.value || 0))}
                placeholder="X"
              />
              <input
                type="number"
                value={currentCoords.rangeY}
                onchange={(e) => setCoordinate('range', 'y', Number((e.target as HTMLInputElement)?.value || 0))}
                placeholder="Y"
              />
            </div>
          </PropertyContainer>
        {/if}

        <!-- 滑块坐标 -->
        {#if slider.boundThumbSprite}
          <PropertyContainer>
            <Label text="滑块位置:" />
            <div class="coordinate-inputs">
              <input
                type="number"
                value={currentCoords.thumbX}
                onchange={(e) => setCoordinate('thumb', 'x', Number((e.target as HTMLInputElement)?.value || 0))}
                placeholder="X"
              />
              <input
                type="number"
                value={currentCoords.thumbY}
                onchange={(e) => setCoordinate('thumb', 'y', Number((e.target as HTMLInputElement)?.value || 0))}
                placeholder="Y"
              />
            </div>
          </PropertyContainer>
        {/if}

        <!-- 进度精灵坐标 -->
        {#if slider.boundProgressSprite}
          <PropertyContainer>
            <Label text="进度位置:" />
            <div class="coordinate-inputs">
              <input
                type="number"
                value={currentCoords.progressX}
                onchange={(e) => setCoordinate('progress', 'x', Number((e.target as HTMLInputElement)?.value || 0))}
                placeholder="X"
              />
              <input
                type="number"
                value={currentCoords.progressY}
                onchange={(e) => setCoordinate('progress', 'y', Number((e.target as HTMLInputElement)?.value || 0))}
                placeholder="Y"
              />
            </div>
          </PropertyContainer>
        {/if}
      </div>
    {/if}
  </AccordionPanel>
{:else}
  <div class="no-selection">
    <p>请选择一个滑动条对象来查看其属性</p>
  </div>
{/if}

<style>
  /* 基础样式 */
  .property-section {
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 4px;
    padding: 8px;
    border: 1px solid var(--theme-border-light, #e9ecef);
    margin-bottom: 8px;
  }

  .property-section h4 {
    margin: 0 0 8px 0;
    font-size: 11px;
    font-weight: 600;
    color: var(--theme-text, #1a202c);
  }



  /* 🔑 绑定状态样式 */
  .binding-item {
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
  }

  .binding-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    font-size: 10px;
  }

  .bound-object-info {
    font-size: 9px;
    color: var(--theme-text-secondary, #666);
    padding: 4px 6px;
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 3px;
    border: 1px solid var(--theme-border-light, #e9ecef);
  }

  .unbind-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 2px;
    padding: 2px 4px;
    font-size: 8px;
    cursor: pointer;
    transition: background-color 0.15s ease;
  }

  .unbind-btn:hover {
    background: #c82333;
  }

  .binding-label {
    font-weight: 500;
    color: var(--theme-text, #1a202c);
  }

  .binding-status-badge {
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 9px;
    font-weight: 500;
    text-transform: uppercase;
  }

  .binding-status-badge.bound {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .binding-status-badge.unbound {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  .binding-info {
    margin-top: 8px;
  }

  .binding-hint {
    font-size: 9px;
    color: var(--theme-text-secondary, #718096);
    margin: 0;
    line-height: 1.4;
    font-style: italic;
  }

  /* 范围精灵特殊信息样式 */
  .range-info {
    font-size: 9px;
    color: var(--theme-accent, #3182ce);
    margin-top: 4px;
    padding: 2px 6px;
    background: var(--theme-accent-light, #ebf8ff);
    border-radius: 2px;
    border-left: 2px solid var(--theme-accent, #3182ce);
    font-weight: 500;
  }



  .no-selection {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 16px;
    text-align: center;
    color: var(--theme-text-secondary, #718096);
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 6px;
    border: 2px dashed var(--theme-border, #e2e8f0);
  }

  .no-selection p {
    margin: 0;
    font-size: 12px;
  }


  /* 布局控制样式 */
  .layout-section {
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 4px;
    padding: 8px;
    border: 1px solid var(--theme-border-light, #e9ecef);
    margin-bottom: 8px;
  }

  .layout-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    margin-right: 8px;
    transition: all 0.2s ease;
  }

  .layout-btn.primary {
    background: #007bff;
    color: white;
  }

  .layout-btn.primary:hover:not(:disabled) {
    background: #0056b3;
  }

  .layout-btn:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
  }

  .layout-warning {
    background: #fff3cd;
    color: #856404;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ffeaa7;
    font-size: 10px;
    margin-top: 8px;
  }

  .coordinate-inputs {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .coordinate-inputs input {
    flex: 1;
    min-width: 60px;
    padding: 4px 8px;
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 4px;
    font-size: 11px;
  }
</style>
