//=============================================================================
// switch.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc UISwitch Plugin v1.0.0
 * <AUTHOR> Editor
 * @version 1.0.0
 * @description 提供开关组件，支持两态切换
 *
 * @help switch.js
 *
 * 这个插件提供了UISwitch开关组件：
 * - 简单的开/关两态切换
 * - 支持点击切换和动画过渡
 * - 通过绑定UIImage组件实现视觉效果
 *
 * 核心组件：
 * - boundBackgroundSprite: 背景轨道 (UIImage) - 定义开关外观和移动范围
 * - boundKnobSprite: 滑块按钮 (UIImage) - 可移动的开关按钮
 *
 * 使用方法：
 * const mySwitch = new UISwitch({
 *     isOn: false,
 *     animationDuration: 200
 * });
 *
 * // 绑定组件
 * mySwitch.bindBackgroundSprite(backgroundImage);
 * mySwitch.bindKnobSprite(knobImage);
 *
 * // 监听状态变化
 * mySwitch.onChange = (isOn) => {
 *     console.log('开关状态:', isOn ? '开启' : '关闭');
 * };
 *
 * // 切换状态
 * mySwitch.toggle();
 * mySwitch.setValue(true);
 */

(() => {
    'use strict';

    // 确保PIXI可用
    if (typeof PIXI === 'undefined') {
        console.error('🔘 UISwitch: PIXI未定义，插件加载失败');
        return;
    }

    /**
     * UISwitch类 - 开关组件
     * 继承自Container，管理背景轨道和滑块按钮
     * 通过属性面板绑定背景精灵和滑块精灵
     * 只负责开/关状态逻辑，文本由用户自定义
     */
    class UISwitch extends PIXI.Container {
        constructor(properties = {}) {
            super();

            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'Switch';

            this.initializeSwitch(properties);
        }

        /**
         * 初始化开关
         * @param {Object} properties 开关属性
         */
        initializeSwitch(properties) {
            // 设置默认属性
            this.setupDefaultProperties(properties);

            // 初始化容器
            this.initializeContainer();

            // 绑定事件
            this.setupEventListeners();

            // 初始更新
            this.updateSwitch();

            console.log('🔘 UISwitch: 开关创建完成', {
                isOn: this.isOn,
                animationDuration: this.animationDuration
            });
        }

        /**
         * 设置默认属性
         */
        setupDefaultProperties(properties) {
            // 状态属性
            this.isOn = Boolean(properties.isOn || false);
            this.value = this.isOn; // 🔑 添加 value 属性，与 isOn 同步
            this.enabled = properties.enabled !== false;

            // 🔑 核心绑定组件（仅需2个）
            this.boundBackgroundSprite = properties.boundBackgroundSprite || null; // 背景轨道 (UIImage)
            this.boundKnobSprite = properties.boundKnobSprite || null;             // 滑块按钮 (UIImage)

            // 动画属性
            this.animationDuration = properties.animationDuration || 200; // 切换动画时长(ms)
            this.isAnimating = false;

            // 尺寸属性（由背景轨道决定）
            this.switchWidth = properties.width || 60;
            this.switchHeight = properties.height || 30;

            // 回调函数
            this.onChange = properties.onChange || null;
            this.onToggle = properties.onToggle || null;

            // 事件代码属性（用于代码生成）
            this._eventCodes = properties._eventCodes || {};

            // 🔑 统一的脚本系统
            this.componentScripts = properties.componentScripts || [];

            // 🔑 事件执行控制（默认禁用，可在属性面板中启用用于调试）
            this.executeEventsInEditor = properties.executeEventsInEditor || false;

            // 🔑 初始化脚本管理器
            this.initializeScriptManager();

            console.log('🔘 UISwitch: 属性初始化完成', {
                isOn: this.isOn,
                enabled: this.enabled,
                size: `${this.switchWidth}x${this.switchHeight}`
            });
        }

        /**
         * 🔑 初始化脚本管理器
         */
        initializeScriptManager() {
            if (typeof window.UIScriptManager !== 'undefined') {
                // 使用 UIScriptManager 为 UISwitch 添加脚本功能
                window.UIScriptManager.applyToObject(this, { componentScripts: this.componentScripts });
                console.log('✅ UISwitch: 脚本管理器初始化完成');
            } else {
                console.warn('⚠️ UISwitch: UIScriptManager 不可用');
            }
        }

        /**
         * 初始化容器
         */
        initializeContainer() {
            // 容器本身不需要交互，交互由背景轨道精灵处理
            this.interactive = false;
            this.interactiveChildren = true; // 允许子组件交互

            // 设置容器的基础尺寸
            this.containerWidth = this.switchWidth;
            this.containerHeight = this.switchHeight;

            console.log('✅ UISwitch: 容器初始化完成', {
                interactive: this.interactive,
                interactiveChildren: this.interactiveChildren,
                size: `${this.containerWidth}x${this.containerHeight}`
            });
        }

        /**
         * 设置事件监听器 - 使用RPG Maker MZ的TouchInput系统
         */
        setupEventListeners() {
            // 初始化状态
            this._pressed = false;
            this._wasPressed = false;

            // 🔑 使用 RPG Maker MZ 的循环事件系统（参考 UIButton）
            this.setupRPGMakerEventLoop();

            console.log('🔘 UISwitch: 事件监听器设置完成 - 使用tick系统');
        }

        /**
         * 🔑 设置 RPG Maker MZ 的循环事件系统（参考 UIButton）
         */
        setupRPGMakerEventLoop() {
            // 🔑 注册到 Graphics.app.ticker（PIXI 渲染循环）
            if (typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                this._tickerCallback = () => this.processSwitchTouch();
                Graphics.app.ticker.add(this._tickerCallback);
                console.log('✅ UISwitch: 已注册到 PIXI ticker 循环');
                return;
            }

            console.warn('⚠️ UISwitch: 无法注册到 RPG Maker MZ 循环');
        }

        /**
         * 处理开关触摸事件 - 使用RPG Maker MZ的TouchInput系统
         */
        processSwitchTouch() {
            if (!this.enabled || !this.visible) return;

            if (typeof TouchInput === 'undefined') return;

            // 🔑 在编辑器模式下且未启用事件执行时，不处理鼠标事件
            if (window.EDITOR_MODE && !this.executeEventsInEditor) {
                return;
            }

            const isCurrentlyTouched = this.isBeingTouched();

            // 处理点击事件 - 只在刚按下时触发
            if (isCurrentlyTouched && TouchInput.isTriggered()) {
                this.onSwitchPress();
            }

            // 处理释放事件
            if (this._pressed && TouchInput.isReleased()) {
                this.onSwitchRelease();
            }
        }

        /**
         * 检查是否被触摸
         */
        isBeingTouched() {
            if (typeof TouchInput === 'undefined') return false;

            // 获取开关在屏幕上的位置
            const bounds = this.getBounds ? this.getBounds() : {
                x: this.x,
                y: this.y,
                width: this.switchWidth,
                height: this.switchHeight
            };
            const touchX = TouchInput.x;
            const touchY = TouchInput.y;

            return touchX >= bounds.x && touchX <= bounds.x + bounds.width &&
                   touchY >= bounds.y && touchY <= bounds.y + bounds.height;
        }

        /**
         * 碰撞检测
         */
        hitTest(x, y) {
            return x >= 0 && x <= this.switchWidth && y >= 0 && y <= this.switchHeight;
        }

        /**
         * 开关按下事件
         */
        onSwitchPress() {
            if (!this.enabled || this.isAnimating) {
                console.log('🔘 UISwitch: 开关被禁用或正在动画中，忽略按下');
                return;
            }

            console.log('🔘 UISwitch: 开关按下');
            this._pressed = true;
        }

        /**
         * 开关释放事件
         */
        onSwitchRelease() {
            if (!this.enabled) return;

            console.log('🔘 UISwitch: 开关释放');

            if (this._pressed) {
                // 处理点击事件
                this.handleSwitchClick();
            }

            this._pressed = false;
        }

        /**
         * 处理开关点击
         */
        handleSwitchClick() {
            console.log('🔘 UISwitch: 开关被点击');

            // 🔑 执行 onClick 脚本（新的脚本系统）
            if (this.executeScript && typeof this.executeScript === 'function') {
                try {
                    this.executeScript('onClick', this.isOn);
                    console.log(`🔄 UISwitch: onClick 脚本执行完成`);
                } catch (error) {
                    console.error('❌ UISwitch: onClick 脚本执行失败', error);
                }
            }

            // 切换状态
            this.toggle();

            // 播放音效
            if (typeof SoundManager !== 'undefined') {
                SoundManager.playCursor();
            }
        }

        /**
         * 🔑 核心方法：更新开关状态
         */
        updateSwitch() {
            // 🔑 执行动画（使用属性面板设置的坐标）
            this.updateKnobPosition();

            // 🔑 更新背景状态
            if (this.boundBackgroundSprite) {
                requestAnimationFrame(() => {
                    this.updateBackground();
                });
            }
        }

        /**
         * 🎯 纯动画方法：直接修改绑定对象坐标实现动画
         */
        updateKnobPosition() {
            if (!this.boundKnobSprite) return;

            // 🔑 获取当前坐标作为起点
            const currentX = this.boundKnobSprite.x;
            const currentY = this.boundKnobSprite.y;

            // 🔑 计算目标坐标：根据背景和滑块尺寸
            if (!this.boundBackgroundSprite) return;

            const trackWidth = this.boundBackgroundSprite.width || 100;
            const knobWidth = this.boundKnobSprite.width || 20;
            const trackHeight = this.boundBackgroundSprite.height || 40;
            const knobHeight = this.boundKnobSprite.height || 20;

            const targetX = this.isOn ? Math.max(0, trackWidth - knobWidth) : 0;
            const targetY = (trackHeight - knobHeight) / 2;

            // 🔑 执行动画：直接修改绑定对象坐标
            if (this.animationDuration > 0 && !this.isAnimating) {
                this.animateKnobTo(targetX, targetY);
            } else {
                // 直接设置到目标位置
                this.boundKnobSprite.x = targetX;
                this.boundKnobSprite.y = targetY;
            }

            console.log('🔘 UISwitch: 滑块动画', {
                isOn: this.isOn,
                from: `(${currentX}, ${currentY})`,
                to: `(${targetX}, ${targetY})`
            });
        }

        /**
         * 更新背景状态（可选，用于状态相关的视觉变化）
         */
        updateBackground() {
            if (!this.boundBackgroundSprite) return;

            // 🔑 避免不必要的alpha变化，减少闪烁
            const targetAlpha = this.enabled ? 1.0 : 0.5;
            if (Math.abs(this.boundBackgroundSprite.alpha - targetAlpha) > 0.01) {
                this.boundBackgroundSprite.alpha = targetAlpha;

                console.log('🔘 UISwitch: 背景状态更新', {
                    isOn: this.isOn,
                    alpha: this.boundBackgroundSprite.alpha
                });
            }
        }

        /**
         * 滑块动画
         */
        animateKnobTo(targetX, targetY) {
            if (!this.boundKnobSprite) return;

            this.isAnimating = true;
            const startX = this.boundKnobSprite.x;
            const startY = this.boundKnobSprite.y;
            const deltaX = targetX - startX;
            const deltaY = targetY - startY;
            const startTime = Date.now();

            console.log('🎬 UISwitch: 开始滑块动画', {
                from: `(${startX}, ${startY})`,
                to: `(${targetX}, ${targetY})`,
                duration: this.animationDuration
            });

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / this.animationDuration, 1);

                // 使用缓动函数
                const easeProgress = this.easeInOutCubic(progress);
                this.boundKnobSprite.x = startX + deltaX * easeProgress;
                this.boundKnobSprite.y = startY + deltaY * easeProgress;

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    this.isAnimating = false;
                    console.log('✅ UISwitch: 滑块动画完成');
                }
            };

            animate();
        }

        /**
         * 缓动函数：三次贝塞尔曲线
         */
        easeInOutCubic(t) {
            return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        }



        /**
         * 🔑 设置开关状态
         */
        setValue(isOn) {
            const oldValue = this.isOn;
            this.isOn = Boolean(isOn);
            this.value = this.isOn; // 🔑 同步 value 属性

            console.log('🔘 UISwitch: 设置状态', {
                oldValue: oldValue,
                newValue: this.isOn,
                changed: this.isOn !== oldValue
            });

            if (this.isOn !== oldValue) {
                this.updateSwitch();

                // 🔑 执行 onChange 脚本（新的脚本系统）
                if (this.executeScript && typeof this.executeScript === 'function') {
                    try {
                        this.executeScript('onChange', this.isOn, oldValue);
                        console.log(`🔄 UISwitch: onChange 脚本执行完成 ${oldValue} → ${this.isOn}`);
                    } catch (error) {
                        console.error('❌ UISwitch: onChange 脚本执行失败', error);
                    }
                }

                // 执行回调函数
                if (this.onChange) {
                    this.onChange(this.isOn, oldValue);
                }

                // 🔑 执行事件代码（保持兼容性）
                this.executeEvent('onChangeCode');
                this.executeEvent('onToggleCode');
            }
        }

        /**
         * 🔑 切换开关状态
         */
        toggle() {
            console.log('🔘 UISwitch: 切换状态', { 当前状态: this.isOn });
            this.setValue(!this.isOn);
        }

        /**
         * 获取开关状态
         */
        getValue() {
            return this.isOn;
        }

        /**
         * 执行指定类型的事件 - 参考 UIButton 实现
         */
        executeEvent(eventType) {
            const eventCode = this[eventType];
            if (!eventCode || typeof eventCode !== 'string') return;

            console.log('⚡ UISwitch: 执行事件', eventType, eventCode);

            try {
                // 创建函数并执行，this指向当前开关，同时传入当前状态
                const eventFunction = new Function('isOn', eventCode);
                eventFunction.call(this, this.isOn);
            } catch (error) {
                console.error(`UISwitch ${eventType} 事件执行失败:`, error);
                console.error('事件代码:', eventCode);
            }
        }

        // ==================== 🔑 组件绑定方法 ====================

        /**
         * 检查是否为 UIImage 类型
         */
        isUIImageType(sprite) {
            return sprite && (
                sprite.constructor.name === 'UIImage' ||
                (sprite.isUIComponent && sprite.uiComponentType === 'UIImage')
            );
        }

        /**
         * 绑定背景轨道精灵
         */
        bindBackgroundSprite(sprite) {
            // 🔑 检查是否重复绑定同一个对象，避免不必要的更新
            if (this.boundBackgroundSprite === sprite) {
                console.log('🔍 UISwitch: 背景轨道已绑定，跳过重复绑定');
                return;
            }

            // 检查是否为 UIImage 类型
            if (!this.isUIImageType(sprite)) {
                console.error('🚨 UISwitch: boundBackgroundSprite 必须是 UIImage 类型', {
                    provided: sprite.constructor.name,
                    isUIComponent: sprite.isUIComponent,
                    uiComponentType: sprite.uiComponentType,
                    expected: 'UIImage'
                });
                return;
            }

            console.log('🔍 UISwitch: 设置 boundBackgroundSprite');
            this.boundBackgroundSprite = sprite;

            // 🔑 移除：不再调用 updateSizeFromBackground()，布局由属性面板控制

            console.log('✅ UISwitch: 成功绑定 BackgroundSprite (UIImage) - 背景轨道');
        }

        /**
         * 绑定滑块按钮精灵
         */
        bindKnobSprite(sprite) {
            // 🔑 检查是否重复绑定同一个对象，避免不必要的更新
            if (this.boundKnobSprite === sprite) {
                console.log('🔍 UISwitch: 滑块按钮已绑定，跳过重复绑定');
                return;
            }

            // 检查是否为 UIImage 类型
            if (!this.isUIImageType(sprite)) {
                console.error('🚨 UISwitch: boundKnobSprite 必须是 UIImage 类型', {
                    provided: sprite.constructor.name,
                    isUIComponent: sprite.isUIComponent,
                    uiComponentType: sprite.uiComponentType,
                    expected: 'UIImage'
                });
                return;
            }

            this.boundKnobSprite = sprite;

            // 🔑 确保滑块按钮不阻挡点击事件 - 让点击穿透到背景
            this.boundKnobSprite.interactive = false;
            this.boundKnobSprite.interactiveChildren = false;

            // 🔑 移除：不再调用 updateKnob()，布局由属性面板控制
            console.log('✅ UISwitch: 成功绑定 KnobSprite (UIImage) - 滑块按钮，已设置为非交互');
        }

        // 🔑 移除：updateSizeFromBackground() 方法已删除，布局由属性面板控制

        // ==================== 🎮 状态管理方法 ====================

        /**
         * 设置启用状态
         */
        setEnabled(enabled) {
            this.enabled = enabled;
            this.alpha = enabled ? 1.0 : 0.5;
            this.interactive = enabled;

            console.log('🔘 UISwitch: 设置启用状态', {
                enabled: this.enabled,
                alpha: this.alpha,
                interactive: this.interactive
            });
        }

        /**
         * 设置动画时长
         */
        setAnimationDuration(duration) {
            this.animationDuration = Math.max(0, duration);
            console.log('🔘 UISwitch: 设置动画时长', this.animationDuration);
        }

        // ==================== 📊 属性获取方法 ====================

        /**
         * 获取所有属性（用于模型同步）
         */
        getProperties() {
            return {
                // 基础属性
                x: this.x,
                y: this.y,
                width: this.switchWidth,
                height: this.switchHeight,

                // 状态属性
                isOn: this.isOn,
                enabled: this.enabled,

                // 动画属性
                animationDuration: this.animationDuration,

                // 事件代码
                _eventCodes: this._eventCodes
            };
        }

        /**
         * 获取状态信息（调试用）
         */
        getStatus() {
            return {
                type: 'UISwitch',
                isOn: this.isOn,
                enabled: this.enabled,
                isAnimating: this.isAnimating,
                size: `${this.switchWidth}x${this.switchHeight}`,
                hasBackground: !!this.boundBackgroundSprite,
                hasKnob: !!this.boundKnobSprite,
                animationDuration: this.animationDuration
            };
        }

        /**
         * 克隆当前 UISwitch 对象
         * @param {Object} options 克隆选项
         * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
         * @param {number} options.offsetX 水平偏移量 (默认: 20)
         * @param {number} options.offsetY 垂直偏移量 (默认: 20)
         * @returns {UISwitch} 克隆的 UISwitch 对象
         */
        clone(options = {}) {
            console.log('🔄 UISwitch: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = {
                // 基础属性
                width: this.switchWidth,
                height: this.switchHeight,
                visible: this.visible,

                // UISwitch 特有属性
                isOn: this.isOn,
                enabled: this.enabled,
                animationDuration: this.animationDuration,

                // 事件代码
                onToggle: this.onToggle || '',
                onTurnOn: this.onTurnOn || '',
                onTurnOff: this.onTurnOff || '',

                // 🔑 深度克隆脚本数组
                componentScripts: this.componentScripts ?
                    this.componentScripts.map(script => ({
                        id: script.id,
                        name: script.name,
                        type: script.type,
                        enabled: script.enabled,
                        code: script.code,
                        description: script.description
                    })) : []
            };

            // 2. 创建克隆对象
            const clonedSwitch = new UISwitch(cloneProperties);

            // 3. 设置位置和变换属性
            clonedSwitch.x = this.x + (offsetPosition ? offsetX : 0);
            clonedSwitch.y = this.y + (offsetPosition ? offsetY : 0);
            clonedSwitch.scale.x = this.scale.x;
            clonedSwitch.scale.y = this.scale.y;
            clonedSwitch.rotation = this.rotation;
            clonedSwitch.alpha = this.alpha;
            clonedSwitch.zIndex = this.zIndex;

            // 4. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                    clonedSwitch.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                }
            }

            // 5. 重建绑定关系
            this.rebuildBindingsForClone(clonedSwitch, clonedChildren);

            console.log('✅ UISwitch: 克隆完成，包含', clonedChildren.length, '个子对象');
            return clonedSwitch;
        }

        /**
         * 为克隆对象重建绑定关系
         * @param {UISwitch} clonedSwitch 克隆的开关对象
         * @param {Array} clonedChildren 克隆的子对象数组
         */
        rebuildBindingsForClone(clonedSwitch, clonedChildren) {
            console.log('🔗 UISwitch: 重建克隆对象的绑定关系');

            // 找到原始绑定对象在子对象数组中的索引
            const findChildIndex = (boundObject) => {
                if (!boundObject) return -1;
                for (let i = 0; i < this.children.length; i++) {
                    if (this.children[i] === boundObject) {
                        return i;
                    }
                }
                return -1;
            };

            // 重新绑定背景精灵
            if (this.boundBackgroundSprite) {
                const index = findChildIndex(this.boundBackgroundSprite);
                if (index >= 0 && index < clonedChildren.length) {
                    clonedSwitch.bindBackgroundSprite(clonedChildren[index]);
                    console.log('🔗 重新绑定背景精灵');
                }
            }

            // 重新绑定旋钮精灵
            if (this.boundKnobSprite) {
                const index = findChildIndex(this.boundKnobSprite);
                if (index >= 0 && index < clonedChildren.length) {
                    clonedSwitch.bindKnobSprite(clonedChildren[index]);
                    console.log('🔗 重新绑定旋钮精灵');
                }
            }
        }

        /**
         * 🔑 销毁方法 - 添加 onDestroy 生命周期
         */
        destroy() {
            console.log('🗑️ UISwitch: 开始销毁开关', this.name);

            // 🔑 执行 onDestroy 脚本
            if (this.executeScript && typeof this.executeScript === 'function') {
                try {
                    this.executeScript('onDestroy');
                    console.log('🔄 UISwitch: onDestroy 脚本执行完成');
                } catch (error) {
                    console.error('❌ UISwitch: onDestroy 脚本执行失败', error);
                }
            }

            // 清理动画
            if (this.animationTween) {
                this.animationTween.stop();
                this.animationTween = null;
            }

            // 🔑 清理 PIXI ticker 注册
            if (this._tickerCallback && typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                Graphics.app.ticker.remove(this._tickerCallback);
                this._tickerCallback = null;
                console.log('🔧 UISwitch: 已清理 PIXI ticker 注册');
            }

            // 清理绑定的组件引用
            this.boundBackgroundSprite = null;
            this.boundKnobSprite = null;

            // 调用父类销毁方法
            super.destroy();

            console.log('✅ UISwitch: 销毁完成');
        }
    }

    // 将UISwitch类添加到全局作用域
    window.UISwitch = UISwitch;

    console.log('🔘 UISwitch插件已加载');

})();