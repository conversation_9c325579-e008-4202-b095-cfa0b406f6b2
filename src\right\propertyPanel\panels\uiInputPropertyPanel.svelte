<script lang="ts">
  import type { InputModel } from '../../../type/ui/inputModel.svelte';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import LabelInput from '../../../components/LabelInput.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import ColorPicker from '../../../components/ColorPicker.svelte';
  import SafeInput from '../../../components/SafeInput.svelte';
  import Select from '../../../components/Select.svelte';

  interface Props {
    model: InputModel;
  }

  let { model }: Props = $props();

  // 输入类型选项
  const inputTypeOptions = [
    { value: 'text', label: '文本' },
    { value: 'password', label: '密码' },
    { value: 'number', label: '数字' },
    { value: 'email', label: '邮箱' }
  ];

  // 获取输入框状态信息
  let inputInfo = $derived(() => {
    if (!model) return null;
    return model.getInputStatus();
  });

  // 预设验证规则选项
  const validationPresets = [
    { value: 'none', label: '无验证', pattern: null },
    { value: 'email', label: '邮箱格式', pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
    { value: 'phone', label: '手机号码', pattern: /^1[3-9]\d{9}$/ },
    { value: 'number', label: '纯数字', pattern: /^\d+$/ },
    { value: 'alphanumeric', label: '字母数字', pattern: /^[a-zA-Z0-9]+$/ },
    { value: 'chinese', label: '中文字符', pattern: /^[\u4e00-\u9fa5]+$/ }
  ];

  // 当前验证规则
  let currentValidationPreset = $state('none');

  // 应用预设验证规则
  function applyValidationPreset(presetValue: string) {
    const preset = validationPresets.find(p => p.value === presetValue);
    if (preset) {
      model.setValidation(preset.pattern, preset.label === '无验证' ? '' : `请输入正确的${preset.label}`);
      currentValidationPreset = presetValue;
    }
  }

  // 清除验证规则
  function clearValidation() {
    model.setValidation(null, '');
    currentValidationPreset = 'none';
  }
</script>

<AccordionPanel
  title="📝 UIInput 属性"
  icon="📝"
  badge="输入框组件"
  badgeVariant="info"
  expanded={true}
>
  <!-- 输入框状态信息 -->
  {#if inputInfo()}
    <div class="input-status">
      <Label text="状态: {inputInfo()?.inputType} | 长度: {inputInfo()?.value?.length || 0}/{inputInfo()?.maxLength} | {inputInfo()?.readonly ? '只读' : '可编辑'}" />
    </div>
  {/if}

  <!-- 基本属性 -->
  <PropertyContainer>
    <Label text="当前值:" />
    <SafeInput
      bind:value={model.value}
      placeholder="输入框的值"
      class="value-input"
      targetObject={model}
      fieldName="value"
      name="输入框值"
    />
  </PropertyContainer>

  <PropertyContainer>
    <Label text="占位符:" />
    <SafeInput
      bind:value={model.placeholder}
      placeholder="请输入占位符文本"
      targetObject={model}
      fieldName="placeholder"
      name="占位符"
    />
  </PropertyContainer>

  <!-- 输入类型和限制 -->
  <PropertyContainer>
    <Label text="输入类型:" />
    <Select
      bind:value={model.inputType}
      options={inputTypeOptions}
      placeholder="选择输入类型"
      size="sm"
      searchable={false}
      clearable={false}
      targetObject={model}
      fieldName="inputType"
      name="输入类型"
    />
  </PropertyContainer>

  <PropertyContainer>
    <Label text="最大长度:" />
    <LabelInput
      bind:value={model.maxLength}
      type="number"
      min={1}
      max={1000}
      step={1}
      targetObject={model}
      fieldName="maxLength"
      name="最大长度"
      placeholder="255"
    />
  </PropertyContainer>

  <!-- 状态配置 -->
  <PropertyContainer>
    <Label text="状态:" />
    <div class="checkbox-group">
      <Checkbox
        bind:checked={model.readonly}
        label="只读"
        targetObject={model}
        fieldName="readonly"
        name="只读状态"
      />
    </div>
  </PropertyContainer>

  <!-- 样式设置 -->
  <PropertyContainer>
    <Label text="字体大小:" />
    <LabelInput
      bind:value={model.fontSize}
      type="number"
      min={8}
      max={72}
      step={1}
      targetObject={model}
      fieldName="fontSize"
      name="字体大小"
      placeholder="16"
    />
  </PropertyContainer>

  <PropertyContainer>
    <Label text="字符间距:" />
    <LabelInput
      bind:value={model.letterSpacing}
      type="number"
      min={0}
      max={20}
      step={0.5}
      targetObject={model}
      fieldName="letterSpacing"
      name="字符间距"
      placeholder="0"
    />
  </PropertyContainer>

  <!-- 颜色设置 -->
  <PropertyContainer>
    <Label text="文本颜色:" />
    <ColorPicker
      bind:value={model.textColor}
      label=""
      targetObject={model}
      fieldName="textColor"
      name="文本颜色"
      onChange={(color) => {
        console.log('🔴 文本颜色变化:', color);
        model.textColor = color;
      }}
    />
  </PropertyContainer>

  <PropertyContainer>
    <Label text="占位符颜色:" />
    <ColorPicker
      bind:value={model.placeholderColor}
      label=""
      targetObject={model}
      fieldName="placeholderColor"
      name="占位符颜色"
      onChange={(color) => {
        console.log('🔴 占位符颜色变化:', color);
        model.placeholderColor = color;
      }}
    />
  </PropertyContainer>

  <PropertyContainer>
    <Label text="背景颜色:" />
    <ColorPicker
      bind:value={model.backgroundColor}
      label=""
      targetObject={model}
      fieldName="backgroundColor"
      name="背景颜色"
      onChange={(color) => {
        console.log('🔴 背景颜色变化:', color);
        model.backgroundColor = color;
      }}
    />
  </PropertyContainer>

  <!-- 边框设置 -->
  <PropertyContainer>
    <Label text="边框颜色:" />
    <ColorPicker
      bind:value={model.borderColor}
      label=""
      targetObject={model}
      fieldName="borderColor"
      name="边框颜色"
      onChange={(color) => {
        console.log('🔴 边框颜色变化:', color);
        model.borderColor = color;
      }}
    />
  </PropertyContainer>

  <PropertyContainer>
    <Label text="焦点边框颜色:" />
    <ColorPicker
      bind:value={model.focusBorderColor}
      label=""
      targetObject={model}
      fieldName="focusBorderColor"
      name="焦点边框颜色"
      onChange={(color) => {
        console.log('🔴 焦点边框颜色变化:', color);
        model.focusBorderColor = color;
      }}
    />
  </PropertyContainer>

  <PropertyContainer>
    <Label text="边框宽度:" />
    <LabelInput
      bind:value={model.borderWidth}
      type="number"
      min={0}
      max={10}
      step={1}
      targetObject={model}
      fieldName="borderWidth"
      name="边框宽度"
      placeholder="1"
    />
  </PropertyContainer>

  <PropertyContainer>
    <Label text="圆角半径:" />
    <LabelInput
      bind:value={model.borderRadius}
      type="number"
      min={0}
      max={50}
      step={1}
      targetObject={model}
      fieldName="borderRadius"
      name="圆角半径"
      placeholder="4"
    />
  </PropertyContainer>

  <!-- 验证设置 -->
  <PropertyContainer>
    <Label text="验证规则:" />
    <div class="validation-controls">
      <Select
        bind:value={currentValidationPreset}
        options={validationPresets}
        placeholder="选择验证规则"
        size="sm"
        searchable={false}
        clearable={false}
        onChange={(value) => applyValidationPreset(value)}
      />
      <button class="clear-btn" onclick={clearValidation} title="清除验证">✕</button>
    </div>
  </PropertyContainer>

  <PropertyContainer>
    <Label text="验证消息:" />
    <SafeInput
      bind:value={model.validationMessage}
      placeholder="验证失败时的提示信息"
      targetObject={model}
      fieldName="validationMessage"
      name="验证消息"
    />
  </PropertyContainer>

  <!-- 验证状态显示 -->
  {#if model.validationPattern}
    <div class="validation-status">
      <Label text="✅ 已启用验证: {model.validationMessage}" />
    </div>
  {:else}
    <div class="validation-status">
      <Label text="⚪ 未设置验证规则" />
    </div>
  {/if}

</AccordionPanel>

<style>
  :global(.value-input) {
    flex: 1;
  }

  .input-status {
    background: #f5f5f5;
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 12px;
    font-size: 12px;
    color: #666;
  }

  .checkbox-group {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .validation-controls {
    display: flex;
    gap: 4px;
    align-items: center;
    flex: 1;
  }

  .clear-btn {
    background: #ff4444;
    color: white;
    border: none;
    border-radius: 3px;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .clear-btn:hover {
    background: #cc0000;
  }

  .validation-status {
    background: #f0f8ff;
    padding: 6px 8px;
    border-radius: 4px;
    margin-top: 8px;
    font-size: 11px;
  }

  /* 覆盖Checkbox组件的字体大小 */
  :global(.simple-checkbox .checkbox-label) {
    font-size: 10px !important;
  }
</style>
