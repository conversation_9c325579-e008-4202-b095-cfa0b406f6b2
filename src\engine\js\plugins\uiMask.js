/**
 * UIMask 插件 - 遮罩组件
 *
 * 功能：
 * - 创建各种类型的遮罩（矩形、圆形、图片）
 * - 容器级遮罩，所有子对象自动应用遮罩效果
 * - Graphics大小始终等于Container大小
 * - 支持动态调整遮罩区域
 * - 完整的生命周期管理
 *
 * 使用方法：
 * const mask = new UIMask({
 *     maskType: 'rectangle',
 *     width: 200,
 *     height: 150
 * });
 *
 * <AUTHOR> Assistant
 * @version 3.0 - 容器级遮罩方案
 */

(function() {
    'use strict';

    console.log('🎭 开始加载 UIMask 插件 v3.0');

    /**
     * UIMask - 遮罩容器组件
     * 继承自 PIXI.Container，内部创建Graphics作为遮罩
     */
    class UIMask extends PIXI.Container {
        constructor(properties = {}) {
            super();

            console.log('🎭 UIMask: 创建遮罩容器', properties);

            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'UIMask';

            // 🔑 使用UIComponentUtils添加UIComponent功能
            if (window.UIComponentUtils) {
                window.UIComponentUtils.applyToSprite(this, properties);
            }

            // 🔑 使用UIScriptManager添加脚本功能
            if (window.UIScriptManager) {
                window.UIScriptManager.applyToObject(this, properties);
                console.log('🔧 UIMask: 脚本系统初始化完成', {
                    hasComponentScripts: !!this.componentScripts,
                    scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                    scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                });
            }

            // 遮罩属性
            this.maskType = properties.maskType || 'rectangle'; // 'rectangle', 'circle', 'image'
            this.maskImage = properties.maskImage || null;

            // 🔑 容器尺寸（这就是遮罩的有效区域）
            this.maskWidth = properties.width || 200;
            this.maskHeight = properties.height || 200;

            // 设置基础属性
            this.x = properties.x || 0;
            this.y = properties.y || 0;
            this.name = properties.name || 'UIMask';

            // 🔑 创建内部Graphics对象作为遮罩
            this.maskGraphics = new PIXI.Graphics();

            // 🔑 设置容器级遮罩
            this.mask = this.maskGraphics;

            // 🔑 绘制遮罩图形
            this.drawMask();

            // 🔑 标记为已创建，onStart会在添加到父容器时执行
            this._isCreated = true;

            console.log('✅ UIMask: 遮罩容器创建完成', {
                type: this.maskType,
                size: `${this.maskWidth}x${this.maskHeight}`,
                position: `(${this.x}, ${this.y})`
            });
        }

        /**
         * 🔑 添加width getter/setter来兼容外部代码
         */
        get width() {
            return this.maskWidth;
        }

        set width(value) {
            this.maskWidth = value;
            this.drawMask(); // 重新绘制遮罩
        }

        /**
         * 🔑 添加height getter/setter来兼容外部代码
         */
        get height() {
            return this.maskHeight;
        }

        set height(value) {
            this.maskHeight = value;
            this.drawMask(); // 重新绘制遮罩
        }

        /**
         * 🔑 绘制遮罩图形（Graphics大小始终等于Container）
         */
        drawMask() {
            this.maskGraphics.clear();

            switch (this.maskType) {
                case 'rectangle':
                    this.drawRectangleMask();
                    break;
                case 'circle':
                    this.drawCircleMask();
                    break;
                case 'image':
                    this.drawImageMask();
                    break;
                default:
                    console.warn('UIMask: 未知的遮罩类型', this.maskType);
                    this.drawRectangleMask();
            }
        }

        /**
         * 绘制矩形遮罩
         */
        drawRectangleMask() {
            console.log('🎨 UIMask: 绘制矩形遮罩', `${this.maskWidth}x${this.maskHeight}`);

            this.maskGraphics.beginFill(0xffffff, 1);
            this.maskGraphics.drawRect(0, 0, this.maskWidth, this.maskHeight);
            this.maskGraphics.endFill();

            console.log('✅ UIMask: 矩形遮罩绘制完成');
        }

        /**
         * 绘制圆形遮罩
         */
        drawCircleMask() {
            const radius = Math.min(this.maskWidth, this.maskHeight) / 2;
            const centerX = this.maskWidth / 2;
            const centerY = this.maskHeight / 2;

            console.log('🎨 UIMask: 绘制圆形遮罩', `半径: ${radius}`);

            this.maskGraphics.beginFill(0xffffff, 1);
            this.maskGraphics.drawCircle(centerX, centerY, radius);
            this.maskGraphics.endFill();

            console.log('✅ UIMask: 圆形遮罩绘制完成');
        }

        /**
         * 绘制图片遮罩
         */
        drawImageMask() {
            if (!this.maskImage) {
                console.warn('UIMask: 图片遮罩需要指定图片路径，使用矩形遮罩代替');
                this.drawRectangleMask();
                return;
            }

            // 对于图片遮罩，我们先绘制一个矩形作为占位
            // 实际的图片遮罩需要加载图片后再处理
            this.maskGraphics.beginFill(0xffffff, 1);
            this.maskGraphics.drawRect(0, 0, this.maskWidth, this.maskHeight);
            this.maskGraphics.endFill();

            console.log('🎭 UIMask: 图片遮罩占位绘制完成', `${this.maskWidth}x${this.maskHeight}`);
            // TODO: 实现真正的图片遮罩加载
        }

        /**
         * 🔑 重写 addChild 方法 - 容器级遮罩，不需要特殊处理
         */
        addChild(child) {
            console.log('🎭 UIMask: 添加子对象，遮罩自动应用', {
                childType: child.constructor.name,
                childName: child.name,
                maskSize: `${this.maskWidth}x${this.maskHeight}`
            });

            const result = super.addChild(child);

            console.log('✅ UIMask: 子对象添加完成，遮罩已自动生效');
            return result;
        }

        /**
         * 🔑 更新遮罩尺寸
         */
        updateSize(width, height) {
            if (width !== undefined) this.maskWidth = width;
            if (height !== undefined) this.maskHeight = height;

            console.log('🔧 UIMask: 更新遮罩尺寸', {
                width: this.maskWidth,
                height: this.maskHeight
            });

            // 重新绘制遮罩
            this.drawMask();
        }

        /**
         * 更新遮罩类型
         */
        updateMaskType(type) {
            this.maskType = type;
            console.log('🔧 UIMask: 更新遮罩类型', this.maskType);

            // 重新绘制遮罩
            this.drawMask();
        }

        /**
         * 获取遮罩信息
         */
        getMaskInfo() {
            return {
                type: this.maskType,
                size: { width: this.maskWidth, height: this.maskHeight },
                maskImage: this.maskImage,
                childrenCount: this.children.length,
                hasChildren: this.children.length > 0
            };
        }

        /**
         * 🔑 更新方法 - 支持脚本执行
         */
        update() {
            // 调用父类更新方法（如果存在）
            if (super.update) {
                super.update();
            }

            // 🔑 执行更新脚本
            if (this.executeScript) {
                this.executeScript('onUpdate');
            }
        }

        /**
         * 🔑 克隆UIMask对象
         * @param {Object} options 克隆选项
         * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
         * @param {number} options.offsetX 水平偏移量 (默认: 20)
         * @param {number} options.offsetY 垂直偏移量 (默认: 20)
         * @returns {UIMask} 克隆的 UIMask 对象
         */
        clone(options = {}) {
            console.log('🔄 UIMask: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 创建新的UIMask对象
            const clonedMask = new UIMask({
                // 基础属性
                width: this.maskWidth,
                height: this.maskHeight,
                visible: this.visible,

                // 🔑 UIComponent 属性（安全访问）
                name: this.name || '',
                enabled: this.enabled !== false,

                // 🔑 深度克隆组件脚本数组
                componentScripts: this.componentScripts ?
                    this.componentScripts.map(script => ({
                        id: script.id,
                        name: script.name,
                        type: script.type,
                        enabled: script.enabled,
                        code: script.code,
                        description: script.description
                    })) : [],

                // UIMask 特有属性
                maskType: this.maskType,
                maskImage: this.maskImage
            });

            // 2. 设置位置和变换属性
            clonedMask.x = this.x + (offsetPosition ? offsetX : 0);
            clonedMask.y = this.y + (offsetPosition ? offsetY : 0);
            clonedMask.scale.x = this.scale.x;
            clonedMask.scale.y = this.scale.y;
            clonedMask.rotation = this.rotation;
            clonedMask.alpha = this.alpha;
            clonedMask.anchor.x = this.anchor.x;
            clonedMask.anchor.y = this.anchor.y;
            clonedMask.pivot.x = this.pivot.x;
            clonedMask.pivot.y = this.pivot.y;
            clonedMask.skew.x = this.skew.x;
            clonedMask.skew.y = this.skew.y;
            clonedMask.zIndex = this.zIndex;

            // 3. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                    clonedMask.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                }
            }

            console.log('✅ UIMask: 克隆完成', {
                originalChildren: this.children.length,
                clonedChildren: clonedChildren.length,
                position: `(${clonedMask.x}, ${clonedMask.y})`
            });

            return clonedMask;
        }

        /**
         * 🔑 销毁时清理Graphics和遮罩
         */
        destroy(options) {
            console.log('🗑️ UIMask: 开始销毁遮罩容器');

            // 🔑 安全执行销毁脚本
            try {
                if (this.executeScript && !this._isDestroying) {
                    this._isDestroying = true; // 防止重复销毁
                    this.executeScript('onDestroy');
                }
            } catch (error) {
                console.warn('⚠️ UIMask: 销毁脚本执行失败', error);
            }

            try {
                // 清理遮罩Graphics
                if (this.maskGraphics) {
                    this.maskGraphics.destroy();
                    this.maskGraphics = null;
                    console.log('✅ UIMask: maskGraphics已销毁');
                }

                // 清理遮罩引用
                this.mask = null;

                // 调用父类销毁方法
                super.destroy(options);

                console.log('✅ UIMask: 遮罩容器销毁完成');
            } catch (error) {
                console.error('❌ UIMask: 销毁过程中出错', error);
            }
        }
    }

    // 导出到全局
    window.UIMask = UIMask;
    console.log('✅ UIMask 插件 v3.0 加载完成');

})();
