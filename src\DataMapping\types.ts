/**
 * 数据流分析类型定义
 */

/**
 * 按钮数据流
 */
export interface ButtonDataFlow {
  /** 按钮名称 */
  buttonName: string;
  /** 触发的方法列表（只记录数据相关方法，不记录UI方法） */
  triggerMethods: string[];
}

/**
 * 场景使用的数据资源
 */
export interface SceneDataResource {
  /** 数据路径 */
  dataPath: string;
  /** 数据说明 */
  description: string;
}

/**
 * 场景数据流
 */
export interface SceneDataFlow {
  /** 场景名称 */
  sceneName: string;
  /** 按钮数据流列表 */
  buttons: ButtonDataFlow[];
  /** 场景使用的数据资源 */
  dataResources?: SceneDataResource[];
}
