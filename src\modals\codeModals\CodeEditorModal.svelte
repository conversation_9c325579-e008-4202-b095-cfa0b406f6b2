<script lang="ts">
  import { onMount } from 'svelte';
  import { codeEditorStore } from './codeEditorStore';
  import { EditorView, keymap } from '@codemirror/view';
  import { EditorState } from '@codemirror/state';
  import { javascript } from '@codemirror/lang-javascript';
  import { oneDark } from '@codemirror/theme-one-dark';
  import { autocompletion, completionKeymap } from '@codemirror/autocomplete';
  import { searchKeymap, highlightSelectionMatches } from '@codemirror/search';
  import { defaultKeymap, historyKeymap, history } from '@codemirror/commands';
  import { bracketMatching, indentOnInput, syntaxHighlighting, defaultHighlightStyle } from '@codemirror/language';
  import { lineNumbers, highlightActiveLineGutter } from '@codemirror/view';
  import { highlightActiveLine, drawSelection, rectangularSelection, crosshairCursor } from '@codemirror/view';

  let editorContainer = $state<HTMLDivElement>();
  let editor = $state<EditorView | null>(null);

  // 响应式订阅store状态
  const isOpen = $derived($codeEditorStore.isOpen);
  const code = $derived($codeEditorStore.code);
  const title = $derived($codeEditorStore.title);
  const language = $derived($codeEditorStore.language);
  
  onMount(() => {
    // CodeMirror不需要复杂的配置
    console.log('🔧 CodeEditor: 初始化完成');

    // 清理函数
    return () => {
      console.log('🔧 CodeEditor: 组件销毁，清理编辑器');
      cleanupEditor();
    };
  });

  // 当模态框打开时创建编辑器
  $effect(() => {
    if (isOpen && editorContainer && !editor) {
      console.log('🔧 CodeEditor: 准备创建编辑器', { isOpen, hasContainer: !!editorContainer, hasEditor: !!editor });
      // 使用setTimeout确保DOM已经渲染
      setTimeout(() => {
        if (editorContainer && !editor) {
          createEditor();
        }
      }, 100);
    }
  });

  // 当模态框关闭时清理编辑器
  $effect(() => {
    if (!isOpen && editor) {
      console.log('🔧 CodeEditor: 模态框关闭，清理编辑器');
      cleanupEditor();
    }
  });
  
  // 当代码变化时更新编辑器内容
  $effect(() => {
    if (editor && code !== undefined) {
      const currentValue = editor.state.doc.toString();
      if (currentValue !== code) {
        editor.dispatch({
          changes: {
            from: 0,
            to: editor.state.doc.length,
            insert: code
          }
        });
      }
    }
  });

  function cleanupEditor() {
    if (editor) {
      console.log('🔧 CodeEditor: 清理编辑器');
      editor.destroy();
      editor = null;
    }
  }

  function createEditor() {
    console.log('🔧 CodeEditor: 开始创建编辑器', {
      hasContainer: !!editorContainer,
      containerElement: editorContainer,
      code: code
    });

    // 确保先清理旧编辑器
    cleanupEditor();

    if (!editorContainer) {
      console.error('❌ CodeEditor: 编辑器容器不存在');
      return;
    }

    const state = EditorState.create({
      doc: code || '',
      extensions: [
        // 基础编辑器功能
        lineNumbers(),
        highlightActiveLineGutter(),
        highlightActiveLine(),
        drawSelection(),
        rectangularSelection(),
        crosshairCursor(),
        history(),
        bracketMatching(),
        indentOnInput(),
        syntaxHighlighting(defaultHighlightStyle, { fallback: true }),
        highlightSelectionMatches(),

        // 主题和样式
        oneDark,
        EditorView.lineWrapping,
        EditorView.theme({
          '&': {
            fontSize: '14px',
            fontFamily: 'Consolas, "Courier New", monospace'
          },
          '.cm-content': {
            padding: '12px',
            minHeight: '200px'
          },
          '.cm-focused': {
            outline: 'none'
          }
        }),

        // 语言支持
        javascript(),
        keymap.of([
          ...defaultKeymap,
          ...historyKeymap,
          ...completionKeymap,
          ...searchKeymap,
          // 添加保存快捷键 Ctrl+S
          {
            key: 'Ctrl-s',
            run: () => {
              handleSave();
              return true;
            }
          }
        ]),
        autocompletion({
          override: [
            // 自定义自动完成
            (context) => {
              const word = context.matchBefore(/\w*/);
              if (!word) return null;

              const options = [
                { label: 'component', type: 'variable', info: '当前组件实例' },
                { label: 'console', type: 'variable', info: '控制台对象' },
                { label: '$gameParty', type: 'variable', info: '游戏队伍数据' },
                { label: '$gameActors', type: 'variable', info: '游戏角色数据' },
                { label: '$dataActors', type: 'variable', info: '角色数据数组' },
                { label: '$gameVariables', type: 'variable', info: '游戏变量' },
                { label: '$gameSwitches', type: 'variable', info: '游戏开关' },
                { label: 'SceneManager', type: 'variable', info: '场景管理器' },
                { label: 'SoundManager', type: 'variable', info: '音效管理器' },
                { label: 'AudioManager', type: 'variable', info: '音频管理器' }
              ];

              return {
                from: word.from,
                options: options.filter(opt =>
                  opt.label.toLowerCase().includes(word.text.toLowerCase())
                )
              };
            }
          ]
        })
      ]
    });

    editor = new EditorView({
      state,
      parent: editorContainer
    });

    console.log('✅ CodeEditor: 编辑器创建成功', {
      editor: editor,
      hasContent: !!editor.state.doc.toString(),
      content: editor.state.doc.toString()
    });
  }
  
  function handleSave() {
    if (editor) {
      const currentCode = editor.state.doc.toString();
      console.log('🔧 CodeEditor: 保存代码', currentCode);
      codeEditorStore.save(currentCode);
    } else {
      console.warn('⚠️ CodeEditor: 编辑器不存在，无法保存');
    }
  }

  function handleCancel() {
    codeEditorStore.close();
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      handleCancel();
    }
  }


</script>

{#if isOpen}
  <!-- svelte-ignore a11y_no_static_element_interactions -->
  <div class="modal-overlay" onkeydown={handleKeydown} role="dialog" aria-modal="true" tabindex="-1">
    <div class="code-editor-modal">
      <div class="editor-header">
        <h3>{title || '代码编辑器'}</h3>
        <div class="editor-actions">
          <button class="save-btn" onclick={handleSave}>
            💾 保存 (Ctrl+S)
          </button>
          <button class="cancel-btn" onclick={handleCancel}>
            ❌ 取消
          </button>
        </div>
      </div>
      <div class="editor-container" bind:this={editorContainer}></div>
      <div class="editor-footer">
        <div class="editor-tips">
          <span>💡 提示：使用 Ctrl+S 保存，ESC 取消</span>
          <span>🔧 可用变量：component, console, $gameParty, $gameActors 等</span>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
  }
  
  .code-editor-modal {
    width: 90%;
    height: 80%;
    max-width: 1200px;
    max-height: 800px;
    background: var(--theme-surface, #2d3748);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  }
  
  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--theme-border, #4a5568);
    background: var(--theme-surface-light, #374151);
    border-radius: 8px 8px 0 0;
  }
  
  .editor-header h3 {
    margin: 0;
    color: var(--theme-text, #ffffff);
    font-size: 18px;
    font-weight: 600;
  }
  
  .editor-actions {
    display: flex;
    gap: 12px;
  }
  
  .save-btn, .cancel-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .save-btn {
    background: var(--theme-primary, #3b82f6);
    color: white;
  }
  
  .save-btn:hover {
    background: var(--theme-primary-dark, #2563eb);
    transform: translateY(-1px);
  }
  
  .cancel-btn {
    background: var(--theme-error, #ef4444);
    color: white;
  }
  
  .cancel-btn:hover {
    background: var(--theme-error-dark, #dc2626);
    transform: translateY(-1px);
  }
  
  .editor-container {
    flex: 1;
    min-height: 400px;
    height: 100%;
    overflow: hidden;
    background: #1e1e1e;
    border: 1px solid var(--theme-border, #4a5568);
  }

  /* 确保CodeMirror编辑器填满容器 */
  .editor-container :global(.cm-editor) {
    height: 100%;
  }

  .editor-container :global(.cm-scroller) {
    min-height: 400px;
  }
  
  .editor-footer {
    padding: 12px 20px;
    border-top: 1px solid var(--theme-border, #4a5568);
    background: var(--theme-surface-light, #374151);
    border-radius: 0 0 8px 8px;
  }
  
  .editor-tips {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--theme-text-secondary, #9ca3af);
  }
  
  @media (max-width: 768px) {
    .code-editor-modal {
      width: 95%;
      height: 90%;
    }
    
    .editor-tips {
      flex-direction: column;
      gap: 4px;
      align-items: flex-start;
    }
  }
</style>
