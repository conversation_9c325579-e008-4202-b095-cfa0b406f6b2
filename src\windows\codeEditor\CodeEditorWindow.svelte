<script lang="ts">
  import { onMount } from 'svelte';
  import { EditorView, keymap } from '@codemirror/view';
  import { EditorState } from '@codemirror/state';
  import { javascript } from '@codemirror/lang-javascript';
  import { oneDark } from '@codemirror/theme-one-dark';
  import { autocompletion, completionKeymap } from '@codemirror/autocomplete';
  import { searchKeymap, highlightSelectionMatches } from '@codemirror/search';
  import { defaultKeymap, historyKeymap, history } from '@codemirror/commands';
  import { bracketMatching, indentOnInput, syntaxHighlighting, defaultHighlightStyle } from '@codemirror/language';
  import { lineNumbers, highlightActiveLineGutter } from '@codemirror/view';
  import { highlightActiveLine, drawSelection, rectangularSelection, crosshairCursor } from '@codemirror/view';
  import { getCurrentWebviewWindow } from '@tauri-apps/api/webviewWindow';

  // 导入API侧边栏组件
  import APISidebar from './APISidebar.svelte';

  // Tauri API
  const appWindow = getCurrentWebviewWindow();

  let editorContainer: HTMLDivElement;
  let editor: EditorView | null = null;
  let currentCode = '';
  let currentTitle = '';
  let hasUnsavedChanges = false;

  // 处理从侧边栏插入代码
  function handleCodeInsert(code: string) {
    if (!editor) {
      console.warn('编辑器未初始化，无法插入代码');
      return;
    }

    const state = editor.state;
    const selection = state.selection.main;

    // 在当前光标位置插入代码
    const transaction = state.update({
      changes: {
        from: selection.from,
        to: selection.to,
        insert: code
      },
      selection: {
        anchor: selection.from + code.length
      }
    });

    editor.dispatch(transaction);
    editor.focus();

    // 标记为有未保存的更改
    hasUnsavedChanges = true;
    updateTitle();
  }

  // 简单的JavaScript代码格式化函数
  function formatJavaScript(code: string): string {
    try {
      // 基本的格式化规则
      let formatted = code;

      // 移除多余的空白行
      formatted = formatted.replace(/\n\s*\n\s*\n/g, '\n\n');

      // 在大括号后添加换行
      formatted = formatted.replace(/\{(?!\s*\n)/g, '{\n');
      formatted = formatted.replace(/\}(?!\s*[,;\n])/g, '}\n');

      // 在分号后添加换行（如果后面不是换行）
      formatted = formatted.replace(/;(?!\s*[\n\}])/g, ';\n');

      // 基本的缩进处理
      const lines = formatted.split('\n');
      let indentLevel = 0;
      const indentSize = 2;

      const formattedLines = lines.map(line => {
        const trimmed = line.trim();
        if (!trimmed) return '';

        // 减少缩进：遇到 } 时
        if (trimmed.startsWith('}')) {
          indentLevel = Math.max(0, indentLevel - 1);
        }

        const indentedLine = ' '.repeat(indentLevel * indentSize) + trimmed;

        // 增加缩进：遇到 { 时
        if (trimmed.endsWith('{')) {
          indentLevel++;
        }

        return indentedLine;
      });

      return formattedLines.join('\n');
    } catch (error) {
      console.error('格式化失败:', error);
      return code; // 如果格式化失败，返回原代码
    }
  }

  // 格式化当前代码
  function formatCode() {
    if (!editor) {
      console.warn('编辑器未初始化，无法格式化代码');
      return;
    }

    const currentCode = editor.state.doc.toString();
    const formattedCode = formatJavaScript(currentCode);

    if (formattedCode !== currentCode) {
      // 替换整个文档内容
      const transaction = editor.state.update({
        changes: {
          from: 0,
          to: editor.state.doc.length,
          insert: formattedCode
        }
      });

      editor.dispatch(transaction);

      // 标记为有未保存的更改
      hasUnsavedChanges = true;
      updateTitle();

      console.log('✅ 代码格式化完成');
    } else {
      console.log('ℹ️ 代码已经是格式化的');
    }
  }

  // 处理键盘快捷键
  function handleKeyDown(event: KeyboardEvent) {
    // Ctrl+Shift+F 格式化代码
    if (event.ctrlKey && event.shiftKey && event.key === 'F') {
      event.preventDefault();
      formatCode();
    }
  }

  // 初始化编辑器
  function initializeEditor() {
    console.log('🔧 开始初始化编辑器');
    console.log('🔧 编辑器容器:', editorContainer);
    console.log('🔧 当前代码:', currentCode);

    if (!editorContainer) {
      throw new Error('❌ 编辑器容器不存在');
    }

    if (!currentCode && currentCode !== '') {
      throw new Error('❌ 当前代码未定义');
    }

    // 清理旧编辑器
    if (editor) {
      console.log('🔧 清理旧编辑器');
      editor.destroy();
      editor = null;
    }

    // 创建编辑器状态
    const state = EditorState.create({
      doc: currentCode,
      extensions: [
        // 基础功能
        lineNumbers(),
        highlightActiveLineGutter(),
        highlightActiveLine(),
        drawSelection(),
        rectangularSelection(),
        crosshairCursor(),
        history(),
        bracketMatching(),
        indentOnInput(),
        syntaxHighlighting(defaultHighlightStyle, { fallback: true }),
        highlightSelectionMatches(),

        // 语言支持
        javascript(),

        // 主题
        oneDark,
        EditorView.theme({
          '&': {
            fontSize: '14px',
            fontFamily: 'Consolas, "Courier New", monospace',
            height: '100%'
          },
          '.cm-content': {
            padding: '16px',
            minHeight: '100%',
            height: '100%'
          },
          '.cm-scroller': {
            fontFamily: 'Consolas, "Courier New", monospace',
            lineHeight: '1.5',
            scrollbarWidth: 'none',
            '-ms-overflow-style': 'none'
          },
          '.cm-scroller::-webkit-scrollbar': {
            display: 'none'
          },
          '.cm-focused': {
            outline: 'none'
          }
        }),

        // 自动完成
        autocompletion({
          activateOnTyping: true,
          maxRenderedOptions: 8,
          defaultKeymap: true,
          closeOnBlur: true,
          override: [
            (context) => {
              const word = context.matchBefore(/[a-zA-Z$_][a-zA-Z0-9$_]*/);

              if (!word || word.text.length < 1) return null;
              if (word.text.length === 1 && !/[a-zA-Z$_]/.test(word.text)) return null;

              const options = [
                // 🔑 组件相关
                { label: 'component', type: 'variable', info: '当前组件实例', detail: 'UIComponent' },
                { label: 'self', type: 'variable', info: '当前组件实例', detail: 'UIComponent' },

                // 🔑 新的数据监听API - 重点推荐
                {
                  label: 'watchData',
                  type: 'method',
                  info: '🔥 监听数据变化 - 支持字符串路径和对象！',
                  detail: '(target: string | object, callback: (newValue, oldValue, key?) => void) => void',
                  apply: 'watchData(\'$gameParty._gold\', (newValue, oldValue, key) => {\n  console.log(\'数据变化:\', newValue);\n  // key 参数在监听对象时可用\n});'
                },

                // 🔑 控制台
                { label: 'console', type: 'variable', info: '控制台对象', detail: 'Console' },
                { label: 'console.log', type: 'method', info: '输出日志', detail: '(...args: any[]) => void' },
                { label: 'console.warn', type: 'method', info: '输出警告', detail: '(...args: any[]) => void' },
                { label: 'console.error', type: 'method', info: '输出错误', detail: '(...args: any[]) => void' },

                // 🔑 RPG Maker MZ 全局对象
                { label: '$gameParty', type: 'variable', info: '游戏队伍数据', detail: 'Game_Party' },
                { label: '$gameActors', type: 'variable', info: '游戏角色数据', detail: 'Game_Actors' },
                { label: '$dataActors', type: 'variable', info: '角色数据数组', detail: 'Array<Actor>' },
                { label: '$gameVariables', type: 'variable', info: '游戏变量', detail: 'Game_Variables' },
                { label: '$gameSwitches', type: 'variable', info: '游戏开关', detail: 'Game_Switches' },

                // 🔑 管理器
                { label: 'SceneManager', type: 'variable', info: '场景管理器', detail: 'SceneManager' },
                { label: 'SoundManager', type: 'variable', info: '音效管理器', detail: 'SoundManager' },
                { label: 'AudioManager', type: 'variable', info: '音频管理器', detail: 'AudioManager' },

                // 🔑 组件属性和方法
                { label: 'text', type: 'property', info: '文本内容 - 自动转换类型！', detail: 'any → string' },
                { label: 'setText', type: 'method', info: '⚠️ 已废弃，推荐使用 self.text = value', detail: '(text: string) => void' },
                { label: 'setDataBinding', type: 'method', info: '⚠️ 已废弃，推荐使用 watchData', detail: '(expression: string) => void' }
              ];

              const inputText = word.text.toLowerCase();
              const exactMatches = options.filter(opt =>
                opt.label.toLowerCase().startsWith(inputText)
              );
              const partialMatches = options.filter(opt =>
                !opt.label.toLowerCase().startsWith(inputText) &&
                opt.label.toLowerCase().includes(inputText)
              );

              const filteredOptions = [...exactMatches, ...partialMatches];

              if (filteredOptions.length === 0) return null;

              return {
                from: word.from,
                options: filteredOptions.slice(0, 8)
              };
            },

            // 🔑 watchData 代码片段自动完成
            (context) => {
              const line = context.state.doc.lineAt(context.pos);
              const lineText = line.text;

              // 检测是否在输入 watchData 相关的代码
              const watchDataPatterns = [
                { trigger: 'watchgold', snippet: 'watchData(\'$gameParty._gold\', (newValue, oldValue, key) => {\n  console.log(\'金币变化:\', newValue);\n  // 在这里处理金币变化\n});', info: '监听金币变化' },
                { trigger: 'watchvar', snippet: 'watchData(\'$gameVariables._dataClass[1]\', (newValue, oldValue, key) => {\n  console.log(\'变量1变化:\', newValue);\n  // 在这里处理变量变化\n});', info: '监听游戏变量变化' },
                { trigger: 'watchswitch', snippet: 'watchData(\'$gameSwitches._dataClass[1]\', (newValue, oldValue, key) => {\n  console.log(\'开关1变化:\', newValue);\n  // 在这里处理开关变化\n});', info: '监听游戏开关变化' },
                { trigger: 'watchexp', snippet: 'watchData(\'$gameActors._data[1]._exp\', (newValue, oldValue, key) => {\n  console.log(\'经验值变化:\', newValue);\n  // 在这里处理经验值变化\n});', info: '监听角色经验值变化' },
                { trigger: 'watchlevel', snippet: 'watchData(\'$gameActors._data[1]._level\', (newValue, oldValue, key) => {\n  console.log(\'等级变化:\', newValue);\n  // 在这里处理等级变化\n});', info: '监听角色等级变化' },
                { trigger: 'watchobj', snippet: '// 创建自定义对象\nconst myData = { count: 0, name: \'test\' };\n\n// 监听对象的所有属性变化\nwatchData(myData, (newValue, oldValue, key) => {\n  console.log(`属性 ${key} 变化:`, newValue);\n  if (key === \'count\') {\n    self.text = `计数: ${newValue}`;\n  }\n});\n\n// 修改属性触发监听\nmyData.count = 1;', info: '监听自定义对象变化' },
                { trigger: 'textauto', snippet: '// 🔥 text 属性自动类型转换示例\n\n// 数字自动转换\nself.text = 123;           // 显示 "123"\nself.text = 3.14;          // 显示 "3.14"\n\n// 布尔值自动转换\nself.text = true;          // 显示 "true"\nself.text = false;         // 显示 "false"\n\n// 对象自动转换为JSON\nself.text = { hp: 100 };   // 显示 "{\\"hp\\":100}"\n\n// null/undefined 转换为空字符串\nself.text = null;          // 显示 ""\nself.text = undefined;     // 显示 ""', info: '文本自动类型转换示例' }
              ];

              for (const pattern of watchDataPatterns) {
                if (lineText.includes(pattern.trigger)) {
                  const triggerStart = lineText.lastIndexOf(pattern.trigger);
                  const from = line.from + triggerStart;
                  const to = line.from + triggerStart + pattern.trigger.length;

                  return {
                    from,
                    to,
                    options: [{
                      label: pattern.trigger,
                      type: 'snippet',
                      info: pattern.info,
                      detail: 'watchData 代码片段',
                      apply: pattern.snippet
                    }]
                  };
                }
              }

              return null;
            }
          ]
        }),

        // 键盘映射
        keymap.of([
          ...completionKeymap,
          ...defaultKeymap,
          ...historyKeymap,
          ...searchKeymap,
          {
            key: 'Ctrl-s',
            run: () => {
              saveCode();
              return true;
            }
          },
          {
            key: 'Escape',
            run: () => {
              const hasAutoComplete = document.querySelector('.cm-tooltip-autocomplete');
              if (hasAutoComplete) {
                return false; // 让默认处理器关闭自动完成
              }
              cancelEdit();
              return true;
            }
          }
        ]),

        // 监听内容变化
        EditorView.updateListener.of((update) => {
          if (update.docChanged) {
            hasUnsavedChanges = true;
            updateTitle();
          }
        })
      ]
    });

    // 创建编辑器视图
    editor = new EditorView({
      state,
      parent: editorContainer
    });

    console.log('✅ 代码编辑器初始化完成');
    console.log('🔧 编辑器实例:', editor);
    console.log('🔧 编辑器是否可编辑:', !editor.state.readOnly);

    // 立即设置焦点
    editor.focus();
    console.log('🔧 编辑器已获得焦点');
  }

  // 更新标题
  function updateTitle() {
    const title = currentTitle + (hasUnsavedChanges ? ' *' : '');
    if (appWindow) {
      appWindow.setTitle(`代码编辑器 - ${title}`);
    }
  }

  // 保存代码
  async function saveCode() {
    if (!editor || !appWindow) return;

    const code = editor.state.doc.toString();
    console.log('💾 保存代码:', code);

    try {
      // 发送保存事件到主窗口
      await appWindow.emit('code-saved', {
        code,
        title: currentTitle
      });

      hasUnsavedChanges = false;
      updateTitle();

      // 不清理编辑器状态，保持代码可见
      // 用户可以继续编辑或手动关闭窗口
      console.log('💾 代码保存完成，可以继续编辑或手动关闭窗口');
    } catch (error) {
      console.log('保存或关闭时出错:', error);
      // 不显示alert，避免阻塞
    }
  }

  // 取消编辑
  function cancelEdit() {
    // 直接取消，不提示保存
    console.log('❌ 编辑已取消，请手动关闭窗口');
  }

  // 自动保存当前代码
  async function autoSaveCurrentCode() {
    if (!editor || !appWindow) {
      throw new Error('编辑器或窗口不可用');
    }

    const code = editor.state.doc.toString();
    console.log('🔧 自动保存当前代码:', code);

    try {
      // 发送保存事件到主窗口
      await appWindow.emit('code-saved', {
        code,
        title: currentTitle
      });

      hasUnsavedChanges = false;
      updateTitle();

      console.log('✅ 自动保存完成');
    } catch (error) {
      console.error('❌ 自动保存失败:', error);
      throw new Error('自动保存失败: ' + error);
    }
  }

  // 更新编辑器内容
  async function updateEditorContent(code: string, title: string) {
    console.log('🔧 更新编辑器内容:', { code, title });

    // 更新变量
    currentCode = code;
    currentTitle = title;
    hasUnsavedChanges = false;

    // 更新标题（这不会导致闪烁）
    updateTitle();

    // 如果编辑器已存在，平滑更新内容
    if (editor) {
      console.log('🔧 平滑更新现有编辑器内容');

      // 使用CodeMirror的事务系统平滑更新内容
      const transaction = editor.state.update({
        changes: {
          from: 0,
          to: editor.state.doc.length,
          insert: code
        },
        // 保持选择位置在开头，避免跳转
        selection: { anchor: 0 }
      });

      editor.dispatch(transaction);

      // 内容更新后聚焦编辑器
      setTimeout(() => {
        if (editor) {
          editor.focus();
        }
      }, 50);
    } else {
      console.log('🔧 编辑器不存在，初始化编辑器');
      initializeEditor();
    }

    console.log('✅ 编辑器内容更新完成');
  }

  // 组件挂载
  onMount(() => {
    console.log('🔧 代码编辑器窗口初始化');

    // 添加全局错误处理
    window.addEventListener('unhandledrejection', (event) => {
      if (event.reason && event.reason.toString().includes('window not found')) {
        console.log('🔧 忽略窗口关闭相关的Promise拒绝');
        event.preventDefault();
      }
    });

    if (!appWindow) {
      throw new Error('❌ Tauri API不可用');
    }

    // 监听初始化数据
    console.log('🔧 开始监听init-code事件');
    appWindow.listen('init-code', (event: any) => {
      console.log('📥 接收到初始化数据:', event.payload);
      const { code, title } = event.payload;

      if (code === undefined || title === undefined) {
        console.error('❌ 初始化数据不完整:', { code, title });
        throw new Error('❌ 初始化数据不完整');
      }

      console.log('🔧 开始处理初始化数据:', {
        code,
        title,
        codeLength: code?.length || 0,
        editorContainer: !!editorContainer,
        currentEditor: !!editor
      });

      currentCode = code;
      currentTitle = title;
      hasUnsavedChanges = false;

      console.log('🔧 设置代码和标题完成, currentCode:', currentCode);
      updateTitle();

      console.log('🔧 准备初始化编辑器');
      initializeEditor();
      console.log('🔧 编辑器初始化调用完成');
    }).then(() => {
      console.log('✅ init-code事件监听器已设置');

      // 主动请求初始化数据
      console.log('🔧 主动请求初始化数据');
      appWindow.emit('request-init-data').catch((error) => {
        console.error('❌ 请求初始化数据失败:', error);
      });
    }).catch((error) => {
      console.error('❌ 设置事件监听器失败:', error);
    });

    // 监听ping事件（用于窗口存活检测）
    appWindow.listen('ping', () => {
      console.log('🏓 收到ping，窗口存活');
      // 不需要响应，只要不抛出错误就表示窗口存活
    });

    // 监听自动保存请求
    appWindow.listen('auto-save-request', async () => {
      console.log('🔧 收到自动保存请求');
      if (hasUnsavedChanges) {
        console.log('🔧 有未保存更改，执行自动保存');
        await autoSaveCurrentCode();
      } else {
        console.log('🔧 没有未保存更改，跳过自动保存');
      }
    });

    // 监听内容更新事件
    appWindow.listen('update-code', async (event: any) => {
      console.log('🔄 接收到内容更新请求:', event.payload);
      const { code, title } = event.payload;

      try {
        // 直接更新编辑器内容（自动保存已在管理器中处理）
        await updateEditorContent(code, title);

        console.log('✅ 编辑器内容更新完成');
      } catch (error) {
        console.error('❌ 更新编辑器内容失败:', error);
        throw error;
      }
    });

    // 添加一个测试：5秒后检查是否收到了数据
    // setTimeout(() => {
    //   console.log('⏰ 5秒检查 - currentCode:', currentCode);
    //   console.log('⏰ 5秒检查 - currentTitle:', currentTitle);
    //   console.log('⏰ 5秒检查 - editor:', editor);
    //   if (!editor) {
    //     console.warn('⚠️ 5秒后仍未初始化编辑器');
    //   }
    // }, 5000);

    // 监听窗口关闭请求，直接关闭不提示保存
    appWindow.onCloseRequested(async () => {
      try {
        console.log('🔧 窗口关闭请求，直接关闭');
        // 直接允许关闭，不提示保存
      } catch (error) {
        console.log('处理窗口关闭请求时出错:', error);
      }
    });

    // 清理函数
    return () => {
      console.log('🔧 组件销毁，清理编辑器');
      try {
        if (editor) {
          editor.destroy();
          editor = null;
        }
      } catch (error) {
        console.log('清理编辑器时出错:', error);
      }
    };
  });
</script>

<div class="code-editor-window" on:keydown={handleKeyDown}>
  <!-- 主内容区域：70%编辑器 + 30%侧边栏 -->
  <div class="main-content">
    <!-- 编辑器区域 (70%) -->
    <div class="editor-section">
      <!-- 编辑器工具栏 -->
      <div class="editor-toolbar">
        <button class="toolbar-btn" on:click={formatCode} title="格式化代码 (Ctrl+Shift+F)">
          <span class="btn-icon">🎨</span>
          <span class="btn-text">格式化</span>
        </button>
        <div class="toolbar-spacer"></div>
        <div class="toolbar-info">
          <span class="file-info">🔧 {currentTitle}{hasUnsavedChanges ? ' *' : ''}</span>
        </div>
      </div>

      <div class="editor-container" bind:this={editorContainer}></div>

      <div class="editor-footer">
        <div class="editor-tips">
          <span>💡 提示：使用 Ctrl+S 保存，ESC 取消，Ctrl+Shift+F 格式化</span>
        </div>
      </div>
    </div>

    <!-- API侧边栏 (30%) -->
    <div class="sidebar-section">
      <APISidebar onCodeInsert={handleCodeInsert} />
    </div>
  </div>
</div>

<style>
  /* 全局样式重置 */
  :global(html, body) {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  :global(#app) {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .code-editor-window {
    display: flex;
    flex-direction: column;
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    background: #1e1e1e;
    color: #ffffff;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
  }

  .main-content {
    display: flex;
    flex: 1;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .editor-section {
    display: flex;
    flex-direction: column;
    width: 70%;
    height: 100%;
    overflow: hidden;
  }

  .sidebar-section {
    width: 30%;
    height: 100%;
    overflow: hidden;
  }

  .editor-container {
    flex: 1;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    position: relative;
    overflow: hidden;
  }

  .editor-footer {
    background: #2d2d2d;
    padding: 8px 16px;
    border-top: 1px solid #404040;
    font-size: 11px;
    color: #cccccc;
    flex-shrink: 0;
  }

  .editor-tips {
    display: flex;
    gap: 20px;
  }

  /* 编辑器工具栏样式 */
  .editor-toolbar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #252525;
    border-bottom: 1px solid #404040;
    flex-shrink: 0;
    gap: 8px;
  }

  .toolbar-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: #404040;
    border: 1px solid #555555;
    border-radius: 4px;
    color: #ffffff;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    outline: none;
  }

  .toolbar-btn:hover {
    background: #4a4a4a;
    border-color: #007acc;
  }

  .toolbar-btn:active {
    background: #007acc;
    transform: translateY(1px);
  }

  .btn-icon {
    font-size: 14px;
  }

  .btn-text {
    font-weight: 500;
  }

  .toolbar-spacer {
    flex: 1;
  }

  .toolbar-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .file-info {
    font-size: 12px;
    color: #cccccc;
    font-weight: 500;
  }

  /* CodeMirror样式优化 */
  :global(.cm-editor) {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
  }

  :global(.cm-focused) {
    outline: none;
  }

  :global(.cm-scroller) {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: auto;
  }

  :global(.cm-content) {
    width: 100%;
    min-height: 100%;
    margin: 0;
    padding: 8px;
  }

  /* 自定义滚动条样式 */
  :global(.cm-scroller::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :global(.cm-scroller::-webkit-scrollbar-track) {
    background: #1e1e1e;
  }

  :global(.cm-scroller::-webkit-scrollbar-thumb) {
    background: #404040;
    border-radius: 4px;
  }

  :global(.cm-scroller::-webkit-scrollbar-thumb:hover) {
    background: #505050;
  }

  :global(.cm-scroller::-webkit-scrollbar-corner) {
    background: #1e1e1e;
  }

  :global(.cm-tooltip-autocomplete) {
    background: #2d3748;
    border: 1px solid #4a5568;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  :global(.cm-completionLabel) {
    color: #e2e8f0;
  }

  :global(.cm-completionDetail) {
    color: #a0aec0;
    font-style: italic;
  }

  :global(.cm-completionInfo) {
    color: #cbd5e0;
    background: #1a202c;
    border: 1px solid #4a5568;
  }
</style>
