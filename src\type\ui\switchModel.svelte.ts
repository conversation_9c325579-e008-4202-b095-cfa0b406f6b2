import { BaseObjectModel } from '../baseObjectModel.svelte';
import type { Script } from '../script.svelte';

export class SwitchModel extends BaseObjectModel {

    constructor(switchObj: any) {
        super(switchObj);

        // 初始化开关特有属性
        this.isOn = Boolean(switchObj.isOn || false);
        this.value = this.isOn; // 🔑 与 isOn 同步
        this.enabled = switchObj.enabled !== false;
        this.animationDuration = switchObj.animationDuration || 200;

        // 🔑 编辑器控制属性
        this.executeEventsInEditor = switchObj.executeEventsInEditor || false;

        // 🔑 核心绑定组件（仅需2个）
        this.boundBackgroundSprite = switchObj.boundBackgroundSprite || null; // 背景轨道 (UIImage)
        this.boundKnobSprite = switchObj.boundKnobSprite || null;             // 滑块按钮 (UIImage)

        // 事件代码属性
        this.onChangeCode = switchObj._eventCodes?.onChange || '';
        this.onToggleCode = switchObj._eventCodes?.onToggle || '';

        // 🔑 统一的脚本系统 - 从原始对象获取脚本数组
        this.componentScripts = switchObj.componentScripts || [];

        // 🔑 脚本系统初始化 - 直接使用传入的脚本数组，不再重复初始化
        console.log('🔧 SwitchModel: 脚本系统初始化', {
            hasScripts: this.componentScripts.length > 0,
            scriptsCount: this.componentScripts.length,
            scriptNames: this.componentScripts.map(s => s.name)
        });

        console.log('🔧 SwitchModel: 创建开关模型', switchObj);

        // setupSync() 已经在基类构造函数中调用了

        // 🔑 延迟重建绑定关系（等待所有子对象加载完成）
        setTimeout(() => this.rebuildBindingsFromChildren(), 0);
    }

    // 开关状态属性
    isOn = $state(false);                // 开关状态 (true/false)
    value = $state(false);               // 🔑 当前值（与 isOn 同步）
    enabled = $state(true);              // 是否启用
    animationDuration = $state(200);     // 动画时长(ms)

    // 🔑 编辑器控制属性
    executeEventsInEditor = $state(false); // 是否在编辑器中执行事件

    // 🔑 核心绑定组件（仅需2个）- 存储模型对象
    boundBackgroundSprite = $state<BaseObjectModel | null>(null); // 绑定的背景轨道精灵模型 (UIImage)
    boundKnobSprite = $state<BaseObjectModel | null>(null);       // 绑定的滑块按钮精灵模型 (UIImage)

    // 事件代码属性
    onChangeCode = $state('');           // 状态改变事件代码
    onToggleCode = $state('');           // 切换事件代码

    // 🔑 统一的脚本系统
    componentScripts = $state<Script[]>([]);   // 脚本数组

    /**
     * 🔑 从子对象重建绑定关系（用于加载保存的场景）
     * 根据子对象的类型和数量自动识别并重建绑定关系
     */
    rebuildBindingsFromChildren(): void {
        console.log('🔄 SwitchModel: 开始重建绑定关系', {
            childrenCount: this.children.length,
            children: this.children.map(child => ({
                className: child.className,
                name: child.name
            }))
        });

        // 清空现有绑定
        this.boundBackgroundSprite = null;
        this.boundKnobSprite = null;

        // 遍历子对象，根据顺序重建绑定
        const uiImages = this.children.filter(child => child.className === 'UIImage');

        if (uiImages.length >= 1) {
            this.boundBackgroundSprite = uiImages[0];
            console.log('🔗 重建绑定: 背景轨道精灵', uiImages[0].name);
        }

        if (uiImages.length >= 2) {
            this.boundKnobSprite = uiImages[1];
            console.log('🔗 重建绑定: 滑块按钮精灵', uiImages[1].name);
        }

        console.log('✅ SwitchModel: 绑定关系重建完成', {
            boundBackgroundSprite: this.boundBackgroundSprite?.name || 'null',
            boundKnobSprite: this.boundKnobSprite?.name || 'null'
        });
    }

    /**
     * 设置Switch特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Switch特有的属性
     */
    protected setupSpecificSync(): void {
        // 同步开关特有属性
        if (this._originalObject.setValue && typeof this._originalObject.setValue === 'function') {
            this._originalObject.setValue(this.isOn);
        } else {
            this._originalObject.isOn = this.isOn;
        }

        // 🔑 同步 value 属性（确保与 isOn 一致）
        this.value = this.isOn;
        this._originalObject.value = this.value;

        // 🔑 同步子组件绑定属性 - 调用绑定方法而不是直接设置属性
        // 这样可以触发 UISwitch 的尺寸计算和布局更新

        // 🔑 绑定背景轨道：从模型对象获取原始对象
        if (this.boundBackgroundSprite) {
            // 🔧 安全检查：确保绑定对象有getOriginalObject方法
            if (typeof this.boundBackgroundSprite.getOriginalObject === 'function') {
                const originalBgSprite = this.boundBackgroundSprite.getOriginalObject();
                if (this._originalObject.bindBackgroundSprite) {
                    this._originalObject.bindBackgroundSprite(originalBgSprite);
                } else {
                    this._originalObject.boundBackgroundSprite = originalBgSprite;
                }
            } else {
                console.warn('🚨 SwitchModel: boundBackgroundSprite 不是有效的模型对象，跳过绑定');
                this._originalObject.boundBackgroundSprite = null;
            }
        } else {
            this._originalObject.boundBackgroundSprite = null;
        }

        // 🔑 绑定滑块按钮：从模型对象获取原始对象
        if (this.boundKnobSprite) {
            // 🔧 安全检查：确保绑定对象有getOriginalObject方法
            if (typeof this.boundKnobSprite.getOriginalObject === 'function') {
                const originalKnobSprite = this.boundKnobSprite.getOriginalObject();
                if (this._originalObject.bindKnobSprite) {
                    this._originalObject.bindKnobSprite(originalKnobSprite);
                } else {
                    this._originalObject.boundKnobSprite = originalKnobSprite;
                }
            } else {
                console.warn('🚨 SwitchModel: boundKnobSprite 不是有效的模型对象，跳过绑定');
                this._originalObject.boundKnobSprite = null;
            }
        } else {
            this._originalObject.boundKnobSprite = null;
        }

        // 同步行为属性
        if (this._originalObject.setEnabled && typeof this._originalObject.setEnabled === 'function') {
            this._originalObject.setEnabled(this.enabled);
        } else {
            this._originalObject.enabled = this.enabled;
        }

        if (this._originalObject.setAnimationDuration && typeof this._originalObject.setAnimationDuration === 'function') {
            this._originalObject.setAnimationDuration(this.animationDuration);
        } else {
            this._originalObject.animationDuration = this.animationDuration;
        }

        // 🔑 同步编辑器控制属性
        this._originalObject.executeEventsInEditor = this.executeEventsInEditor;

        // 同步事件代码属性
        if (!this._originalObject._eventCodes) {
            this._originalObject._eventCodes = {};
        }
        this._originalObject._eventCodes.onChange = this.onChangeCode;
        this._originalObject._eventCodes.onToggle = this.onToggleCode;

        // 🔑 同步脚本系统
        this._originalObject.componentScripts = this.componentScripts;
    }

    /**
     * 设置开关状态
     */
    public setValue(isOn: boolean): void {
        this.isOn = Boolean(isOn);
    }

    /**
     * 切换开关状态
     */
    public toggle(): void {
        this.setValue(!this.isOn);
    }

    /**
     * 获取开关状态
     */
    public getValue(): boolean {
        return this.isOn;
    }

    /**
     * 设置动画时长
     */
    public setAnimationDuration(duration: number): void {
        this.animationDuration = Math.max(0, duration);
    }

    /**
     * 获取绑定状态信息
     */
    public getBindingInfo(): {
        hasBackground: boolean;
        hasKnob: boolean;
        backgroundSprite?: any;
        knobSprite?: any;
    } {
        return {
            hasBackground: this.boundBackgroundSprite !== null,
            hasKnob: this.boundKnobSprite !== null,
            backgroundSprite: this.boundBackgroundSprite,
            knobSprite: this.boundKnobSprite
        };
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 创建UISwitch容器对象
        codes.push(`${indent}const ${varName} = new UISwitch({`);
        codes.push(`${indent}    width: ${this.width},`);
        codes.push(`${indent}    height: ${this.height},`);
        codes.push(`${indent}    isOn: ${this.isOn},`);
        codes.push(`${indent}    enabled: ${this.enabled},`);
        codes.push(`${indent}    animationDuration: ${this.animationDuration}`);
        codes.push(`${indent}});`);

        // 设置基础属性
        codes.push(`${indent}${varName}.x = ${this.x};`);
        codes.push(`${indent}${varName}.y = ${this.y};`);

        if (this.name) {
            codes.push(`${indent}${varName}.name = '${this.name}';`);
        }

        return codes.join('\n');
    }

    /**
     * 重写代码生成方法，确保正确的生成顺序
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 生成的代码字符串
     */
    public generateCreationCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        // 1. 对象创建代码
        codes.push(this.generateObjectCreation(varName, indent));

        // 2. 基础属性设置
        codes.push(this.generateBasicProperties(varName, indent));

        // 3. 特定属性设置（不包含绑定）
        codes.push(this.generateSpecificProperties(varName, indent));

        // 4. 子对象代码生成
        if (this.generateChildrenCode) {
            codes.push(this.generateChildrenCreation(varName, indent));
        }

        // 5. 🔑 绑定代码（在子对象创建之后）
        codes.push(this.generateBindingCode(varName, indent));

        return codes.filter(code => code.trim()).join('\n');
    }

    /**
     * 生成绑定代码（在子对象创建之后调用）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 绑定代码
     */
    public generateBindingCode(varName: string, indent: string): string {
        const codes: string[] = [];

        // 生成绑定代码
        if (this.boundBackgroundSprite) {
            const bgVarName = this.getChildVariableName(this.boundBackgroundSprite, varName);
            codes.push(`${indent}${varName}.bindBackgroundSprite(${bgVarName});`);
        }

        if (this.boundKnobSprite) {
            const knobVarName = this.getChildVariableName(this.boundKnobSprite, varName);
            codes.push(`${indent}${varName}.bindKnobSprite(${knobVarName});`);
        }

        // 生成事件代码 - 设置为字符串，与 UIButton 保持一致
        if (this.onChangeCode.trim()) {
            codes.push(`${indent}${varName}.onChangeCode = \`${this.onChangeCode}\`;`);
        }

        if (this.onToggleCode.trim()) {
            codes.push(`${indent}${varName}.onToggleCode = \`${this.onToggleCode}\`;`);
        }

        return codes.join('\n');
    }

    /**
     * 获取子对象的变量名
     */
    private getChildVariableName(childObject: any, parentVarName: string): string {
        // 🔑 现在childObject是模型对象，需要直接比较模型对象
        const childIndex = this.children.findIndex(child => child === childObject);
        if (childIndex !== -1) {
            return `${parentVarName}_child${childIndex}`;
        }

        // 🔑 如果找不到，说明绑定的对象不在children中，这是错误的
        console.error('🚨 SwitchModel: 无法找到绑定对象的变量名', {
            childObject: childObject?.className,
            parentVarName,
            childrenCount: this.children.length
        });
        return `/* ERROR: 找不到绑定对象 ${childObject?.className || 'unknown'} */`;
    }

    /**
     * 重写克隆方法 - 调用插件的 clone 方法
     */
    public clone(): SwitchModel {
        console.log('🔄 SwitchModel: 开始克隆Switch对象（调用插件方法）');

        // 1. 调用原始 UISwitch 对象的 clone 方法
        const originalUISwitch = this.getOriginalObject();
        if (!originalUISwitch || typeof originalUISwitch.clone !== 'function') {
            console.error('❌ SwitchModel: 原始对象没有 clone 方法');
            throw new Error('UISwitch 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUISwitch = originalUISwitch.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 SwitchModel 包装克隆的对象
        const clonedModel = new SwitchModel(clonedUISwitch);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ SwitchModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UISwitch 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UISwitch 特有属性
            isOn: this.isOn,
            enabled: this.enabled,
            animationDuration: this.animationDuration,

            // 事件代码
            onToggle: this.onToggleCode,
            onTurnOn: this.onChangeCode, // 复用 onChange 作为 onTurnOn
            onTurnOff: this.onChangeCode, // 复用 onChange 作为 onTurnOff

            // 🔑 统一的脚本系统
            componentScripts: this.componentScripts
        };
    }

    /**
     * 🔑 重写对象创建代码生成（参考 UILabel）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 创建 UISwitch 对象
        codes.push(`${indent}const ${varName} = new UISwitch({`);

        // 基础属性
        codes.push(`${indent}    width: ${this.width},`);
        codes.push(`${indent}    height: ${this.height},`);
        codes.push(`${indent}    isOn: ${this.isOn},`);
        codes.push(`${indent}    enabled: ${this.enabled},`);
        codes.push(`${indent}    animationDuration: ${this.animationDuration},`);

        // 🔑 编辑器控制属性
        if (this.executeEventsInEditor) {
            codes.push(`${indent}    executeEventsInEditor: ${this.executeEventsInEditor},`);
        }

        // 🔑 生成组件脚本数组（如果有的话）
        if (this.componentScripts && this._hasNonEmptyScripts()) {
            codes.push(`${indent}    componentScripts: ${JSON.stringify(this.componentScripts, null, 8).replace(/\n/g, `\n${indent}`)},`);
        }

        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 🔑 检查是否有非空的脚本内容（参考 UILabel）
     */
    private _hasNonEmptyScripts(): boolean {
        if (!this.componentScripts || this.componentScripts.length === 0) return false;

        // 检查是否有启用且有代码的脚本
        return this.componentScripts.some(script =>
            script.enabled && script.code && script.code.trim().length > 0
        );
    }

    /**
     * 重写销毁方法
     */
    public destroy(): void {
        console.log(`🔧 SwitchModel: 销毁开关模型 ${this.className}`);

        // 清理绑定的组件引用
        this.boundBackgroundSprite = null;
        this.boundKnobSprite = null;

        // 调用基类销毁方法
        super.destroy();
    }
}

// 注册SwitchModel到基类容器
BaseObjectModel.registerModel('UISwitch', SwitchModel);
BaseObjectModel.registerModel('Switch', SwitchModel);
