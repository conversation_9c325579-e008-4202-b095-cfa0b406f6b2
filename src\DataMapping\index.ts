/**
 * DataMapping 主入口文件
 */

// 导出类型
export type { SceneDataFlow, ButtonDataFlow } from './types';

// 导出场景数据
export { getSceneDataFlow, getSupportedScenes, SceneTitleDataFlow } from './scenes';

// 导出组件
export { default as DataFlowModal } from './DataFlowModal.svelte';

/**
 * 获取当前RPG Maker MZ场景名称
 */
export function getCurrentSceneName(): string | null {
  try {
    if (typeof window === 'undefined' || !(window as any).SceneManager) {
      return null;
    }

    const sceneManager = (window as any).SceneManager;
    const currentScene = sceneManager._scene;

    if (!currentScene) {
      return null;
    }

    return currentScene.constructor.name;
  } catch (error) {
    console.warn('获取当前场景失败:', error);
    return null;
  }
}
