import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * MaskModel - UIMask的模型对象
 * 继承自BaseObjectModel，管理UIMask的所有属性和状态
 */
export class MaskModel extends BaseObjectModel {
    // 组件类型
    public readonly componentType = 'UIMask';

    // 🎭 遮罩类型
    maskType = $state('rectangle'); // 'rectangle', 'circle', 'image'

    // 🎭 图片路径（当类型为image时）
    maskImage = $state('');

    // 🔑 脚本系统
    componentScripts = $state<any[]>([]);

    constructor(mask: any = {}) {
        super(mask);

        // 初始化遮罩属性 - v3.0版本
        this.maskType = mask.maskType || 'rectangle';
        this.maskImage = mask.maskImage || '';

        // 🔑 统一的脚本系统 - 从原始对象获取脚本数组
        this.componentScripts = mask.componentScripts || [];

        console.log('🎭 MaskModel: 创建遮罩模型 v3.0', {
            maskType: this.maskType,
            size: { width: this.width, height: this.height },
            childrenCount: this.children.length,
            hasScripts: this.componentScripts.length > 0,
            scriptsCount: this.componentScripts.length
        });
    }

    /**
     * 设置Mask特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Mask特有的属性
     */
    protected setupSpecificSync(): void {
        // 同步遮罩特有属性 - 简化版本
        this._originalObject.maskType = this.maskType;
        this._originalObject.maskImage = this.maskImage;

        // 🔑 触发遮罩重绘
        if (typeof this._originalObject.drawMask === 'function') {
            this._originalObject.drawMask();
        }

        // 🔑 对所有子对象应用遮罩效果
        if (typeof this._originalObject.applyMaskToAllChildren === 'function') {
            this._originalObject.applyMaskToAllChildren();
        }

        console.log('🎭 MaskModel: 遮罩属性同步完成', {
            maskType: this.maskType,
            size: `${this.width}x${this.height}`
        });
    }



    /**
     * 对所有子对象应用遮罩效果（模型层面的辅助方法）
     */
    public applyMaskToAllChildren(): void {
        console.log('🎭 MaskModel: 对所有子对象应用遮罩效果');

        if (this._originalObject && this._originalObject.applyMaskToAllChildren) {
            this._originalObject.applyMaskToAllChildren();
        }
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 创建UIMask容器对象 - v3.0版本
        codes.push(`${indent}const ${varName} = new UIMask({`);
        codes.push(`${indent}    width: ${this.width},`);
        codes.push(`${indent}    height: ${this.height},`);
        codes.push(`${indent}    maskType: '${this.maskType}',`);
        codes.push(`${indent}    maskImage: '${this.maskImage}',`);

        // 🔑 生成组件脚本数组（如果有的话）
        if (this.componentScripts && this._hasNonEmptyScripts()) {
            codes.push(`${indent}    componentScripts: ${JSON.stringify(this.componentScripts, null, 8).replace(/\n/g, `\n${indent}`)}`);
        } else {
            // 移除最后一个逗号
            const lastLine = codes[codes.length - 1];
            codes[codes.length - 1] = lastLine.replace(/,$/, '');
        }

        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 🔑 检查是否有非空的脚本内容
     */
    private _hasNonEmptyScripts(): boolean {
        if (!this.componentScripts || this.componentScripts.length === 0) return false;

        // 检查是否有启用且有代码的脚本
        return this.componentScripts.some(script =>
            script.enabled && script.code && script.code.trim().length > 0
        );
    }

    /**
     * 重写特定属性设置代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns Mask特定属性设置代码
     */
    protected generateSpecificProperties(_varName: string, _indent: string): string {
        const codes: string[] = [];

        // 🔑 不在这里生成绑定代码，因为子对象还没创建
        // 绑定代码将在 generateBindingCode() 中生成

        // 如果有特殊的遮罩配置，可以在这里添加
        // 目前遮罩的主要属性都在构造函数中设置了

        return codes.join('\n');
    }

    /**
     * 重写代码生成方法，确保正确的生成顺序
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 生成的代码字符串
     */
    public generateCreationCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        // 1. 对象创建代码
        codes.push(this.generateObjectCreation(varName, indent));

        // 2. 基础属性设置
        codes.push(this.generateBasicProperties(varName, indent));

        // 3. 特定属性设置（不包含绑定）
        codes.push(this.generateSpecificProperties(varName, indent));

        // 4. 子对象代码生成
        if (this.generateChildrenCode) {
            codes.push(this.generateChildrenCreation(varName, indent));
        }

        // 5. 🔑 绑定代码（在子对象创建之后）
        codes.push(this.generateBindingCode(varName, indent));

        return codes.filter(code => code.trim()).join('\n');
    }

    /**
     * 生成遮罩应用代码（在子对象创建之后调用）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 遮罩应用代码
     */
    protected generateBindingCode(_varName: string, _indent: string): string {
        // 🔑 不需要生成额外的遮罩应用代码
        // 因为 UIMask.addChild() 会自动对每个新添加的子对象应用遮罩
        return '';
    }

    /**
     * 克隆当前Mask对象 - 调用插件的 clone 方法
     */
    clone(): MaskModel {
        console.log('🔄 MaskModel: 开始克隆Mask对象（调用插件方法）');

        // 1. 调用原始 UIMask 对象的 clone 方法
        const originalUIMask = this.getOriginalObject();
        if (!originalUIMask || typeof originalUIMask.clone !== 'function') {
            console.error('❌ MaskModel: 原始对象没有 clone 方法');
            throw new Error('UIMask 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUIMask = originalUIMask.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 MaskModel 包装克隆的对象
        const clonedModel = new MaskModel(clonedUIMask);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ MaskModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UIMask 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UIMask 特有属性 - v3.0版本
            maskType: this.maskType,
            maskImage: this.maskImage,

            // 🔑 脚本系统
            componentScripts: this.componentScripts
        };
    }

    /**
     * 获取遮罩状态信息
     */
    public getMaskStatus(): {
        maskType: string;
        maskImage: string;
        childrenCount: number;
        hasChildren: boolean;
    } {
        return {
            maskType: this.maskType,
            maskImage: this.maskImage,
            childrenCount: this.children.length,
            hasChildren: this.children.length > 0
        };
    }

    /**
     * 获取遮罩信息摘要 - v3.0版本
     */
    getMaskInfo(): any {
        return {
            maskType: this.maskType,
            maskImage: this.maskImage,
            size: { width: this.width, height: this.height }, // 🔑 直接使用对象的width和height
            childrenCount: this.children.length,
            hasChildren: this.children.length > 0
        };
    }


}

// 注册MaskModel到基类容器
BaseObjectModel.registerModel('UIMask', MaskModel);
BaseObjectModel.registerModel('Mask', MaskModel);
