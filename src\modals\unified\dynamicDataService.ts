import type { Category, DataItem, DataField } from './types';

// 扩展 Window 类型以包含 RPG Maker MZ 的全局变量
declare global {
  interface Window {
    $dataActors: any[];
    $dataClasses: any[];
    $dataSkills: any[];
    $dataItems: any[];
    $dataWeapons: any[];
    $dataArmors: any[];
    $dataEnemies: any[];
    $dataTroops: any[];
    $dataStates: any[];
    $dataAnimations: any[];
    $dataTilesets: any[];
    $dataCommonEvents: any[];
    $dataSystem: any;
    $dataMapInfos: any[];
    $dataMap: any;
    $gameTemp: any;
    $gameSystem: any;
    $gameScreen: any;
    $gameTimer: any;
    $gameMessage: any;
    $gameSwitches: any;
    $gameVariables: any;
    $gameSelfSwitches: any;
    $gameActors: any;
    $gameParty: any;
    $gameTroop: any;
    $gameMap: any;
    $gamePlayer: any;
    DataManager: any;
    SceneManager: any;
    BattleManager: any;
    ImageManager: any;
    AudioManager: any;
    PluginManager: any;
    StorageManager: any;
  }
}

/**
 * 检测值的类型
 */
function detectFieldType(value: any): 'string' | 'number' | 'boolean' | 'object' | 'array' {
  if (value === null || value === undefined) return 'string';
  if (Array.isArray(value)) return 'array';
  if (typeof value === 'object') return 'object';
  if (typeof value === 'boolean') return 'boolean';
  if (typeof value === 'number') return 'number';
  return 'string';
}

/**
 * 安全地获取对象的字段
 */
function getObjectFields(obj: any, basePath: string, maxDepth: number = 2, currentDepth: number = 0): DataField[] {
  const fields: DataField[] = [];
  
  if (!obj || typeof obj !== 'object' || currentDepth >= maxDepth) {
    return fields;
  }

  try {
    const keys = Object.keys(obj);
    
    for (const key of keys) {
      try {
        const value = obj[key];
        
        // 跳过函数和特殊属性
        if (typeof value === 'function' || key.startsWith('_') || key === 'constructor') {
          continue;
        }

        const fieldPath = `${basePath}.${key}`;
        const fieldType = detectFieldType(value);
        
        // 添加基础字段
        fields.push({
          name: key,
          label: `${key} (${fieldType})`,
          type: fieldType,
          description: `${basePath}.${key} - ${fieldType}类型`
        });

        // 如果是对象或数组，递归获取子字段（限制深度）
        if (currentDepth < maxDepth - 1) {
          if (fieldType === 'object' && value) {
            const subFields = getObjectFields(value, fieldPath, maxDepth, currentDepth + 1);
            fields.push(...subFields.slice(0, 10)); // 限制子字段数量
          } else if (fieldType === 'array' && Array.isArray(value) && value.length > 0) {
            // 对于数组，分析第一个有效元素的结构
            const firstValidItem = value.find(item => item && typeof item === 'object');
            if (firstValidItem) {
              const arrayItemFields = getObjectFields(firstValidItem, `${fieldPath}[index]`, maxDepth, currentDepth + 1);
              fields.push(...arrayItemFields.slice(0, 5)); // 限制数组项字段数量
            }
          }
        }
      } catch (error) {
        console.warn(`Error processing field ${key}:`, error);
      }
    }
  } catch (error) {
    console.warn(`Error processing object at ${basePath}:`, error);
  }

  return fields;
}

/**
 * 获取数组元素的字段
 */
function getArrayElementFields(array: any[], basePath: string, maxElements: number = 10): DataField[] {
  const fields: DataField[] = [];

  if (!Array.isArray(array)) {
    console.warn(`⚠️ getArrayElementFields: ${basePath} is not an array:`, typeof array);
    return fields;
  }

  console.log(`🔍 getArrayElementFields: Processing ${basePath}, array length:`, array.length);

  try {
    // 过滤出有效的对象元素，跳过 null 和 undefined
    const validElements = array.filter((item) => {
      if (item === null || item === undefined) {
        return false;
      }
      if (typeof item === 'object') {
        return true;
      }
      return false;
    }).slice(0, maxElements);

    console.log(`🔍 getArrayElementFields: Found ${validElements.length} valid elements in ${basePath}`);

    if (validElements.length === 0) {
      console.warn(`⚠️ getArrayElementFields: No valid elements found in ${basePath}`);
      return fields;
    }

    for (let i = 0; i < validElements.length; i++) {
      const element = validElements[i];
      const actualIndex = array.indexOf(element); // 获取实际索引
      const elementPath = `${basePath}[${actualIndex}]`;

      try {
        console.log(`🔍 Processing element ${actualIndex} of ${basePath}:`, element);
        // 获取元素的基本信息
        const elementFields = getObjectFields(element, elementPath, 2, 0);
        console.log(`🔍 Element ${actualIndex} generated ${elementFields.length} fields`);
        fields.push(...elementFields.slice(0, 8)); // 限制每个元素的字段数量
      } catch (error) {
        console.warn(`Error processing array element ${actualIndex}:`, error);
      }
    }
  } catch (error) {
    console.warn(`Error processing array at ${basePath}:`, error);
  }

  console.log(`🔍 getArrayElementFields: Total ${fields.length} fields generated for ${basePath}`);
  return fields;
}

/**
 * 动态获取 RPG Maker MZ 数据分类
 */
export function getDynamicDataCategories(): Record<string, Category> {
  const categories: Record<string, Category> = {};
  console.log('🔍 Starting dynamic data categories generation...');

  try {
    // 🎮 游戏数据 ($data*)
    const gameDataItems: Record<string, DataItem> = {};
    console.log('🔍 Checking window object:', typeof window);

    // 检查是否有 RPG Maker MZ 数据
    const rpgMakerKeys = Object.keys(window).filter(key => key.startsWith('$data') || key.startsWith('$game'));
    console.log('🔍 Available RPG Maker MZ properties:', rpgMakerKeys);

    if (rpgMakerKeys.length === 0) {
      console.warn('⚠️ No RPG Maker MZ data found in window object');
      return {}; // 返回空对象，让上层处理错误
    }

    // $dataActors - 角色数据
    if (window.$dataActors && Array.isArray(window.$dataActors)) {
      const fields = getArrayElementFields(window.$dataActors, '$dataActors', 5);
      gameDataItems.$dataActors = {
        id: '$dataActors',
        label: '角色数据',
        description: '所有角色的基础信息',
        path: '$dataActors',
        hasIndex: true,
        fields,
        example: '$dataActors[1].name'
      };
    }

    // $dataClasses - 职业数据
    if (window.$dataClasses && Array.isArray(window.$dataClasses)) {
      const fields = getArrayElementFields(window.$dataClasses, '$dataClasses', 5);
      gameDataItems.$dataClasses = {
        id: '$dataClasses',
        label: '职业数据',
        description: '职业和技能学习',
        path: '$dataClasses',
        hasIndex: true,
        fields
      };
    }

    // $dataSkills - 技能数据
    if (window.$dataSkills && Array.isArray(window.$dataSkills)) {
      const fields = getArrayElementFields(window.$dataSkills, '$dataSkills', 5);
      gameDataItems.$dataSkills = {
        id: '$dataSkills',
        label: '技能数据',
        description: '所有技能信息',
        path: '$dataSkills',
        hasIndex: true,
        fields
      };
    }

    // $dataItems - 物品数据
    if (window.$dataItems && Array.isArray(window.$dataItems)) {
      const fields = getArrayElementFields(window.$dataItems, '$dataItems', 5);
      gameDataItems.$dataItems = {
        id: '$dataItems',
        label: '物品数据',
        description: '消耗品和重要物品',
        path: '$dataItems',
        hasIndex: true,
        fields
      };
    }

    // $dataWeapons - 武器数据
    if (window.$dataWeapons && Array.isArray(window.$dataWeapons)) {
      const fields = getArrayElementFields(window.$dataWeapons, '$dataWeapons', 5);
      gameDataItems.$dataWeapons = {
        id: '$dataWeapons',
        label: '武器数据',
        description: '所有武器信息',
        path: '$dataWeapons',
        hasIndex: true,
        fields
      };
    }

    // $dataArmors - 防具数据
    if (window.$dataArmors && Array.isArray(window.$dataArmors)) {
      const fields = getArrayElementFields(window.$dataArmors, '$dataArmors', 5);
      gameDataItems.$dataArmors = {
        id: '$dataArmors',
        label: '防具数据',
        description: '所有防具信息',
        path: '$dataArmors',
        hasIndex: true,
        fields
      };
    }

    // $dataEnemies - 敌人数据
    if (window.$dataEnemies && Array.isArray(window.$dataEnemies)) {
      console.log('🔍 Processing $dataEnemies:', window.$dataEnemies.length, 'items');
      const fields = getArrayElementFields(window.$dataEnemies, '$dataEnemies', 5);
      console.log('🔍 $dataEnemies fields:', fields.length, 'fields generated');
      gameDataItems.$dataEnemies = {
        id: '$dataEnemies',
        label: '敌人数据',
        description: '所有敌人信息',
        path: '$dataEnemies',
        hasIndex: true,
        fields
      };
    } else {
      console.warn('⚠️ $dataEnemies not available or not an array:', typeof window.$dataEnemies);
    }

    // $dataTroops - 敌群数据
    if (window.$dataTroops && Array.isArray(window.$dataTroops)) {
      const fields = getArrayElementFields(window.$dataTroops, '$dataTroops', 5);
      gameDataItems.$dataTroops = {
        id: '$dataTroops',
        label: '敌群数据',
        description: '敌人组合配置',
        path: '$dataTroops',
        hasIndex: true,
        fields
      };
    }

    // $dataStates - 状态数据
    if (window.$dataStates && Array.isArray(window.$dataStates)) {
      const fields = getArrayElementFields(window.$dataStates, '$dataStates', 5);
      gameDataItems.$dataStates = {
        id: '$dataStates',
        label: '状态数据',
        description: '所有状态效果',
        path: '$dataStates',
        hasIndex: true,
        fields
      };
    }

    // $dataAnimations - 动画数据
    if (window.$dataAnimations && Array.isArray(window.$dataAnimations)) {
      const fields = getArrayElementFields(window.$dataAnimations, '$dataAnimations', 5);
      gameDataItems.$dataAnimations = {
        id: '$dataAnimations',
        label: '动画数据',
        description: '战斗和技能动画',
        path: '$dataAnimations',
        hasIndex: true,
        fields
      };
    }

    // $dataTilesets - 图块集数据
    if (window.$dataTilesets && Array.isArray(window.$dataTilesets)) {
      const fields = getArrayElementFields(window.$dataTilesets, '$dataTilesets', 5);
      gameDataItems.$dataTilesets = {
        id: '$dataTilesets',
        label: '图块集数据',
        description: '地图图块配置',
        path: '$dataTilesets',
        hasIndex: true,
        fields
      };
    }

    // $dataCommonEvents - 公共事件数据
    if (window.$dataCommonEvents && Array.isArray(window.$dataCommonEvents)) {
      const fields = getArrayElementFields(window.$dataCommonEvents, '$dataCommonEvents', 5);
      gameDataItems.$dataCommonEvents = {
        id: '$dataCommonEvents',
        label: '公共事件数据',
        description: '可重用的事件',
        path: '$dataCommonEvents',
        hasIndex: true,
        fields
      };
    }

    // $dataMapInfos - 地图信息数据
    if (window.$dataMapInfos && Array.isArray(window.$dataMapInfos)) {
      const fields = getArrayElementFields(window.$dataMapInfos, '$dataMapInfos', 5);
      gameDataItems.$dataMapInfos = {
        id: '$dataMapInfos',
        label: '地图信息数据',
        description: '所有地图的基本信息',
        path: '$dataMapInfos',
        hasIndex: true,
        fields
      };
    }

    // $dataMap - 当前地图数据
    if (window.$dataMap && typeof window.$dataMap === 'object') {
      const fields = getObjectFields(window.$dataMap, '$dataMap', 2);
      gameDataItems.$dataMap = {
        id: '$dataMap',
        label: '当前地图数据',
        description: '当前地图的详细数据',
        path: '$dataMap',
        hasIndex: false,
        fields
      };
    }

    // $dataSystem - 系统数据
    if (window.$dataSystem && typeof window.$dataSystem === 'object') {
      const fields = getObjectFields(window.$dataSystem, '$dataSystem', 2);
      gameDataItems.$dataSystem = {
        id: '$dataSystem',
        label: '系统数据',
        description: '游戏系统配置',
        path: '$dataSystem',
        hasIndex: false,
        fields
      };
    }

    categories.gameData = {
      id: 'gameData',
      label: '游戏数据',
      icon: '🎮',
      description: '静态游戏数据 ($data*)',
      items: gameDataItems
    };
  } catch (error) {
    console.warn('Error processing game data:', error);
  }

  try {
    // 🎯 游戏状态 ($game*)
    const gameStateItems: Record<string, DataItem> = {};

    // $gameSystem - 系统状态
    if (window.$gameSystem && typeof window.$gameSystem === 'object') {
      const fields = getObjectFields(window.$gameSystem, '$gameSystem', 2);
      gameStateItems.$gameSystem = {
        id: '$gameSystem',
        label: '系统状态',
        description: '游戏系统运行状态',
        path: '$gameSystem',
        hasIndex: false,
        fields
      };
    }

    // $gameParty - 队伍状态
    if (window.$gameParty && typeof window.$gameParty === 'object') {
      const fields = getObjectFields(window.$gameParty, '$gameParty', 2);
      gameStateItems.$gameParty = {
        id: '$gameParty',
        label: '队伍状态',
        description: '当前队伍信息和状态',
        path: '$gameParty',
        hasIndex: false,
        fields
      };
    }

    // $gameVariables - 变量
    if (window.$gameVariables && typeof window.$gameVariables === 'object') {
      const fields = getObjectFields(window.$gameVariables, '$gameVariables', 2);
      gameStateItems.$gameVariables = {
        id: '$gameVariables',
        label: '游戏变量',
        description: '游戏中的变量系统',
        path: '$gameVariables',
        hasIndex: false,
        fields
      };
    }

    // $gameSwitches - 开关
    if (window.$gameSwitches && typeof window.$gameSwitches === 'object') {
      const fields = getObjectFields(window.$gameSwitches, '$gameSwitches', 2);
      gameStateItems.$gameSwitches = {
        id: '$gameSwitches',
        label: '游戏开关',
        description: '游戏中的开关系统',
        path: '$gameSwitches',
        hasIndex: false,
        fields
      };
    }

    // $gameActors - 角色实例
    if (window.$gameActors && typeof window.$gameActors === 'object') {
      const fields = getObjectFields(window.$gameActors, '$gameActors', 2);
      gameStateItems.$gameActors = {
        id: '$gameActors',
        label: '角色实例',
        description: '当前游戏中的角色实例',
        path: '$gameActors',
        hasIndex: false,
        fields
      };
    }

    // $gamePlayer - 玩家
    if (window.$gamePlayer && typeof window.$gamePlayer === 'object') {
      const fields = getObjectFields(window.$gamePlayer, '$gamePlayer', 2);
      gameStateItems.$gamePlayer = {
        id: '$gamePlayer',
        label: '玩家角色',
        description: '玩家控制的角色',
        path: '$gamePlayer',
        hasIndex: false,
        fields
      };
    }

    // $gameTemp - 临时数据
    if (window.$gameTemp && typeof window.$gameTemp === 'object') {
      console.log('🔍 Processing $gameTemp:', window.$gameTemp);
      const fields = getObjectFields(window.$gameTemp, '$gameTemp', 2);
      console.log('🔍 $gameTemp fields:', fields.length, 'fields generated');
      gameStateItems.$gameTemp = {
        id: '$gameTemp',
        label: '临时数据',
        description: '临时游戏数据',
        path: '$gameTemp',
        hasIndex: false,
        fields
      };
    } else {
      console.warn('⚠️ $gameTemp not available or not an object:', typeof window.$gameTemp);
    }

    // $gameScreen - 屏幕效果
    if (window.$gameScreen && typeof window.$gameScreen === 'object') {
      const fields = getObjectFields(window.$gameScreen, '$gameScreen', 2);
      gameStateItems.$gameScreen = {
        id: '$gameScreen',
        label: '屏幕效果',
        description: '屏幕色调、闪烁等效果',
        path: '$gameScreen',
        hasIndex: false,
        fields
      };
    }

    // $gameTimer - 计时器
    if (window.$gameTimer && typeof window.$gameTimer === 'object') {
      const fields = getObjectFields(window.$gameTimer, '$gameTimer', 2);
      gameStateItems.$gameTimer = {
        id: '$gameTimer',
        label: '计时器',
        description: '游戏计时器状态',
        path: '$gameTimer',
        hasIndex: false,
        fields
      };
    }

    // $gameMessage - 消息系统
    if (window.$gameMessage && typeof window.$gameMessage === 'object') {
      const fields = getObjectFields(window.$gameMessage, '$gameMessage', 2);
      gameStateItems.$gameMessage = {
        id: '$gameMessage',
        label: '消息系统',
        description: '对话框和消息状态',
        path: '$gameMessage',
        hasIndex: false,
        fields
      };
    }

    // $gameSelfSwitches - 独立开关
    if (window.$gameSelfSwitches && typeof window.$gameSelfSwitches === 'object') {
      const fields = getObjectFields(window.$gameSelfSwitches, '$gameSelfSwitches', 2);
      gameStateItems.$gameSelfSwitches = {
        id: '$gameSelfSwitches',
        label: '独立开关',
        description: '事件独立开关状态',
        path: '$gameSelfSwitches',
        hasIndex: false,
        fields
      };
    }

    // $gameActors - 角色管理器
    if (window.$gameActors && typeof window.$gameActors === 'object') {
      const fields = getObjectFields(window.$gameActors, '$gameActors', 2);
      gameStateItems.$gameActors = {
        id: '$gameActors',
        label: '角色管理器',
        description: '所有角色的运行时状态',
        path: '$gameActors',
        hasIndex: false,
        fields
      };
    }

    // $gameTroop - 当前敌群
    if (window.$gameTroop && typeof window.$gameTroop === 'object') {
      const fields = getObjectFields(window.$gameTroop, '$gameTroop', 2);
      gameStateItems.$gameTroop = {
        id: '$gameTroop',
        label: '当前敌群',
        description: '战斗中的敌人状态',
        path: '$gameTroop',
        hasIndex: false,
        fields
      };
    }

    // $gameMap - 地图
    if (window.$gameMap && typeof window.$gameMap === 'object') {
      const fields = getObjectFields(window.$gameMap, '$gameMap', 2);
      gameStateItems.$gameMap = {
        id: '$gameMap',
        label: '当前地图',
        description: '当前地图的状态和信息',
        path: '$gameMap',
        hasIndex: false,
        fields
      };
    }

    // $gamePlayer - 玩家角色
    if (window.$gamePlayer && typeof window.$gamePlayer === 'object') {
      const fields = getObjectFields(window.$gamePlayer, '$gamePlayer', 2);
      gameStateItems.$gamePlayer = {
        id: '$gamePlayer',
        label: '玩家角色',
        description: '主角在地图上的状态',
        path: '$gamePlayer',
        hasIndex: false,
        fields
      };
    }

    categories.gameState = {
      id: 'gameState',
      label: '游戏状态',
      icon: '🎯',
      description: '运行时游戏状态 ($game*)',
      items: gameStateItems
    };
  } catch (error) {
    console.warn('Error processing game state:', error);
  }

  try {
    // 💾 管理器类
    const managerItems: Record<string, DataItem> = {};

    // DataManager
    if (window.DataManager && typeof window.DataManager === 'object') {
      const fields = getObjectFields(window.DataManager, 'DataManager', 2);
      managerItems.DataManager = {
        id: 'DataManager',
        label: '数据管理器',
        description: '游戏数据管理',
        path: 'DataManager',
        hasIndex: false,
        fields
      };
    }

    // SceneManager
    if (window.SceneManager && typeof window.SceneManager === 'object') {
      const fields = getObjectFields(window.SceneManager, 'SceneManager', 2);
      managerItems.SceneManager = {
        id: 'SceneManager',
        label: '场景管理器',
        description: '场景切换和管理',
        path: 'SceneManager',
        hasIndex: false,
        fields
      };
    }

    // AudioManager
    if (window.AudioManager && typeof window.AudioManager === 'object') {
      const fields = getObjectFields(window.AudioManager, 'AudioManager', 2);
      managerItems.AudioManager = {
        id: 'AudioManager',
        label: '音频管理器',
        description: '音频播放和管理',
        path: 'AudioManager',
        hasIndex: false,
        fields
      };
    }

    // BattleManager
    if (window.BattleManager && typeof window.BattleManager === 'object') {
      const fields = getObjectFields(window.BattleManager, 'BattleManager', 2);
      managerItems.BattleManager = {
        id: 'BattleManager',
        label: '战斗管理器',
        description: '战斗系统管理',
        path: 'BattleManager',
        hasIndex: false,
        fields
      };
    }

    // ImageManager
    if (window.ImageManager && typeof window.ImageManager === 'object') {
      const fields = getObjectFields(window.ImageManager, 'ImageManager', 2);
      managerItems.ImageManager = {
        id: 'ImageManager',
        label: '图像管理器',
        description: '图像资源管理',
        path: 'ImageManager',
        hasIndex: false,
        fields
      };
    }

    // PluginManager
    if (window.PluginManager && typeof window.PluginManager === 'object') {
      const fields = getObjectFields(window.PluginManager, 'PluginManager', 2);
      managerItems.PluginManager = {
        id: 'PluginManager',
        label: '插件管理器',
        description: '插件系统管理',
        path: 'PluginManager',
        hasIndex: false,
        fields
      };
    }

    // StorageManager
    if (window.StorageManager && typeof window.StorageManager === 'object') {
      const fields = getObjectFields(window.StorageManager, 'StorageManager', 2);
      managerItems.StorageManager = {
        id: 'StorageManager',
        label: '存储管理器',
        description: '存档和数据存储',
        path: 'StorageManager',
        hasIndex: false,
        fields
      };
    }

    categories.managers = {
      id: 'managers',
      label: '管理器',
      icon: '💾',
      description: 'RPG Maker MZ 管理器类',
      items: managerItems
    };
  } catch (error) {
    console.warn('Error processing managers:', error);
  }

  return categories;
}

/**
 * 获取动态数据分类（带缓存）
 */
let cachedCategories: Record<string, Category> | null = null;
let lastCacheTime = 0;
const CACHE_DURATION = 30000; // 30秒缓存

export function getDynamicDataCategoriesWithCache(): Record<string, Category> {
  const now = Date.now();

  console.log('🔍 getDynamicDataCategoriesWithCache called');
  console.log('🔍 Cache status - has cache:', !!cachedCategories, 'cache age:', now - lastCacheTime);

  if (cachedCategories && (now - lastCacheTime) < CACHE_DURATION) {
    console.log('🔍 Returning cached data');
    return cachedCategories;
  }

  try {
    console.log('🔍 Getting fresh dynamic data...');
    cachedCategories = getDynamicDataCategories();
    lastCacheTime = now;
    console.log('🔄 Dynamic data categories refreshed, result:', cachedCategories);
  } catch (error) {
    console.warn('Error refreshing dynamic data categories:', error);
    // 如果有缓存，返回缓存；否则返回空对象
    if (cachedCategories) {
      console.log('🔍 Returning cached data due to error');
      return cachedCategories;
    }
    console.log('🔍 Returning empty object due to error');
    return {};
  }

  return cachedCategories;
}

/**
 * 清除缓存，强制重新获取数据
 */
export function clearDynamicDataCache(): void {
  cachedCategories = null;
  lastCacheTime = 0;
  console.log('🗑️ Dynamic data cache cleared');
}
