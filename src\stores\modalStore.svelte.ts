/**
 * 全局模态框状态管理
 * 使用 Svelte 5 的 $state 实现高性能状态管理
 */

interface DataBindingModalProps {
  mode?: 'method' | 'data' | 'event';
  initialPath?: string;
  parentDataContext?: any;
}

interface ParentDataModalProps {
  parentPath?: string; // 🔑 父级的绑定路径
}

interface DataFlowModalProps {
  sceneName?: string; // 场景名称
}

interface ModalState {
  // 数据绑定模态框
  dataBinding: {
    isOpen: boolean;
    props: DataBindingModalProps;
    onConfirm?: (result: string) => void;
    onCancel?: () => void;
  };

  // 🔑 父级数据模态框
  parentData: {
    isOpen: boolean;
    props: ParentDataModalProps;
    onConfirm?: (result: string) => void;
    onCancel?: () => void;
  };

  // 📊 数据流分析模态框
  dataFlow: {
    isOpen: boolean;
    props: DataFlowModalProps;
    onCancel?: () => void;
  };

  // 可以扩展其他模态框类型
  // confirmation: { ... };
  // fileSelector: { ... };
}

// 全局模态框状态
const modalState = $state<ModalState>({
  dataBinding: {
    isOpen: false,
    props: {},
    onConfirm: undefined,
    onCancel: undefined
  },

  parentData: {
    isOpen: false,
    props: {},
    onConfirm: undefined,
    onCancel: undefined
  },

  dataFlow: {
    isOpen: false,
    props: {},
    onCancel: undefined
  }
});

/**
 * 模态框服务 - 提供统一的模态框操作接口
 */
export const modalService = {
  /**
   * 打开数据绑定模态框
   */
  openDataBinding(
    props: DataBindingModalProps = {},
    onConfirm?: (result: string) => void,
    onCancel?: () => void
  ) {
    console.log('🔧 ModalService: 打开数据绑定模态框', props);

    modalState.dataBinding = {
      isOpen: true,
      props: {
        mode: 'data',
        initialPath: '',
        ...props
      },
      onConfirm,
      onCancel
    };
  },

  /**
   * 关闭数据绑定模态框
   */
  closeDataBinding() {
    console.log('🔧 ModalService: 关闭数据绑定模态框');

    // 调用取消回调（如果有）
    if (modalState.dataBinding.onCancel) {
      modalState.dataBinding.onCancel();
    }

    // 重置状态
    modalState.dataBinding = {
      isOpen: false,
      props: {},
      onConfirm: undefined,
      onCancel: undefined
    };
  },

  /**
   * 确认数据绑定选择
   */
  confirmDataBinding(result: string) {
    console.log('🔧 ModalService: 确认数据绑定选择', result);

    // 调用确认回调
    if (modalState.dataBinding.onConfirm) {
      modalState.dataBinding.onConfirm(result);
    }

    // 关闭模态框
    this.closeDataBinding();
  },

  /**
   * 🔑 打开父级数据模态框
   */
  openParentData(
    props: ParentDataModalProps = {},
    onConfirm?: (result: string) => void,
    onCancel?: () => void
  ) {
    console.log('🔧 ModalService: 打开父级数据模态框', props);

    modalState.parentData = {
      isOpen: true,
      props: {
        parentPath: '',
        ...props
      },
      onConfirm,
      onCancel
    };
  },

  /**
   * 关闭父级数据模态框
   */
  closeParentData() {
    console.log('🔧 ModalService: 关闭父级数据模态框');

    // 调用取消回调（如果有）
    if (modalState.parentData.onCancel) {
      modalState.parentData.onCancel();
    }

    // 重置状态
    modalState.parentData = {
      isOpen: false,
      props: {},
      onConfirm: undefined,
      onCancel: undefined
    };
  },

  /**
   * 确认父级数据选择
   */
  confirmParentData(result: string) {
    console.log('🔧 ModalService: 确认父级数据选择', result);

    // 调用确认回调
    if (modalState.parentData.onConfirm) {
      modalState.parentData.onConfirm(result);
    }

    // 关闭模态框
    this.closeParentData();
  },

  /**
   * 📊 打开数据流分析模态框
   */
  openDataFlow(
    props: DataFlowModalProps = {},
    onCancel?: () => void
  ) {
    console.log('🔧 ModalService: 打开数据流分析模态框', props);

    modalState.dataFlow = {
      isOpen: true,
      props: {
        sceneName: '',
        ...props
      },
      onCancel
    };
  },

  /**
   * 关闭数据流分析模态框
   */
  closeDataFlow() {
    console.log('🔧 ModalService: 关闭数据流分析模态框');

    // 调用取消回调（如果有）
    if (modalState.dataFlow.onCancel) {
      modalState.dataFlow.onCancel();
    }

    // 重置状态
    modalState.dataFlow = {
      isOpen: false,
      props: {},
      onCancel: undefined
    };
  },

  /**
   * 获取数据绑定模态框状态（只读）
   */
  getDataBindingState() {
    return {
      isOpen: modalState.dataBinding.isOpen,
      props: { ...modalState.dataBinding.props }
    };
  },

  /**
   * 检查是否有任何模态框打开
   */
  hasOpenModal() {
    return modalState.dataBinding.isOpen || modalState.parentData.isOpen || modalState.dataFlow.isOpen;
    // 未来可以扩展：|| modalState.confirmation.isOpen || ...
  }
};

/**
 * 响应式状态访问器 - 用于组件中的响应式更新
 */
export const modalReactive = {
  /**
   * 获取数据绑定模态框的响应式状态
   */
  get dataBinding() {
    return modalState.dataBinding;
  },

  /**
   * 🔑 获取父级数据模态框的响应式状态
   */
  get parentData() {
    return modalState.parentData;
  },

  /**
   * 📊 获取数据流分析模态框的响应式状态
   */
  get dataFlow() {
    return modalState.dataFlow;
  }
};

/**
 * 模态框管理器 - 用于全局模态框容器组件
 */
export class ModalManager {
  /**
   * 处理数据绑定模态框的确认
   */
  static handleDataBindingConfirm(result: string) {
    modalService.confirmDataBinding(result);
  }

  /**
   * 处理数据绑定模态框的取消
   */
  static handleDataBindingCancel() {
    modalService.closeDataBinding();
  }

  /**
   * 获取数据绑定模态框的属性
   */
  static getDataBindingProps() {
    return modalState.dataBinding.props;
  }

  /**
   * 检查数据绑定模态框是否打开
   */
  static isDataBindingOpen() {
    return modalState.dataBinding.isOpen;
  }

  /**
   * 🔑 处理父级数据模态框的确认
   */
  static confirmParentData(result: string) {
    modalService.confirmParentData(result);
  }

  /**
   * 处理父级数据模态框的取消
   */
  static cancelParentData() {
    modalService.closeParentData();
  }

  /**
   * 获取父级数据模态框的属性
   */
  static getParentDataProps() {
    return modalState.parentData.props;
  }

  /**
   * 检查父级数据模态框是否打开
   */
  static isParentDataOpen() {
    return modalState.parentData.isOpen;
  }

  /**
   * 📊 处理数据流分析模态框的取消
   */
  static cancelDataFlow() {
    modalService.closeDataFlow();
  }

  /**
   * 获取数据流分析模态框的属性
   */
  static getDataFlowProps() {
    return modalState.dataFlow.props;
  }

  /**
   * 检查数据流分析模态框是否打开
   */
  static isDataFlowOpen() {
    return modalState.dataFlow.isOpen;
  }
}

// 开发模式下的调试工具
if (typeof window !== 'undefined' && window.location?.hostname === 'localhost') {
  // @ts-ignore
  window.modalService = modalService;
  // @ts-ignore
  window.modalState = modalState;

  console.log('🔧 ModalService 已加载，调试工具已注册到 window.modalService');
}
