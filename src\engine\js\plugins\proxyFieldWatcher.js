(() => {
class ProxyFieldWatcher {
  static proxies = new WeakMap();
  static watchers = new WeakMap();
  
  /**
   * 创建代理对象并监听字段变化
   * @param {Object} target - 目标对象
   * @param {string|Array} fields - 要监听的字段名或字段数组
   * @param {Function} callback - 回调函数
   * @returns {Object} 代理对象和取消监听函数
   */
  static watch(target, fields, callback) {
    console.log('🔧 ProxyFieldWatcher.watch called:', { target: target.constructor?.name, fields });

    // 获取或创建监听器映射
    let watchers = this.watchers.get(target);
    if (!watchers) {
      watchers = new Map();
      this.watchers.set(target, watchers);
    }

    // 处理字段列表
    const fieldList = Array.isArray(fields) ? fields : [fields];

    // 为每个字段添加监听器
    fieldList.forEach(field => {
      if (!watchers.has(field)) {
        watchers.set(field, new Set());
      }
      watchers.get(field).add(callback);
      console.log(`🔧 ProxyFieldWatcher: 添加监听器 ${field}`);
    });

    // 🔑 关键修复：直接在原始对象上设置拦截器
    this.setupDirectInterception(target, fieldList);

    // 返回取消监听函数
    return {
      unwatch: () => {
        console.log('🔧 ProxyFieldWatcher: 取消监听');
        fieldList.forEach(field => {
          const fieldWatchers = watchers.get(field);
          if (fieldWatchers) {
            fieldWatchers.delete(callback);
            if (fieldWatchers.size === 0) {
              watchers.delete(field);
            }
          }
        });

        if (watchers.size === 0) {
          this.watchers.delete(target);
        }
      }
    };
  }

  /**
   * 直接在原始对象上设置属性拦截器
   */
  static setupDirectInterception(target, fieldList) {
    fieldList.forEach(field => {
      // 检查是否已经设置过拦截器
      const descriptor = Object.getOwnPropertyDescriptor(target, field);
      if (descriptor && descriptor.get && descriptor.get._isProxyFieldWatcher) {
        console.log(`🔧 ProxyFieldWatcher: ${field} 已经有拦截器，监听器已添加到现有拦截器`);
        return; // 拦截器已存在，但监听器已经在上面的代码中添加到 Set 中了
      }

      // 保存原始值
      let originalValue = target[field];

      // 🔑 如果是数组，设置数组方法拦截
      if (Array.isArray(originalValue)) {
        this.setupArrayInterception(originalValue, target, field);
      }

      // 重新定义属性
      Object.defineProperty(target, field, {
        get() {
          return originalValue;
        },
        set(newValue) {
          const oldValue = originalValue;
          originalValue = newValue;

          // 🔑 如果新值是数组，也要设置数组方法拦截
          if (Array.isArray(newValue)) {
            this.setupArrayInterception(newValue, target, field);
          }

          if (oldValue !== newValue) {
            console.log(`🔄 ProxyFieldWatcher: ${field} 变化 ${oldValue} -> ${newValue}`);

            // 通知所有监听器
            const watchers = ProxyFieldWatcher.watchers.get(target);
            if (watchers) {
              const fieldWatchers = watchers.get(field);
              if (fieldWatchers) {
                console.log(`🔔 ProxyFieldWatcher: 通知 ${fieldWatchers.size} 个监听器 ${field} 变化`);
                fieldWatchers.forEach((callback, index) => {
                  try {
                    console.log(`🔔 ProxyFieldWatcher: 调用监听器 ${index + 1}/${fieldWatchers.size}`);
                    callback(field, newValue, oldValue);
                  } catch (error) {
                    console.error('ProxyFieldWatcher callback error:', error);
                  }
                });
              } else {
                console.warn(`🚨 ProxyFieldWatcher: 没有找到 ${field} 的监听器`);
              }
            } else {
              console.warn(`🚨 ProxyFieldWatcher: 没有找到目标对象的监听器映射`);
            }
          }
        },
        enumerable: true,
        configurable: true
      });

      // 标记这个 getter 是由 ProxyFieldWatcher 创建的
      const newDescriptor = Object.getOwnPropertyDescriptor(target, field);
      if (newDescriptor && newDescriptor.get) {
        newDescriptor.get._isProxyFieldWatcher = true;
      }

      console.log(`🔧 ProxyFieldWatcher: 为 ${field} 设置了直接拦截器`);
    });
  }

  /**
   * 为数组设置方法拦截
   */
  static setupArrayInterception(array, parentObject, fieldName) {
    // 避免重复设置
    if (array._proxyFieldWatcherSetup) {
      return;
    }

    console.log(`🔧 ProxyFieldWatcher: 为数组 ${fieldName} 设置方法拦截`);

    // 需要拦截的数组方法
    const methodsToIntercept = [
      'push', 'pop', 'shift', 'unshift', 'splice',
      'sort', 'reverse', 'fill', 'copyWithin'
    ];

    methodsToIntercept.forEach(methodName => {
      const originalMethod = array[methodName];
      if (typeof originalMethod === 'function') {
        array[methodName] = function(...args) {
          console.log(`🔄 ProxyFieldWatcher: 数组方法 ${fieldName}.${methodName} 被调用`);

          // 保存旧的数组状态（浅拷贝）
          const oldArray = [...this];

          // 调用原始方法
          const result = originalMethod.apply(this, args);

          // 通知监听器数组发生了变化
          const watchers = ProxyFieldWatcher.watchers.get(parentObject);
          if (watchers) {
            const fieldWatchers = watchers.get(fieldName);
            if (fieldWatchers) {
              fieldWatchers.forEach(callback => {
                try {
                  callback(fieldName, this, oldArray);
                } catch (error) {
                  console.error('ProxyFieldWatcher array callback error:', error);
                }
              });
            }
          }

          return result;
        };
      }
    });

    // 拦截数组元素的直接赋值 array[index] = value
    const originalLength = array.length;
    for (let i = 0; i < originalLength + 10; i++) { // 预设一些索引
      this.setupArrayIndexInterception(array, i, parentObject, fieldName);
    }

    // 标记已设置
    array._proxyFieldWatcherSetup = true;
  }

  /**
   * 为数组索引设置拦截
   */
  static setupArrayIndexInterception(array, index, parentObject, fieldName) {
    const descriptor = Object.getOwnPropertyDescriptor(array, index);
    if (descriptor && descriptor.get && descriptor.get._isProxyFieldWatcher) {
      return; // 已经设置过
    }

    let currentValue = array[index];

    Object.defineProperty(array, index, {
      get() {
        return currentValue;
      },
      set(newValue) {
        if (currentValue !== newValue) {
          console.log(`🔄 ProxyFieldWatcher: 数组元素 ${fieldName}[${index}] 变化`);

          const oldValue = currentValue;
          currentValue = newValue;

          // 通知监听器
          const watchers = ProxyFieldWatcher.watchers.get(parentObject);
          if (watchers) {
            const fieldWatchers = watchers.get(fieldName);
            if (fieldWatchers) {
              fieldWatchers.forEach(callback => {
                try {
                  callback(fieldName, array, array); // 传递整个数组
                } catch (error) {
                  console.error('ProxyFieldWatcher array index callback error:', error);
                }
              });
            }
          }
        }
      },
      enumerable: true,
      configurable: true
    });

    if (Object.getOwnPropertyDescriptor(array, index).get) {
      Object.getOwnPropertyDescriptor(array, index).get._isProxyFieldWatcher = true;
    }
  }

  static createProxy(target) {
    const watchers = this.watchers;
    
    return new Proxy(target, {
      set(obj, property, value) {
        const oldValue = obj[property];
        const result = Reflect.set(obj, property, value);
        
        if (oldValue !== value) {
          const objectWatchers = watchers.get(target);
          if (objectWatchers) {
            const fieldWatchers = objectWatchers.get(property);
            if (fieldWatchers) {
              fieldWatchers.forEach(callback => {
                try {
                  callback(property, value, oldValue);
                } catch (error) {
                  console.error('ProxyFieldWatcher callback error:', error);
                }
              });
            }
          }
        }
        
        return result;
      },
      
      get(obj, property) {
        return Reflect.get(obj, property);
      }
    });
  }

  /**
   * 🔧 调试方法：显示当前所有监听器状态
   */
  static debugWatchers() {
    console.log('🔍 ProxyFieldWatcher 调试信息:');

    let totalObjects = 0;
    let totalFields = 0;
    let totalWatchers = 0;

    this.watchers.forEach((objectWatchers, target) => {
      totalObjects++;
      console.log(`📦 对象: ${target.constructor?.name || 'Unknown'}`);

      objectWatchers.forEach((fieldWatchers, fieldName) => {
        totalFields++;
        totalWatchers += fieldWatchers.size;
        console.log(`  📝 字段 ${fieldName}: ${fieldWatchers.size} 个监听器`);

        // 显示每个监听器的信息
        let index = 0;
        fieldWatchers.forEach((callback) => {
          index++;
          console.log(`    🔔 监听器 ${index}: ${callback.name || 'anonymous'}`);
        });
      });
    });

    console.log(`📊 总计: ${totalObjects} 个对象, ${totalFields} 个字段, ${totalWatchers} 个监听器`);
  }

  /**
   * 🔧 调试方法：检查特定字段的监听器
   */
  static debugField(target, fieldName) {
    console.log(`🔍 检查字段 ${fieldName} 的监听器:`);

    const watchers = this.watchers.get(target);
    if (!watchers) {
      console.log('❌ 没有找到目标对象的监听器映射');
      return;
    }

    const fieldWatchers = watchers.get(fieldName);
    if (!fieldWatchers) {
      console.log(`❌ 没有找到字段 ${fieldName} 的监听器`);
      return;
    }

    console.log(`✅ 找到 ${fieldWatchers.size} 个监听器:`);
    let index = 0;
    fieldWatchers.forEach((callback) => {
      index++;
      console.log(`  🔔 监听器 ${index}: ${callback.name || 'anonymous'}`);
    });
  }
}
    // 将类添加到全局
    window.ProxyFieldWatcher = ProxyFieldWatcher;
})();