// 每个对象的专用示例代码

import type { RPGExample } from './types';

// 为每个对象定义示例
export const objectExamples: Record<string, RPGExample[]> = {
  // 数据对象示例
  '$dataSystem': [
    {
      name: '获取游戏基本信息',
      type: 'method',
      description: '获取游戏标题和版本信息',
      code: `// 获取游戏基本信息
console.log('游戏标题:', $dataSystem.gameTitle);
console.log('版本ID:', $dataSystem.versionId);
console.log('起始地图:', $dataSystem.startMapId);`
    },
    {
      name: '播放系统音乐',
      type: 'method',
      description: '播放标题和战斗BGM',
      code: `// 播放系统音乐
AudioManager.playBgm($dataSystem.titleBgm);
// 或播放战斗BGM
AudioManager.playBgm($dataSystem.battleBgm);`
    }
  ],

  '$dataMap': [
    {
      name: '获取地图信息',
      type: 'method',
      description: '获取当前地图的基本信息',
      code: `// 获取地图信息
console.log('地图ID:', $dataMap.mapId);
console.log('地图名称:', $dataMap.displayName);
console.log('地图尺寸:', $dataMap.width + 'x' + $dataMap.height);
console.log('事件数量:', $dataMap.events.length);`
    }
  ],

  '$dataItems': [
    {
      name: '查找和使用物品',
      type: 'method',
      description: '查找特定物品并检查其属性',
      code: `// 查找和使用物品
const healingItem = $dataItems.find(item =>
  item && item.name.includes('药')
);

if (healingItem) {
  console.log('找到治疗物品:', healingItem.name);
  console.log('物品描述:', healingItem.description);
  console.log('物品价格:', healingItem.price);
}`
    }
  ],

  '$dataWeapons': [
    {
      name: '武器属性查看',
      type: 'method',
      description: '查看武器的攻击力和特殊效果',
      code: `// 武器属性查看
const weapon = $dataWeapons[1];
if (weapon) {
  console.log('武器名称:', weapon.name);
  console.log('攻击力:', weapon.params[2]); // 攻击力参数
  console.log('武器类型:', weapon.wtypeId);
  console.log('价格:', weapon.price);
}`
    }
  ],

  '$dataSkills': [
    {
      name: '技能信息查看',
      type: 'method',
      description: '查看技能的消耗和效果',
      code: `// 技能信息查看
const skill = $dataSkills[1];
if (skill) {
  console.log('技能名称:', skill.name);
  console.log('MP消耗:', skill.mpCost);
  console.log('技能类型:', skill.stypeId);
  console.log('技能描述:', skill.description);
}`
    }
  ],

  // 游戏对象示例
  '$gameParty': [
    {
      name: '队伍管理操作',
      type: 'method',
      description: '管理队伍成员和物品',
      code: `// 队伍管理操作
const party = $gameParty;

// 查看队伍状态
console.log('队伍金钱:', party.gold());
console.log('队伍成员数:', party.members().length);

// 添加物品和金钱
party.gainGold(1000);
party.gainItem($dataItems[1], 5);

// 查看队伍成员信息
party.members().forEach((actor, index) => {
  console.log(\`成员\${index + 1}: \${actor.name()}\`);
});`
    }
  ],

  '$gameSystem': [
    {
      name: '系统状态检查',
      type: 'method',
      description: '检查游戏系统的各种状态',
      code: `// 系统状态检查
console.log('自动保存启用:', $gameSystem.isAutosaveEnabled());
console.log('保存功能启用:', $gameSystem.isSaveEnabled());
console.log('菜单功能启用:', $gameSystem.isMenuEnabled());
console.log('当前存档ID:', $gameSystem.savefileId());`
    }
  ],

  '$gamePlayer': [
    {
      name: '玩家位置和移动',
      type: 'method',
      description: '获取玩家位置并进行传送',
      code: `// 玩家位置和移动
const player = $gamePlayer;

// 获取当前位置
console.log('玩家位置:', \`(\${player.x}, \${player.y})\`);
console.log('玩家朝向:', player.direction());

// 传送玩家到新位置
player.transferPlayer(2, 10, 10, 2);

// 检查移动状态
console.log('是否可以移动:', player.canMove());
console.log('是否正在移动:', player.isMoving());`
    }
  ],

  '$gameMap': [
    {
      name: '地图操作',
      type: 'method',
      description: '地图刷新和事件操作',
      code: `// 地图操作
const map = $gameMap;

// 获取地图信息
console.log('当前地图ID:', map.mapId());
console.log('地图显示名:', map.displayName());

// 刷新地图
map.refresh();

// 获取指定事件
const event = map.event(1);
if (event) {
  console.log('事件1名称:', event.event().name);
}`
    }
  ],

  '$gameTimer': [
    {
      name: '计时器控制',
      type: 'method',
      description: '启动和控制游戏计时器',
      code: `// 计时器控制
const timer = $gameTimer;

// 启动5分钟计时器
timer.start(300); // 300秒

// 检查计时器状态
console.log('剩余时间:', timer.seconds() + '秒');

// 停止计时器
// timer.stop();`
    }
  ],

  '$gameTroop': [
    {
      name: '敌群管理',
      type: 'method',
      description: '战斗中的敌群操作',
      code: `// 敌群管理
const troop = $gameTroop;

// 设置敌群
troop.setup(1);

// 获取敌群信息
console.log('敌人总数:', troop.members().length);
console.log('存活敌人:', troop.aliveMembers().length);
console.log('死亡敌人:', troop.deadMembers().length);

// 获取奖励
console.log('总经验值:', troop.expTotal());
console.log('总金钱:', troop.goldTotal());`
    }
  ]
};

// 管理器对象示例
objectExamples['SceneManager'] = [
  {
    name: '场景切换操作',
    type: 'method',
    description: '各种场景切换方式',
    code: `// 场景切换操作
// 直接跳转
SceneManager.goto(Scene_Menu);

// 推入场景（可返回）
SceneManager.push(Scene_Shop);

// 弹出当前场景
SceneManager.pop();

// 调用场景
SceneManager.call(Scene_Save);

// 检查场景状态
console.log('是否在切换场景:', SceneManager.isSceneChanging());`
  },
  {
    name: '获取所有保存记录',
    type: 'method',
    description: '遍历所有存档槽位，获取保存记录信息',
    code: `// 获取所有保存记录
function getAllSaveRecords() {
  console.log('=== 所有保存记录 ===');

  const maxSaves = DataManager.maxSavefiles();
  const saveRecords = [];

  // 遍历所有存档槽位（包括自动保存槽位0）
  for (let i = 0; i <= maxSaves; i++) {
    if (DataManager.savefileExists(i)) {
      const info = DataManager.savefileInfo(i);
      const record = {
        slotId: i,
        isAutosave: i === 0,
        title: info.title,
        playtime: info.playtime,
        timestamp: new Date(info.timestamp),
        characters: info.characters.map(char => char.name).join(', '),
        faces: info.faces,
        globalId: info.globalId
      };

      saveRecords.push(record);

      // 显示记录信息
      const slotType = record.isAutosave ? '[自动保存]' : \`[存档\${i}]\`;
      console.log(\`\${slotType} \${record.title}\`);
      console.log(\`  游戏时间: \${record.playtime}\`);
      console.log(\`  保存时间: \${record.timestamp.toLocaleString()}\`);
      console.log(\`  角色: \${record.characters}\`);
      console.log('');
    }
  }

  // 返回所有记录
  console.log(\`总共找到 \${saveRecords.length} 个保存记录\`);
  return saveRecords;
}

// 使用示例
const allSaves = getAllSaveRecords();

// 获取最新的保存记录
const latestSaveId = DataManager.latestSavefileId();
if (latestSaveId > 0) {
  console.log(\`最新保存记录: 存档\${latestSaveId}\`);
}`
  }
];

objectExamples['AudioManager'] = [
  {
    name: '音频播放控制',
    type: 'method',
    description: '播放和控制各种音频',
    code: `// 音频播放控制
// 播放BGM
AudioManager.playBgm($dataSystem.titleBgm);

// 播放音效
AudioManager.playSe({
  name: "Cursor1",
  volume: 90,
  pitch: 100,
  pan: 0
});

// 淡出BGM
AudioManager.fadeOutBgm(3);

// 停止所有音频
AudioManager.stopAll();`
  }
];

objectExamples['DataManager'] = [
  {
    name: '存档操作',
    type: 'method',
    description: '保存和加载游戏数据',
    code: `// 存档操作
// 保存游戏
DataManager.saveGame(1).then(() => {
  console.log('保存成功');
}).catch(() => {
  console.log('保存失败');
});

// 检查存档
if (DataManager.savefileExists(1)) {
  const info = DataManager.savefileInfo(1);
  console.log('存档信息:', info.title);
}

// 加载游戏
// DataManager.loadGame(1);`
  }
];

objectExamples['SoundManager'] = [
  {
    name: '系统音效播放',
    type: 'method',
    description: '播放各种系统音效',
    code: `// 系统音效播放
// 界面音效
SoundManager.playCursor();  // 光标移动
SoundManager.playOk();      // 确定
SoundManager.playCancel();  // 取消
SoundManager.playBuzzer();  // 错误

// 游戏音效
SoundManager.playUseItem(); // 使用物品
SoundManager.playEquip();   // 装备
SoundManager.playShop();    // 商店交易`
  }
];

objectExamples['BattleManager'] = [
  {
    name: '战斗系统控制',
    type: 'method',
    description: '控制战斗流程',
    code: `// 战斗系统控制
// 开始战斗
BattleManager.playBattleBgm();
BattleManager.startBattle();

// 检查战斗状态
console.log('是否在输入:', BattleManager.isInputting());
console.log('当前行动角色:', BattleManager.actor()?.name());
console.log('战斗是否结束:', BattleManager.isBattleEnd());

// 处理逃跑
BattleManager.processEscape();`
  }
];

objectExamples['Game_Action'] = [
  {
    name: '行动对象使用',
    type: 'method',
    description: '创建和使用行动对象',
    code: `// 行动对象使用
const actor = $gameActors.actor(1);
const action = new Game_Action(actor);

// 设置攻击
action.setAttack();

// 设置技能
action.setSkill(1);

// 设置目标
action.setTarget(0);

// 检查行动属性
console.log('需要选择目标:', action.needsSelection());
console.log('对敌方使用:', action.isForOpponent());`
  }
];

objectExamples['Game_Actor'] = [
  {
    name: '角色操作',
    type: 'method',
    description: '角色属性和装备管理',
    code: `// 角色操作
const actor = $gameActors.actor(1);

// 查看角色信息
console.log('角色名称:', actor.name());
console.log('等级:', actor.level);
console.log('HP:', \`\${actor.hp}/\${actor.mhp}\`);

// 装备管理
actor.optimizeEquipments(); // 最优化装备
console.log('当前装备:', actor.equips());

// 技能使用
const skill = actor.skills()[0];
if (actor.canUse(skill)) {
  actor.useItem(skill);
}`
  }
];

objectExamples['ConfigManager'] = [
  {
    name: '游戏设置管理',
    type: 'method',
    description: '查看和修改游戏的各种设置选项',
    code: `// 游戏设置管理
console.log('=== 当前游戏设置 ===');

// 查看游戏行为设置
console.log('始终跑步:', ConfigManager.alwaysDash);
console.log('记住指令:', ConfigManager.commandRemember);
console.log('触碰界面:', ConfigManager.touchUI);

// 查看音频设置
console.log('背景音乐音量:', ConfigManager.bgmVolume + '%');
console.log('背景声音音量:', ConfigManager.bgsVolume + '%');
console.log('音效音量:', ConfigManager.seVolume + '%');
console.log('声效音量(ME):', ConfigManager.meVolume + '%');

// 修改设置
ConfigManager.alwaysDash = true;        // 启用始终跑步
ConfigManager.commandRemember = true;   // 启用记住指令
ConfigManager.touchUI = false;          // 禁用触碰界面

// 修改音量设置
ConfigManager.bgmVolume = 80;  // BGM音量80%
ConfigManager.bgsVolume = 70;  // BGS音量70%
ConfigManager.seVolume = 90;   // SE音量90%
ConfigManager.meVolume = 85;   // ME音量85%

// 保存设置到本地存储
ConfigManager.save();
console.log('设置已保存');`
  },
  {
    name: '音量控制系统',
    type: 'method',
    description: '创建音量控制功能',
    code: `// 音量控制系统
function adjustVolume(type, value) {
  // 确保音量在0-100范围内
  value = Math.max(0, Math.min(100, value));

  switch (type) {
    case 'bgm':
      ConfigManager.bgmVolume = value;
      console.log(\`BGM音量设置为: \${value}%\`);
      break;
    case 'bgs':
      ConfigManager.bgsVolume = value;
      console.log(\`BGS音量设置为: \${value}%\`);
      break;
    case 'se':
      ConfigManager.seVolume = value;
      console.log(\`SE音量设置为: \${value}%\`);
      break;
    case 'me':
      ConfigManager.meVolume = value;
      console.log(\`ME音量设置为: \${value}%\`);
      break;
    default:
      console.log('未知的音量类型');
      return;
  }

  // 保存设置
  ConfigManager.save();
}

function getAllVolumeSettings() {
  return {
    bgm: ConfigManager.bgmVolume,
    bgs: ConfigManager.bgsVolume,
    se: ConfigManager.seVolume,
    me: ConfigManager.meVolume
  };
}

// 使用示例
console.log('当前音量设置:', getAllVolumeSettings());

// 调整各种音量
adjustVolume('bgm', 75);  // 设置BGM音量为75%
adjustVolume('se', 85);   // 设置SE音量为85%

// 静音所有音频
adjustVolume('bgm', 0);
adjustVolume('bgs', 0);
adjustVolume('se', 0);
adjustVolume('me', 0);`
  }
];
