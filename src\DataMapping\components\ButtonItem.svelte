<script lang="ts">
  import type { ButtonDataFlow } from '../types';

  interface Props {
    buttonData: ButtonDataFlow;
    index: number;
  }

  let { buttonData, index }: Props = $props();

  // 获取方法的说明
  function getMethodDescription(method: string): string {
    if (method.includes('DataManager.setupNewGame')) {
      return '初始化新游戏数据';
    } else if (method.includes('DataManager.saveGame')) {
      return '保存游戏数据';
    } else if (method.includes('DataManager.loadGame')) {
      return '加载游戏数据';
    } else if (method.includes('SceneManager.goto')) {
      return '切换到指定场景';
    } else if (method.includes('SceneManager.push')) {
      return '推入新场景到栈顶';
    } else if (method.includes('this.fadeOutAll')) {
      return '淡出所有音频和画面';
    } else if (method.includes('ConfigManager.save')) {
      return '保存配置数据到本地';
    } else if (method.includes('this.popScene')) {
      return '返回上一个场景';
    } else if (method.includes('this.fadeOutAllAudio')) {
      return '只淡出音频';
    } else if (method.includes('this.startFadeOut')) {
      return '开始画面淡出';
    } else if (method.includes('this.checkGameover')) {
      return '检查游戏结束状态';
    } else if (method.includes('$gameParty')) {
      return '操作队伍数据';
    } else if (method.includes('$gamePlayer')) {
      return '操作玩家数据';
    } else if (method.includes('AudioManager') || method.includes('SoundManager')) {
      return '播放音效或音乐';
    } else if (method.includes('ImageManager')) {
      return '加载图像资源';
    } else {
      return '执行游戏逻辑';
    }
  }
</script>

<!-- 按钮卡片 -->
<div class="button-card">
  <!-- 按钮标题 -->
  <div class="button-header">
    <div class="button-info">
      <span class="button-number">{index + 1}</span>
      <h3 class="button-name">{buttonData.buttonName}</h3>
    </div>
    <div class="method-count">
      {buttonData.triggerMethods.length} 个方法
    </div>
  </div>

  <!-- 方法列表 -->
  <div class="methods-list">
    {#each buttonData.triggerMethods as method, methodIndex}
      <div class="method-item">
        <span class="method-number">{methodIndex + 1}.</span>
        <code class="method-code">{method}</code>
        <span class="method-description">{getMethodDescription(method)}</span>
      </div>
    {/each}
  </div>
</div>

<style>
  .button-card {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-3);
    transition: all 0.2s ease;
  }

  .button-card:hover {
    border-color: var(--theme-primary);
    box-shadow: var(--shadow-sm);
  }

  .button-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-3);
    padding-bottom: var(--spacing-2);
    border-bottom: 1px solid var(--theme-border);
  }

  .button-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .button-number {
    background: var(--theme-primary);
    color: var(--theme-text-inverse);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: 600;
    flex-shrink: 0;
  }

  .button-name {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-text);
  }

  .method-count {
    background: var(--theme-accent-light);
    color: var(--theme-accent-dark);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
  }

  .methods-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .method-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
    background: var(--theme-surface-elevated);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--theme-border);
  }

  .method-number {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
    font-weight: 500;
    min-width: 20px;
  }

  .method-code {
    font-family: var(--font-mono);
    font-size: var(--font-size-sm);
    color: #2563eb;  /* 蓝色 */
    background: var(--theme-surface);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--theme-border);
    font-weight: 500;
    flex: 1;
  }

  .method-description {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
    font-style: italic;
    flex-shrink: 0;
    max-width: 200px;
  }
</style>
