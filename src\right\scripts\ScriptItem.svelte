<script lang="ts">
  import CodeEditorButton from '../../modals/codeModals/CodeEditorButton.svelte';
  
  interface Script {
    id: string;
    name: string;
    enabled: boolean;
    code: string;
    description?: string;
  }

  export let script: Script;
  export let onUpdate: (scriptId: string, newCode: string) => void;
  export let onDelete: (scriptId: string) => void;
  export let onToggle: (scriptId: string) => void;

  /**
   * 解析脚本中包含的方法名
   */
  function getScriptMethods(scriptCode: string): string[] {
    const methods: string[] = [];
    const methodRegex = /function\s+(on\w+)\s*\(/g;
    
    let match;
    while ((match = methodRegex.exec(scriptCode)) !== null) {
      methods.push(match[1]);
    }
    
    return methods;
  }

  /**
   * 删除脚本
   */
  function handleDelete() {
    if (confirm(`确定要删除脚本 "${script.name}" 吗？`)) {
      onDelete(script.id);
    }
  }

  /**
   * 切换脚本启用状态
   */
  function handleToggle() {
    onToggle(script.id);
  }

  /**
   * 更新脚本代码
   */
  function handleUpdate(newCode: string) {
    onUpdate(script.id, newCode);
  }
</script>

<div class="script-item" class:disabled={!script.enabled}>
  <!-- 启用/禁用开关 -->
  <label class="script-toggle">
    <input 
      type="checkbox" 
      checked={script.enabled} 
      onchange={handleToggle}
    />
    <span class="toggle-slider"></span>
  </label>
  
  <!-- 脚本信息 -->
  <div class="script-info">
    <div class="script-name">{script.name}</div>
    <div class="script-methods">
      包含方法: {getScriptMethods(script.code).join(', ') || '无'}
    </div>
    {#if script.description}
      <div class="script-description">{script.description}</div>
    {/if}
  </div>

  <!-- 操作按钮 -->
  <div class="script-actions">
    <CodeEditorButton
      title="编辑脚本: {script.name}"
      code={script.code}
      onSave={handleUpdate}
      buttonText="编辑"
      buttonClass="secondary small"
    />
    <button 
      class="delete-btn"
      onclick={handleDelete}
      title="删除脚本"
    >
      删除
    </button>
  </div>
</div>

<style>
  /* 脚本项 */
  .script-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--theme-surface-dark, #475569);
    border: 1px solid var(--theme-border-dark, #64748b);
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  .script-item:hover {
    background: var(--theme-surface-dark-hover, #334155);
    border-color: var(--theme-border-dark-hover, #475569);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .script-item.disabled {
    opacity: 0.6;
    background: var(--theme-surface-dark-disabled, #64748b);
  }

  .script-item.disabled:hover {
    transform: none;
    box-shadow: none;
  }

  /* 启用/禁用开关 */
  .script-toggle {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    flex-shrink: 0;
  }

  .script-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--theme-border, #cbd5e1);
    transition: 0.2s;
    border-radius: 20px;
  }

  .toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: var(--theme-surface-light, #f8fafc);
    transition: 0.2s;
    border-radius: 50%;
  }

  .script-toggle input:checked + .toggle-slider {
    background-color: var(--theme-primary, #3b82f6);
  }

  .script-toggle input:checked + .toggle-slider:before {
    transform: translateX(20px);
  }

  /* 脚本信息 */
  .script-info {
    flex: 1;
    min-width: 0;
  }

  .script-name {
    font-weight: 600;
    color: var(--theme-text-light, #f1f5f9);
    font-size: 14px;
    margin-bottom: 4px;
  }

  .script-methods {
    font-size: 11px;
    color: var(--theme-text-light-secondary, #cbd5e1);
    line-height: 1.4;
    margin-bottom: 2px;
  }

  .script-description {
    font-size: 10px;
    color: var(--theme-text-light-tertiary, #94a3b8);
    line-height: 1.3;
    font-style: italic;
  }

  /* 操作按钮 */
  .script-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
  }

  .delete-btn {
    padding: 6px 12px;
    border: 1px solid var(--theme-border-dark, #64748b);
    border-radius: 6px;
    background: var(--theme-surface-secondary, #64748b);
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    color: var(--theme-text-light, #f1f5f9);
    transition: all 0.2s ease;
  }

  .delete-btn:hover {
    background: var(--theme-danger, #dc2626);
    border-color: var(--theme-danger-dark, #b91c1c);
    color: var(--theme-text-light, #ffffff);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
  }
</style>
