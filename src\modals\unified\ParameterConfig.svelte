<script lang="ts">
  import type { DataItem, Method, ModalMode, Parameter } from './types';

  let {
    item,
    parameters,
    onUpdate,
    mode
  }: {
    item: DataItem | Method;
    parameters: Record<string, any>;
    onUpdate: (params: Record<string, any>) => void;
    mode: ModalMode;
  } = $props();

  function handleParameterChange(paramName: string, value: any) {
    onUpdate({ [paramName]: value });
  }

  function renderParameterInput(param: Parameter) {
    const currentValue = parameters[param.name] || param.defaultValue;

    switch (param.type) {
      case 'string':
        return {
          type: 'text',
          value: currentValue || '',
          placeholder: param.defaultValue || ''
        };
      case 'number':
        return {
          type: 'number',
          value: currentValue || 0,
          placeholder: param.defaultValue?.toString() || '0'
        };
      case 'boolean':
        return {
          type: 'checkbox',
          checked: currentValue || false
        };
      case 'itemId':
        return {
          type: 'text',
          value: currentValue || 'itemId',
          placeholder: 'itemId'
        };
      default:
        return {
          type: 'text',
          value: currentValue || '',
          placeholder: param.defaultValue || ''
        };
    }
  }
</script>

<div class="parameter-config">
  {#if mode === 'method' && 'params' in item && item.params && item.params.length > 0}
    <!-- 方法参数配置 -->
    {#each item.params as param}
      <div class="parameter-group">
        <label class="parameter-label">
          {param.label}
          {#if param.required}
            <span class="required">*</span>
          {/if}
        </label>
        
        {#if param.description}
          <div class="parameter-description">{param.description}</div>
        {/if}

        {#if param.type === 'boolean'}
          {@const inputProps = renderParameterInput(param)}
          <label class="checkbox-wrapper">
            <input
              type="checkbox"
              checked={inputProps.checked}
              onchange={(e) => handleParameterChange(param.name, e.target.checked)}
            />
            <span class="checkbox-label">启用</span>
          </label>
        {:else if param.options}
          <select
            value={parameters[param.name] || param.defaultValue}
            onchange={(e) => handleParameterChange(param.name, e.target.value)}
            class="parameter-select"
          >
            {#each param.options as option}
              <option value={option.value}>{option.label}</option>
            {/each}
          </select>
        {:else}
          {@const inputProps = renderParameterInput(param)}
          <input
            type={inputProps.type}
            value={inputProps.value}
            placeholder={inputProps.placeholder}
            oninput={(e) => {
              const value = param.type === 'number' ? Number(e.target.value) : e.target.value;
              handleParameterChange(param.name, value);
            }}
            class="parameter-input"
          />
        {/if}
      </div>
    {/each}
  {:else if mode === 'data'}
    <!-- 数据索引配置 -->
    {#if 'hasIndex' in item && item.hasIndex}
      <div class="parameter-group">
        <label class="parameter-label">
          索引参数
          <span class="required">*</span>
        </label>
        <div class="parameter-description">
          数组索引，通常使用 itemId
        </div>
        <input
          type="text"
          value={parameters.index || 'itemId'}
          placeholder="itemId"
          oninput={(e) => handleParameterChange('index', e.target.value)}
          class="parameter-input"
        />
      </div>
    {/if}

    <!-- 数据访问类型选择 -->
    <div class="parameter-group">
      <label class="parameter-label">数据访问类型</label>
      <div class="parameter-description">
        选择如何访问数据
      </div>
      <div class="access-type-buttons">
        <button
          type="button"
          class="access-type-btn"
          class:active={parameters.accessType !== 'field'}
          onclick={() => handleParameterChange('accessType', 'full')}
        >
          📊 获取完整数据
        </button>
        {#if 'fields' in item && item.fields && item.fields.length > 0}
          <button
            type="button"
            class="access-type-btn"
            class:active={parameters.accessType === 'field'}
            onclick={() => handleParameterChange('accessType', 'field')}
          >
            🔍 访问特定字段
          </button>
        {/if}
      </div>
    </div>

    <!-- 字段选择（仅在选择字段访问时显示） -->
    {#if parameters.accessType === 'field' && 'fields' in item && item.fields && item.fields.length > 0}
      <div class="parameter-group">
        <label class="parameter-label">字段选择</label>
        <div class="parameter-description">
          选择要访问的字段
        </div>
        <select
          value={parameters.field || ''}
          onchange={(e) => handleParameterChange('field', e.target.value)}
          class="parameter-select"
        >
          <option value="">-- 选择字段 --</option>
          {#each item.fields as field}
            <option value={field.name}>{field.label} ({field.name})</option>
          {/each}
        </select>
      </div>
    {/if}
  {:else}
    <div class="no-parameters">
      <p>此项目无需配置参数</p>
    </div>
  {/if}
</div>

<style>
  .parameter-config {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .parameter-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .parameter-label {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary, #333);
  }

  .required {
    color: var(--error-color, #f44336);
    margin-left: 2px;
  }

  .parameter-description {
    font-size: 12px;
    color: var(--text-muted, #666);
    line-height: 1.3;
  }

  .parameter-input,
  .parameter-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color, #ddd);
    border-radius: 4px;
    font-size: 13px;
    font-family: 'Consolas', 'Monaco', monospace;
    background: white;
    transition: border-color 0.2s;
  }

  .parameter-input:focus,
  .parameter-select:focus {
    outline: none;
    border-color: var(--primary-color, #2196f3);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
  }

  .checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }

  .checkbox-wrapper input[type="checkbox"] {
    margin: 0;
  }

  .checkbox-label {
    font-size: 13px;
    color: var(--text-primary, #333);
  }

  .no-parameters {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
    color: var(--text-muted, #666);
    font-style: italic;
  }

  .no-parameters p {
    margin: 0;
  }

  .access-type-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .access-type-btn {
    padding: 8px 16px;
    border: 1px solid var(--border-color, #ddd);
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .access-type-btn:hover {
    border-color: var(--primary-color, #2196f3);
    background: var(--hover-bg, #f0f8ff);
  }

  .access-type-btn.active {
    background: var(--primary-color, #2196f3);
    color: white;
    border-color: var(--primary-color, #2196f3);
  }

  .access-type-btn.active:hover {
    background: var(--primary-hover, #1976d2);
  }
</style>
