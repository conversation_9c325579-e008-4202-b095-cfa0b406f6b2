import { invoke } from '@tauri-apps/api/core';

/// UI元素类型
export enum UIElementType {
  Button = 'Button',
  Icon = 'Icon',
  Text = 'Text',
  Panel = 'Panel',
  Unknown = 'Unknown',
}

/// 边界框
export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

/// 元素属性
export interface ElementProperties {
  has_border: boolean;
  has_shadow: boolean;
  dominant_colors: string[];
  pixel_count: number;
}

/// 检测到的UI元素
export interface DetectedUIElement {
  id: string;
  bounds: BoundingBox;
  confidence: number;
  element_type: UIElementType;
  properties: ElementProperties;
}

/// 裁切选项
export interface CropOptions {
  min_width: number;
  min_height: number;
  alpha_tolerance: number;
  color_tolerance: number;
  merge_distance: number;
}

/// 智能裁切分析结果
export interface SmartCropResponse {
  success: boolean;
  elements: DetectedUIElement[];
  processing_time: number;
  error?: string;
}

/// 智能裁切请求
export interface SmartCropRequest {
  image_data: string; // base64编码的图像数据
  options?: CropOptions;
}

/// 图像信息
export interface ImageInfo {
  width: number;
  height: number;
  format: string;
  size_bytes: number;
}

/// 智能裁切API类
export class SmartCropAPI {
  
  /**
   * 分析图像并检测UI元素
   * @param imageData base64编码的图像数据
   * @param options 可选的裁切选项
   * @returns 分析结果
   */
  static async analyzeImage(imageData: string, options?: CropOptions): Promise<SmartCropResponse> {
    try {
      const request: SmartCropRequest = {
        image_data: imageData,
        options
      };
      
      console.log('🔍 开始智能裁切分析...', {
        dataLength: imageData.length,
        options
      });
      
      const result = await invoke<SmartCropResponse>('analyze_image_smart_crop', { request });
      
      console.log('✅ 智能裁切分析完成', {
        success: result.success,
        elementsCount: result.elements.length,
        processingTime: result.processing_time
      });
      
      return result;
    } catch (error) {
      console.error('❌ 智能裁切分析失败:', error);
      return {
        success: false,
        elements: [],
        processing_time: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 从文件路径分析图像
   * @param filePath 图像文件路径
   * @param options 可选的裁切选项
   * @returns 分析结果
   */
  static async analyzeImageFromPath(filePath: string, options?: CropOptions): Promise<SmartCropResponse> {
    try {
      console.log('🔍 从文件分析图像:', filePath);
      
      const result = await invoke<SmartCropResponse>('analyze_image_from_path', { 
        file_path: filePath, 
        options 
      });
      
      console.log('✅ 文件分析完成', {
        success: result.success,
        elementsCount: result.elements.length,
        processingTime: result.processing_time
      });
      
      return result;
    } catch (error) {
      console.error('❌ 文件分析失败:', error);
      return {
        success: false,
        elements: [],
        processing_time: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 获取默认裁切选项
   * @returns 默认选项
   */
  static async getDefaultOptions(): Promise<CropOptions> {
    try {
      return await invoke<CropOptions>('get_default_crop_options');
    } catch (error) {
      console.error('❌ 获取默认选项失败:', error);
      // 返回前端默认值
      return {
        min_width: 16,
        min_height: 16,
        alpha_tolerance: 10,
        color_tolerance: 30,
        merge_distance: 5
      };
    }
  }

  /**
   * 验证图像数据
   * @param imageData base64编码的图像数据
   * @returns 图像信息
   */
  static async validateImageData(imageData: string): Promise<ImageInfo | null> {
    try {
      return await invoke<ImageInfo>('validate_image_data', { image_data: imageData });
    } catch (error) {
      console.error('❌ 图像数据验证失败:', error);
      return null;
    }
  }

  /**
   * 将Canvas转换为base64数据
   * @param canvas Canvas元素
   * @param quality 图像质量 (0-1)
   * @returns base64数据（不包含data:前缀）
   */
  static canvasToBase64(canvas: HTMLCanvasElement, quality: number = 0.9): string {
    const dataUrl = canvas.toDataURL('image/png', quality);
    // 移除 "data:image/png;base64," 前缀
    return dataUrl.split(',')[1];
  }

  /**
   * 将图像元素转换为base64数据
   * @param img 图像元素
   * @param quality 图像质量 (0-1)
   * @returns base64数据（不包含data:前缀）
   */
  static imageToBase64(img: HTMLImageElement, quality: number = 0.9): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('无法获取Canvas上下文');
    }
    
    canvas.width = img.naturalWidth;
    canvas.height = img.naturalHeight;
    
    ctx.drawImage(img, 0, 0);
    
    return this.canvasToBase64(canvas, quality);
  }

  /**
   * 将DetectedUIElement转换为CropRegion
   * @param elements 检测到的UI元素数组
   * @returns CropRegion数组
   */
  static elementsToRegions(elements: DetectedUIElement[]): Array<{
    id: string;
    label: string;
    sx: number;
    sy: number;
    sw: number;
    sh: number;
    gridIndex: number;
  }> {
    return elements.map((element, index) => ({
      id: element.id,
      label: `${element.element_type}_${index + 1}`,
      sx: element.bounds.x,
      sy: element.bounds.y,
      sw: element.bounds.width,
      sh: element.bounds.height,
      gridIndex: index
    }));
  }

  /**
   * 格式化处理时间
   * @param milliseconds 毫秒数
   * @returns 格式化的时间字符串
   */
  static formatProcessingTime(milliseconds: number): string {
    if (milliseconds < 1000) {
      return `${milliseconds}ms`;
    } else {
      return `${(milliseconds / 1000).toFixed(2)}s`;
    }
  }

  /**
   * 获取元素类型的显示名称
   * @param elementType 元素类型
   * @returns 显示名称
   */
  static getElementTypeDisplayName(elementType: UIElementType): string {
    const names = {
      [UIElementType.Button]: '按钮',
      [UIElementType.Icon]: '图标',
      [UIElementType.Text]: '文本',
      [UIElementType.Panel]: '面板',
      [UIElementType.Unknown]: '未知'
    };
    return names[elementType] || '未知';
  }

  /**
   * 获取元素类型的颜色
   * @param elementType 元素类型
   * @returns CSS颜色值
   */
  static getElementTypeColor(elementType: UIElementType): string {
    const colors = {
      [UIElementType.Button]: '#4CAF50',
      [UIElementType.Icon]: '#2196F3',
      [UIElementType.Text]: '#FF9800',
      [UIElementType.Panel]: '#9C27B0',
      [UIElementType.Unknown]: '#757575'
    };
    return colors[elementType] || '#757575';
  }
}
