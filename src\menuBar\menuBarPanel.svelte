<script lang="ts">
  import { onMount } from 'svelte';
  import { Menu } from '@tauri-apps/api/menu';
  import { TauriAPI } from '../lib/tauriAPI';
  import { projectActions } from '../stores/projectStore';
  import { projectLoader } from '../logics/projectManage/projectLoad';
  import { exportPlug } from '../filePro';
  import { modalService } from '../stores/modalStore.svelte';
  import { getCurrentSceneName } from '../DataMapping';

  // 菜单状态
  let menuCreated = $state(false);
  let isMaximized = $state(false);

  // 创建原生窗口菜单
  async function createNativeMenu() {
    try {
      // 创建文件菜单
      const menu = await Menu.new({
        items: [
          {
            id: 'file',
            text: '文件',
            items: [
              {
                id: 'open',
                text: '打开',
                accelerator: 'Ctrl+O',
                action: () => {
                  handleMenuAction('open');
                }
              },
              {
                id: 'save',
                text: '保存',
                accelerator: 'Ctrl+S',
                action: () => {
                  handleMenuAction('save');
                }
              },
              {
                id: 'separator1',
                text: '',
                item: 'Separator'
              },
              {
                id: 'exit',
                text: '退出',
                accelerator: 'Ctrl+Q',
                action: () => {
                  handleMenuAction('exit');
                }
              }
            ]
          },
          {
            id: 'edit',
            text: '编辑',
            items: [
              {
                id: 'undo',
                text: '撤销',
                accelerator: 'Ctrl+Z',
                action: () => {
                  handleMenuAction('undo');
                }
              },
              {
                id: 'redo',
                text: '重做',
                accelerator: 'Ctrl+Y',
                action: () => {
                  handleMenuAction('redo');
                }
              },
              {
                id: 'separator2',
                text: '',
                item: 'Separator'
              },
              {
                id: 'cut',
                text: '剪切',
                accelerator: 'Ctrl+X',
                action: () => {
                  handleMenuAction('cut');
                }
              },
              {
                id: 'copy',
                text: '复制',
                accelerator: 'Ctrl+C',
                action: () => {
                  handleMenuAction('copy');
                }
              },
              {
                id: 'paste',
                text: '粘贴',
                accelerator: 'Ctrl+V',
                action: () => {
                  handleMenuAction('paste');
                }
              }
            ]
          },
          {
            id: 'view',
            text: '视图',
            items: [
              {
                id: 'zoom_in',
                text: '放大',
                accelerator: 'Ctrl+Plus',
                action: () => {
                  handleMenuAction('zoom_in');
                }
              },
              {
                id: 'zoom_out',
                text: '缩小',
                accelerator: 'Ctrl+-',
                action: () => {
                  handleMenuAction('zoom_out');
                }
              },
              {
                id: 'zoom_reset',
                text: '重置缩放',
                accelerator: 'Ctrl+0',
                action: () => {
                  handleMenuAction('zoom_reset');
                }
              },
              {
                id: 'separator3',
                text: '',
                item: 'Separator'
              },
              {
                id: 'toggle_dataflow',
                text: '数据流分析',
                accelerator: 'F2',
                action: () => {
                  handleMenuAction('toggle_dataflow');
                }
              },
              {
                id: 'separator3',
                text: '',
                item: 'Separator'
              },
              {
                id: 'toggle_dataflow',
                text: '数据流分析',
                accelerator: 'F2',
                action: () => {
                  handleMenuAction('toggle_dataflow');
                }
              }
            ]
          },
          {
            id: 'help',
            text: '帮助',
            items: [
              {
                id: 'about',
                text: '关于',
                action: () => {
                  handleMenuAction('about');
                }
              }
            ]
          }
        ]
      });

      // 只为当前窗口设置菜单，不影响其他窗口
      const { getCurrentWebviewWindow } = await import('@tauri-apps/api/webviewWindow');
      const currentWindow = getCurrentWebviewWindow();
      await currentWindow.setMenu(menu);

      menuCreated = true;
      console.log('原生菜单创建成功');

    } catch (error) {
      console.error('创建菜单失败:', error);
    }
  }

  // 处理菜单项点击事件
  async function handleMenuAction(actionId: string) {
    console.log(`菜单项被点击: ${actionId}`);

    switch (actionId) {
      case 'open':
        await handleOpenFile();
        break;
      case 'save':
        await handleSaveFile();
        break;
      case 'exit':
        await handleExitApp();
        break;
      case 'undo':
        handleUndo();
        break;
      case 'redo':
        handleRedo();
        break;
      case 'cut':
        handleCut();
        break;
      case 'copy':
        handleCopy();
        break;
      case 'paste':
        handlePaste();
        break;
      case 'zoom_in':
        handleZoomIn();
        break;
      case 'zoom_out':
        handleZoomOut();
        break;
      case 'zoom_reset':
        handleZoomReset();
        break;
      case 'toggle_dataflow':
        handleToggleDataFlow();
        break;
      case 'about':
        handleAbout();
        break;
      default:
        console.log(`未处理的菜单项: ${actionId}`);
    }
  }

  // 文件操作处理函数
  async function handleOpenFile() {

    console.log('=== 开始项目选择流程 ===');

    try {
      // 使用项目加载器选择并加载项目
      const result = await projectLoader.selectAndLoadProject();

      if (result.success && result.data) {
        // 项目加载成功
        const { projectName, projectPath, scripts } = result.data;

        console.log('=== 项目加载成功 ===');
        console.log('项目名称:', projectName);
        console.log('项目路径:', projectPath);
        console.log('脚本数量:', scripts.length);
        console.log('全局配置已设置，CenterPanel 将自动加载引擎');

      } else {
        // 项目加载失败
        const error = result.error || '项目加载失败';
        console.error('项目加载失败:', error);
      }

    } catch (error) {
      console.error('项目选择过程中发生错误:', error);
      const errorMsg = `项目选择失败: ${error}`;
      projectActions.setError(errorMsg);
    }
  }

  async function handleSaveFile() {
    const result = await TauriAPI.File.saveFileDialog();
    if (result.success && result.data) {
      console.log('保存文件:', result.data);
    }
  }

  async function handleExitApp() {
    const result = await TauriAPI.Window.close();
    if (!result.success) {
      console.error(`退出失败: ${result.error}`)

    }
  }

  // 编辑操作处理函数
  function handleUndo() {
    console.log('执行撤销操作');

  }

  function handleRedo() {
    console.log('执行重做操作');
  }

  function handleCut() {
    console.log('执行剪切操作');

  }

  function handleCopy() {
    console.log('执行复制操作');
  }

  function handlePaste() {
    console.log('执行粘贴操作');
  }

  // 视图操作处理函数
  function handleZoomIn() {
    console.log('执行放大操作');
  }

  function handleZoomOut() {
    console.log('执行缩小操作');
  }

  function handleZoomReset() {
    console.log('执行重置缩放操作');
  }

  // 数据流分析操作处理函数
  function handleToggleDataFlow() {
    console.log('打开数据流分析模态框');

    try {
      // 获取当前场景名称
      const currentScene = getCurrentSceneName();
      const sceneName = currentScene || 'Scene_Title'; // 默认显示Scene_Title

      console.log('当前场景:', sceneName);

      // 打开数据流分析模态框
      modalService.openDataFlow({
        sceneName: sceneName
      });

      console.log('数据流分析模态框已打开');
    } catch (error) {
      console.error('打开数据流分析失败:', error);
    }
  }

  function handleAbout() {
    console.log('显示关于对话框');
  }

  // 导出插件处理函数
  async function handleExportPlugin() {
    console.log('开始导出插件');

    try {
      // 使用默认的插件配置（在 pluginGenerator.ts 中定义）
      await exportPlug();
      console.log('插件导出完成');
    } catch (error) {
      console.error('导出插件失败:', error);
    }
  }

  // 窗口控制函数
  async function minimizeWindow() {
    const result = await TauriAPI.Window.minimize();
    if (result.success) {
      console.log('窗口已最小化');
    } else {
      console.error('最小化失败:', result.error);
    }
  }

  async function maximizeWindow() {
    const result = await TauriAPI.Window.toggleMaximize();
    if (result.success && result.data !== undefined) {
      isMaximized = result.data;
     let statusMessage = isMaximized ? '窗口已最大化' : '窗口已还原';
      console.log(statusMessage);
    } else {
     const statusMessage = `操作失败: ${result.error}`;
      console.error(statusMessage);
    }
  }

  async function closeWindow() {
    const result = await TauriAPI.Window.close();
    if (!result.success) {
     const statusMessage = `关闭失败: ${result.error}`;
      console.error(statusMessage);
    }
  }

  // 检查窗口状态
  async function checkWindowState() {
    const result = await TauriAPI.Window.isMaximized();
    if (result.success && result.data !== undefined) {
      isMaximized = result.data;
    }
  }

  // 组件挂载时创建菜单和检查窗口状态
  onMount(() => {
    createNativeMenu();

    // 暂时禁用所有窗口状态检查来调试问题
    console.log('🔧 调试模式：已禁用窗口状态检查');

    // checkWindowState(); // 暂时禁用

    // 暂时禁用轮询和事件监听
    // const interval = setInterval(checkWindowState, 5000);
    // window.addEventListener('resize', handleResize);

    return () => {
      // clearInterval(interval);
      // window.removeEventListener('resize', handleResize);
      console.log('🔧 调试模式：菜单栏组件清理完成');
    };
  });
</script>

<!-- 自定义标题栏 -->
<div class="title-bar" data-tauri-drag-region>
  <!-- 应用图标和标题 -->
  <div class="title-section">
    <div class="app-icon">🎮</div>
    <span class="app-title">RPG Maker MZL</span>
  </div>

  <!-- 菜单栏 -->
  <div class="menu-section">
    <div class="menu-item" data-menu="file">
      文件
      <div class="dropdown-menu">
        <div class="menu-option" onclick={() => handleMenuAction('open')}>
          <span>打开</span>
          <span class="shortcut">Ctrl+O</span>
        </div>
        <div class="menu-option" onclick={() => handleMenuAction('save')}>
          <span>保存</span>
          <span class="shortcut">Ctrl+S</span>
        </div>
        <div class="menu-separator"></div>
        <div class="menu-option" onclick={() => handleMenuAction('exit')}>
          <span>退出</span>
          <span class="shortcut">Ctrl+Q</span>
        </div>
      </div>
    </div>

    <div class="menu-item" data-menu="edit">
      编辑
      <div class="dropdown-menu">
        <div class="menu-option" onclick={() => handleMenuAction('undo')}>
          <span>撤销</span>
          <span class="shortcut">Ctrl+Z</span>
        </div>
        <div class="menu-option" onclick={() => handleMenuAction('redo')}>
          <span>重做</span>
          <span class="shortcut">Ctrl+Y</span>
        </div>
        <div class="menu-separator"></div>
        <div class="menu-option" onclick={() => handleMenuAction('cut')}>
          <span>剪切</span>
          <span class="shortcut">Ctrl+X</span>
        </div>
        <div class="menu-option" onclick={() => handleMenuAction('copy')}>
          <span>复制</span>
          <span class="shortcut">Ctrl+C</span>
        </div>
        <div class="menu-option" onclick={() => handleMenuAction('paste')}>
          <span>粘贴</span>
          <span class="shortcut">Ctrl+V</span>
        </div>
      </div>
    </div>

    <div class="menu-item" data-menu="view">
      视图
      <div class="dropdown-menu">
        <div class="menu-option" onclick={() => handleMenuAction('zoom_in')}>
          <span>放大</span>
          <span class="shortcut">Ctrl++</span>
        </div>
        <div class="menu-option" onclick={() => handleMenuAction('zoom_out')}>
          <span>缩小</span>
          <span class="shortcut">Ctrl+-</span>
        </div>
        <div class="menu-option" onclick={() => handleMenuAction('zoom_reset')}>
          <span>重置缩放</span>
          <span class="shortcut">Ctrl+0</span>
        </div>
        <div class="menu-separator"></div>
        <div class="menu-option" onclick={() => handleMenuAction('toggle_dataflow')}>
          <span>数据流分析</span>
          <span class="shortcut">F2</span>
        </div>
      </div>
    </div>

    <div class="menu-item" data-menu="help">
      帮助
      <div class="dropdown-menu">
        <div class="menu-option" onclick={() => handleMenuAction('about')}>
          <span>关于</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 工具栏按钮 -->
  <div class="toolbar-section">
    <button class="toolbar-btn export-plugin-btn" onclick={handleExportPlugin} title="导出插件">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 1a.5.5 0 0 1 .5.5v6h2.793l-3.147 3.146a.5.5 0 0 1-.708 0L4.293 7.5H7V1.5A.5.5 0 0 1 8 1z"/>
        <path d="M3 9.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5z"/>
        <path d="M2.5 12.5A.5.5 0 0 1 3 12h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"/>
      </svg>
      <span>导出插件</span>
    </button>
  </div>

  <!-- 空白拖拽区域 -->
  <div class="drag-area"></div>

  <!-- 窗口控制按钮 -->
  <div class="window-controls">
    <button class="control-btn minimize-btn" onclick={minimizeWindow} title="最小化">
      <svg width="12" height="12" viewBox="0 0 12 12">
        <rect x="2" y="5" width="8" height="2" fill="currentColor"/>
      </svg>
    </button>

    <button class="control-btn maximize-btn" onclick={maximizeWindow} title={isMaximized ? '还原' : '最大化'}>
      {#if isMaximized}
        <svg width="12" height="12" viewBox="0 0 12 12">
          <rect x="2" y="2" width="6" height="6" stroke="currentColor" stroke-width="1" fill="none"/>
          <rect x="4" y="4" width="6" height="6" stroke="currentColor" stroke-width="1" fill="none"/>
        </svg>
      {:else}
        <svg width="12" height="12" viewBox="0 0 12 12">
          <rect x="2" y="2" width="8" height="8" stroke="currentColor" stroke-width="1" fill="none"/>
        </svg>
      {/if}
    </button>

    <button class="control-btn close-btn" onclick={closeWindow} title="关闭">
      <svg width="12" height="12" viewBox="0 0 12 12">
        <path d="M2 2 L10 10 M10 2 L2 10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    </button>
  </div>
</div>



<style>
  /* 标题栏样式 */
  .title-bar {
    height: var(--title-bar-height);
    background: linear-gradient(135deg, var(--theme-primary-dark) 0%, var(--theme-primary) 100%);
    color: var(--theme-text);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--theme-border-light);
    user-select: none;
    position: relative;
    z-index: 1000;
  }

  /* 标题区域 */
  .title-section {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 12px;
    flex-shrink: 0;
  }

  .app-icon {
    font-size: 16px;
  }

  .app-title {
    font-size: 13px;
    font-weight: 500;
    opacity: 0.9;
  }

  /* 菜单区域 */
  .menu-section {
    display: flex;
    align-items: center;
    padding: 0 8px;
  }

  /* 拖拽区域 */
  .drag-area {
    flex: 1;
    height: 100%;
    min-width: 100px;
  }

  .menu-item {
    position: relative;
    padding: 6px 12px;
    font-size: 13px;
    cursor: pointer;
    border-radius: 3px;
    transition: background-color 0.2s;
  }

  .menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .menu-item:hover .dropdown-menu {
    display: block;
  }

  /* 下拉菜单 */
  .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 180px;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px var(--theme-shadow);
    z-index: 1001;
    padding: var(--spacing-1) 0;
  }

  .menu-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .menu-option:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .shortcut {
    font-size: 11px;
    opacity: 0.7;
    font-family: 'Courier New', monospace;
  }

  .menu-separator {
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
    margin: 4px 8px;
  }

  /* 窗口控制按钮 */
  .window-controls {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .control-btn {
    width: 46px;
    height: 32px;
    border: none;
    background: transparent;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
  }

  .control-btn:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .close-btn:hover {
    background: #e53e3e;
  }

  /* 工具栏样式 */
  .toolbar-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-left: auto;
    margin-right: var(--spacing-3);
    -webkit-app-region: no-drag;
  }

  .toolbar-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-1) var(--spacing-2);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-sm);
    color: var(--theme-text);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all 0.2s ease;
    -webkit-app-region: no-drag;
  }

  .toolbar-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }

  .toolbar-btn:active {
    transform: translateY(0);
    background: rgba(255, 255, 255, 0.2);
  }

  .toolbar-btn svg {
    width: 14px;
    height: 14px;
    opacity: 0.9;
  }

  .export-plugin-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: #10b981;
  }

  .export-plugin-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-color: #059669;
  }



  /* 拖拽区域配置 */
  [data-tauri-drag-region] {
    -webkit-app-region: drag;
  }

  /* 防止菜单和按钮被拖拽 */
  .menu-item,
  .control-btn,
  .toolbar-btn,
  .menu-option,
  .dropdown-menu {
    -webkit-app-region: no-drag;
  }

  /* 确保下拉菜单不会被拖拽影响 */
  .dropdown-menu {
    pointer-events: auto;
  }

  /* 确保整个标题栏可以拖拽，但菜单和按钮除外 */
  .title-bar {
    -webkit-app-region: drag;
  }

  .menu-section,
  .toolbar-section,
  .window-controls {
    -webkit-app-region: no-drag;
  }
</style>