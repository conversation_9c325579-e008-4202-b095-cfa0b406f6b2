/**
 * Scene_Map 场景数据流定义
 */

import type { SceneDataFlow } from '../types';

/**
 * Scene_Map 场景的数据流
 */
export const SceneMapDataFlow: SceneDataFlow = {
  sceneName: "Scene_Map",
  buttons: [
    {
      buttonName: "Menu",
      triggerMethods: [
        "SceneManager.push(Scene_Menu)"
      ]
    },
    {
      buttonName: "Save", 
      triggerMethods: [
        "SceneManager.push(Scene_Save)",
        "DataManager.saveGame()"
      ]
    },
    {
      buttonName: "Load",
      triggerMethods: [
        "SceneManager.push(Scene_Load)",
        "DataManager.loadGame()"
      ]
    },
    {
      buttonName: "Transfer",
      triggerMethods: [
        "$gamePlayer.reserveTransfer()",
        "SceneManager.goto(Scene_Map)"
      ]
    }
  ]
};
