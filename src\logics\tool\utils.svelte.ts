/**
 * 选中拖动工具的状态管理工具
 * 使用 Svelte 5 的响应式系统管理工具状态
 */

import { ToolIntegration } from './ToolIntegration';
import type { ToolConfig } from './types';
import { sceneModelState } from '../../stores/sceneModelStore';

/**
 * 工具状态管理类
 */
class SelectionToolManager {
  // 工具实例
  private tool: ToolIntegration | null = null;

  // 响应式状态
  private _isInitialized = $state(false);
  private _isEnabled = $state(true);
  private _hasSelection = $state(false);
  private _isDragging = $state(false);
  private _currentCanvas: HTMLCanvasElement | null = null;

  // 🔑 防重复调用
  private _lastSelectedObject: BaseObjectModel | null = null;

  constructor() {
    // 监听场景状态变化
    this.setupSceneSubscription();
  }

  /**
   * 设置场景状态订阅
   */
  private setupSceneSubscription() {
    // 订阅场景状态变化
    sceneModelState.subscribe(state => {
      if (this.tool && state) {
        // 同步选中状态
        const hasSelectedObjects = state.selectedObjects && state.selectedObjects.length > 0;
        const primaryObject = hasSelectedObjects && state.primarySelectedIndex >= 0
          ? state.selectedObjects[state.primarySelectedIndex]
          : null;

        // 🔑 防重复调用：只有当选中对象真正改变时才更新工具
        if (this._lastSelectedObject !== primaryObject) {
          this._lastSelectedObject = primaryObject;

          // 🚨 暂时禁用工具同步，避免循环更新
          // TODO: 需要重构工具系统的响应式逻辑
          // if (this.tool) {
          //   this.tool.getTool().selectObject(primaryObject);
          // }
          this._hasSelection = !!primaryObject;

          console.log('🎯 工具状态同步 (已禁用):', {
            hasSelectedObjects,
            primaryObject: primaryObject?.className,
            toolHasSelection: this._hasSelection
          });
        }
      }
    });
  }

  /**
   * 初始化工具
   */
  initialize(canvas: HTMLCanvasElement, config?: Partial<ToolConfig>): boolean {
    try {
      console.log('🎯 初始化选中拖动工具...');

      // 如果已经初始化，先销毁
      if (this.tool) {
        this.destroy();
      }

      // 创建工具实例
      const toolConfig = {
        ...TOOL_PRESETS.UNITY_STYLE,
        ...config
      };

      this.tool = new ToolIntegration(toolConfig);

      // 初始化到Canvas
      this.tool.initialize(canvas);

      // 更新状态
      this._currentCanvas = canvas;
      this._isInitialized = true;
      this._isEnabled = true;

      console.log('✅ 选中拖动工具初始化成功');
      return true;

    } catch (error) {
      console.error('❌ 选中拖动工具初始化失败:', error);
      this._isInitialized = false;
      return false;
    }
  }

  /**
   * 销毁工具
   */
  destroy(): void {
    if (this.tool) {
      console.log('🎯 销毁选中拖动工具...');
      this.tool.destroy();
      this.tool = null;
    }

    this._isInitialized = false;
    this._isEnabled = false;
    this._hasSelection = false;
    this._isDragging = false;
    this._currentCanvas = null;

    console.log('✅ 选中拖动工具已销毁');
  }

  /**
   * 启用/禁用工具
   */
  setEnabled(enabled: boolean): void {
    if (this.tool) {
      this.tool.setEnabled(enabled);
      this._isEnabled = enabled;
      console.log(`🎯 工具${enabled ? '启用' : '禁用'}`);
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ToolConfig>): void {
    if (this.tool) {
      this.tool.updateConfig(config);
      console.log('🎯 工具配置已更新');
    }
  }

  /**
   * 手动选择对象（会更新全局状态）
   */
  selectObjectAndUpdateStore(object: any): void {
    if (this.tool) {
      this.tool.selectObjectAndUpdateStore(object);
      this._hasSelection = !!object;
    }
  }

  /**
   * 内部选择对象（仅更新工具状态）
   */
  selectObjectInternal(object: any): void {
    if (this.tool) {
      this.tool.selectObject(object);
      this._hasSelection = !!object;
    }
  }

  /**
   * 清除选择
   */
  clearSelection(): void {
    if (this.tool) {
      this.tool.getTool().clearSelection();
      this._hasSelection = false;
    }
  }

  /**
   * 检查工具状态
   */
  checkToolStatus(): void {
    if (this.tool) {
      this._hasSelection = this.tool.hasSelection();
      this._isDragging = this.tool.isDragging();

      console.log('🎯 工具状态检查:', {
        isInitialized: this._isInitialized,
        isEnabled: this._isEnabled,
        hasSelection: this._hasSelection,
        isDragging: this._isDragging
      });
    }
  }

  // Getter 方法（响应式）
  get isInitialized(): boolean {
    return this._isInitialized;
  }

  get isEnabled(): boolean {
    return this._isEnabled;
  }

  get hasSelection(): boolean {
    return this._hasSelection;
  }

  get isDragging(): boolean {
    return this._isDragging;
  }

  get currentCanvas(): HTMLCanvasElement | null {
    return this._currentCanvas;
  }

  /**
   * 获取工具实例（用于高级操作）
   */
  getTool(): ToolIntegration | null {
    return this.tool;
  }
}

/**
 * 全局工具管理器实例
 */
const globalToolManager = new SelectionToolManager();

/**
 * 对外暴露的初始化接口
 */
export function initializeSelectionTool(
  canvas: HTMLCanvasElement,
  config?: Partial<ToolConfig>
): boolean {
  return globalToolManager.initialize(canvas, config);
}

/**
 * 销毁工具
 */
export function destroySelectionTool(): void {
  globalToolManager.destroy();
}

/**
 * 启用/禁用工具
 */
export function setSelectionToolEnabled(enabled: boolean): void {
  globalToolManager.setEnabled(enabled);
}

/**
 * 更新工具配置
 */
export function updateSelectionToolConfig(config: Partial<ToolConfig>): void {
  globalToolManager.updateConfig(config);
}

/**
 * 获取工具状态（响应式）
 */
export function getSelectionToolState() {
  return {
    get isInitialized() { return globalToolManager.isInitialized; },
    get isEnabled() { return globalToolManager.isEnabled; },
    get hasSelection() { return globalToolManager.hasSelection; },
    get isDragging() { return globalToolManager.isDragging; },
    get currentCanvas() { return globalToolManager.currentCanvas; }
  };
}

/**
 * 手动选择对象（会更新全局状态）
 */
export function selectObjectInTool(object: any): void {
  globalToolManager.selectObjectAndUpdateStore(object);
}

/**
 * 清除选择
 */
export function clearSelectionInTool(): void {
  globalToolManager.clearSelection();
}

/**
 * 检查工具状态
 */
export function checkSelectionToolStatus(): void {
  globalToolManager.checkToolStatus();
}

/**
 * 获取工具实例（用于高级操作）
 */
export function getSelectionToolInstance(): ToolIntegration | null {
  return globalToolManager.getTool();
}

/**
 * 预设配置
 */
export const TOOL_PRESETS = {
  UNITY_STYLE: {
    arrowSize: 10,
    arrowOffset: 25,
    arrowColors: {
      normal: '#FF6B35',
      hover: '#FF8C42',
      active: '#FF4500'
    },
    boundingBox: {
      strokeColor: '#666666',
      strokeWidth: 1,
      fillColor: 'rgba(102, 102, 102, 0.1)',
      dashPattern: [3, 3] as number[]
    },
    dragSensitivity: 1,
    snapToGrid: false,
    gridSize: 16
  },
  MINIMAL: {
    arrowSize: 6,
    arrowOffset: 20,
    arrowColors: {
      normal: '#666666',
      hover: '#333333',
      active: '#000000'
    },
    boundingBox: {
      strokeColor: '#666666',
      strokeWidth: 1,
      dashPattern: [3, 3] as number[]
    },
    dragSensitivity: 1,
    snapToGrid: false,
    gridSize: 10
  },
  COLORFUL: {
    arrowSize: 10,
    arrowOffset: 28,
    arrowColors: {
      normal: '#E91E63',
      hover: '#C2185B',
      active: '#AD1457'
    },
    boundingBox: {
      strokeColor: '#E91E63',
      strokeWidth: 2,
      fillColor: 'rgba(233, 30, 99, 0.1)',
      dashPattern: [] as number[]
    },
    dragSensitivity: 1,
    snapToGrid: true,
    gridSize: 8
  }
} as const;
