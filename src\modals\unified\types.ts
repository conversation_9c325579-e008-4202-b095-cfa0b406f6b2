// 基础类型定义
export interface Category {
  id: string;
  label: string;
  icon: string;
  description: string;
  items: Record<string, DataItem | Method>;
}

// 数据项类型
export interface DataItem {
  id: string;
  label: string;
  description: string;
  path: string;           // 访问路径，如 '$dataActors'
  hasIndex?: boolean;     // 是否需要索引，如 '$dataActors[index]'
  fields?: DataField[];   // 可用字段
  example?: string;       // 使用示例
}

// 数据字段
export interface DataField {
  name: string;
  label: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description?: string;
}

// 方法类型
export interface Method {
  id: string;
  label: string;
  description: string;
  template: string;       // 代码模板，如 'DataManager.loadGame({saveId})'
  params?: Parameter[];   // 参数定义
  returnType?: string;    // 返回类型
  example?: string;       // 使用示例
  category?: string;      // 方法分类
}

// 参数定义
export interface Parameter {
  name: string;
  label: string;
  type: 'string' | 'number' | 'boolean' | 'itemId';
  required?: boolean;
  defaultValue?: any;
  description?: string;
  options?: Array<{ value: any; label: string }>; // 枚举选项
}

// 生成的代码
export interface GeneratedCode {
  code: string;
  description: string;
  category: string;
  item: string;
  parameters?: Record<string, any>;
}

// 模态框模式
export type ModalMode = 'method' | 'data' | 'event';

// RPG Maker MZ 数据类型
export type RPGMakerDataType = 
  | '$dataActors'
  | '$dataClasses' 
  | '$dataSkills'
  | '$dataItems'
  | '$dataWeapons'
  | '$dataArmors'
  | '$dataEnemies'
  | '$dataTroops'
  | '$dataStates'
  | '$dataAnimations'
  | '$dataTilesets'
  | '$dataCommonEvents'
  | '$dataSystem'
  | '$dataMapInfos'
  | '$dataMap'
  | '$gameTemp'
  | '$gameSystem'
  | '$gameScreen'
  | '$gameTimer'
  | '$gameMessage'
  | '$gameSwitches'
  | '$gameVariables'
  | '$gameSelfSwitches'
  | '$gameActors'
  | '$gameParty'
  | '$gameTroop'
  | '$gameMap'
  | '$gamePlayer';

// RPG Maker MZ 管理器类型
export type RPGMakerManagerType =
  | 'DataManager'
  | 'SceneManager'
  | 'BattleManager'
  | 'ImageManager'
  | 'AudioManager'
  | 'PluginManager'
  | 'StorageManager';

// 场景类型
export type SceneType =
  | 'Scene_Map'
  | 'Scene_Title'
  | 'Scene_Menu'
  | 'Scene_Item'
  | 'Scene_Skill'
  | 'Scene_Equip'
  | 'Scene_Status'
  | 'Scene_Options'
  | 'Scene_Save'
  | 'Scene_Load'
  | 'Scene_GameEnd'
  | 'Scene_Shop'
  | 'Scene_Name'
  | 'Scene_Debug'
  | 'Scene_Battle';

// 事件类型
export type EventType =
  | 'onClick'
  | 'onDoubleClick'
  | 'onHover'
  | 'onMouseEnter'
  | 'onMouseLeave'
  | 'onFocus'
  | 'onBlur'
  | 'onChange'
  | 'onInput';
