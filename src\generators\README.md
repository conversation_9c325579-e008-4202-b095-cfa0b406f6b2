# 可扩展继承架构系统

基于继承的对象处理器系统，支持无限扩展的 Sprite 类型。

## 🎯 最新更新：模块化立即执行函数结构

### 代码生成结构优化
现在生成的插件代码采用完全模块化的立即执行函数结构：

1. **插件列表立即执行方法**：所有依赖插件在独立的 IIFE 中加载
2. **每个场景独立的立即执行方法**：每个场景的代码都包装在独立的 IIFE 中

```javascript
// ===== 插件代码立即执行方法 =====
(() => {
  "use strict";
  console.log("RPG Editor: 开始加载插件代码");
  // 所有依赖插件代码...
  console.log("RPG Editor: 插件代码加载完成");
})();

// ===== Scene_Title 场景立即执行方法 =====
(() => {
  "use strict";
  console.log("RPG Editor: 开始处理 Scene_Title");
  // Scene_Title 相关代码...
  console.log("RPG Editor: Scene_Title 处理完成");
})();

// ===== Scene_Map 场景立即执行方法 =====
(() => {
  "use strict";
  console.log("RPG Editor: 开始处理 Scene_Map");
  // Scene_Map 相关代码...
  console.log("RPG Editor: Scene_Map 处理完成");
})();
```

### 优势
- **模块化隔离**：每个场景在独立作用域中执行，避免变量污染
- **更好的调试**：可以单独启用/禁用某个场景的代码
- **代码组织清晰**：每个场景都是一个完整的功能单元
- **错误隔离**：某个场景出错不会影响其他场景

## 🏗️ 核心设计原则

### 1. 完全可扩展的继承体系
```typescript
BaseProcessor<BaseDisplayProperties>
    ↓
SpriteProcessor<SpriteProperties>
    ↓
SpriteButtonProcessor<SpriteButtonProperties>
    ↓
YourCustomProcessor<YourCustomProperties>
```

### 2. 零配置扩展
- 添加新类型只需要创建一个处理器类
- 自动继承所有基础功能
- 一行代码注册即可使用

### 3. 职责分离
- **序列化**：只提取必要属性
- **反序列化**：创建真正的RPG Maker MZ对象
- **代码生成**：生成可执行的插件代码
- **路径处理**：统一的相对路径处理

## 📁 文件结构

```
src/generators/
├── core/                    # 核心处理器
│   ├── BaseProcessor.ts
│   ├── SpriteProcessor.ts
│   ├── SpriteButtonProcessor.ts
│   ├── ProcessorFactory.ts
│   └── utils/              # 工具类
│       ├── PathUtils.ts
│       └── BitmapUtils.ts
├── types/                  # 类型定义
│   ├── base.ts
│   ├── sprite.ts
│   ├── spriteButton.ts
│   └── index.ts
├── operationManager.ts     # 数据管理
├── pluginGenerator.ts      # 插件生成
└── index.ts               # 统一导出
```

## 🚀 使用方式

### 基础用法

```typescript
import { ProcessorFactory, registerProcessor } from './generators';

// 获取处理器
const processor = ProcessorFactory.getProcessor('Sprite');
const data = processor.serialize(spriteObj);
const code = processor.generateCode('mySprite', data, '  ');
```

### 添加新的 Sprite 类型

```typescript
// 1. 定义类型
interface SpriteHealthBarProperties extends SpriteProperties {
  maxHealth: number;
  currentHealth: number;
  barColor: string;
}

// 2. 创建处理器
class SpriteHealthBarProcessor extends SpriteProcessor {
  serialize(obj: any): SpriteHealthBarProperties {
    const spriteData = super.serialize(obj);
    return {
      ...spriteData,
      maxHealth: obj.maxHealth || 100,
      currentHealth: obj.currentHealth || 100,
      barColor: obj.barColor || '#ff0000'
    };
  }

  generateCode(varName: string, data: SpriteHealthBarProperties, indent: string): string {
    let code = super.generateCode(varName, data, indent);
    code += `\n${indent}// HealthBar 特有属性`;
    code += `\n${indent}${varName}.maxHealth = ${data.maxHealth};`;
    code += `\n${indent}${varName}.currentHealth = ${data.currentHealth};`;
    code += `\n${indent}${varName}.barColor = '${data.barColor}';`;
    return code;
  }
}

// 3. 注册处理器
registerProcessor('SpriteHealthBar', new SpriteHealthBarProcessor());
```

## 🎯 核心优势

### 完全可扩展
- ✅ 添加新类型不需要修改任何现有代码
- ✅ 自动继承所有基础功能
- ✅ 类型安全的 TypeScript 支持

### 性能优化
- ✅ 单例模式：处理器实例复用
- ✅ 预编译正则表达式
- ✅ 常量提取和缓存
- ✅ 智能路径处理

### 清晰的职责分离
- ✅ 每个处理器只关注自己的特有逻辑
- ✅ 工具类专门处理通用功能
- ✅ 工厂模式管理所有处理器

## 🔧 特殊处理

### RPGEditor_BitmapTracker插件
- **Elements数组**：完整保存elements数组
- **图片元素**：只保存URL字符串，异步加载后调用redrawing()
- **创建方式**：传入bitmap数据对象，插件自动处理转换

### 路径处理
- **统一格式**：所有路径都转换为 `'img/xxx/xxx.png'` 格式
- **智能推断**：根据文件名自动推断目录
- **相对路径**：确保生成的代码使用相对路径
```

### 添加新的接口

```typescript
// 2. 在type.ts中添加新接口
export interface WindowProperties extends BaseDisplayProperties {
  windowskin?: string;
  contents?: string;
  contentsOpacity: number;
  openness: number;
  // ... 其他Window属性
}
```

## 测试

运行测试示例：

```typescript
import { runTests } from './usage';
runTests();
```

测试包括：
- 基础序列化器测试
- Sprite序列化器测试
- 继承调用顺序验证
- Elements数组处理测试

## 注意事项

1. **环境检测**：代码会自动检测是否在RPG Maker MZ环境中运行
2. **模拟对象**：在测试环境中会创建模拟对象，保持接口一致性
3. **类型安全**：使用TypeScript确保类型安全
4. **内存管理**：只保存必要数据，避免内存泄漏
5. **插件兼容**：兼容RPGEditor_BitmapTracker和CustomResourcePath插件
