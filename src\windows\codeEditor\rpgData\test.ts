// 简单测试文件
import { dataObjects } from './dataObjects';
import { gameObjects } from './gameObjects';
import { managerObjects } from './managers';
import { basicExamples } from './examples/basic';

console.log('✅ 模块导入测试');
console.log('数据对象数量:', dataObjects.length);
console.log('游戏对象数量:', gameObjects.length);
console.log('管理器对象数量:', managerObjects.length);
console.log('基础示例数量:', basicExamples.length);

export { dataObjects, gameObjects, managerObjects, basicExamples };
