import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * InputModel - UIInput的模型对象
 * 继承自BaseObjectModel，管理UIInput的所有属性和状态
 */
export class InputModel extends BaseObjectModel {
    // 组件类型
    public readonly componentType = 'UIInput';

    // 📝 输入框特有属性
    value = $state('');
    placeholder = $state('请输入文本...');
    inputType = $state('text'); // 'text', 'password', 'number', 'email'
    maxLength = $state(255);
    readonly = $state(false);

    // 📝 样式属性
    fontSize = $state(16);
    textColor = $state('#000000');
    placeholderColor = $state('#999999');
    backgroundColor = $state('#ffffff');
    borderColor = $state('#cccccc');
    focusBorderColor = $state('#007bff');
    borderWidth = $state(1);
    borderRadius = $state(4);
    letterSpacing = $state(0); // 🔑 新增字符间距属性

    // 📝 验证属性
    validationPattern = $state<RegExp | null>(null);
    validationMessage = $state('输入格式不正确');

    // 🔑 脚本系统
    componentScripts = $state<any[]>([]);

    constructor(input: any = {}) {
        super(input);

        // 初始化输入框属性
        this.value = input.value || '';
        this.placeholder = input.placeholder || '请输入文本...';
        this.inputType = input.inputType || 'text';
        this.maxLength = input.maxLength || 255;
        this.readonly = input.readonly || false;

        // 初始化样式属性
        this.fontSize = input.fontSize || 16;
        this.textColor = input.textColor || '#000000';
        this.placeholderColor = input.placeholderColor || '#999999';
        this.backgroundColor = input.backgroundColor || '#ffffff';
        this.borderColor = input.borderColor || '#cccccc';
        this.focusBorderColor = input.focusBorderColor || '#007bff';
        this.borderWidth = input.borderWidth || 1;
        this.borderRadius = input.borderRadius || 4;
        this.letterSpacing = input.letterSpacing || 0; // 🔑 新增字符间距初始化

        // 初始化验证属性
        this.validationPattern = input.validationPattern || null;
        this.validationMessage = input.validationMessage || '输入格式不正确';

        // 🔑 统一的脚本系统 - 从原始对象获取脚本数组
        this.componentScripts = input.componentScripts || [];

        console.log('📝 InputModel: 创建输入框模型', {
            value: this.value,
            inputType: this.inputType,
            size: { width: this.width, height: this.height },
            hasScripts: this.componentScripts.length > 0,
            scriptsCount: this.componentScripts.length
        });

        // setupSync() 已经在基类构造函数中调用了
    }

    /**
     * 设置Input特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Input特有的属性
     */
    protected setupSpecificSync(): void {
        console.log('🔧 InputModel: setupSpecificSync 被调用');

        // 🔑 设置反向同步 - 监听原始对象的值变化
        this.setupReverseSync();

        // 🔑 同步组件脚本数组
        if (this._originalObject.componentScripts !== this.componentScripts) {
            this._originalObject.componentScripts = this.componentScripts;
            console.log('🔧 InputModel: 同步组件脚本', this.componentScripts);
        }

        // 同步输入框值
        if (this._originalObject.value !== this.value) {
            this._originalObject.value = this.value;
            if (this._originalObject.setValue && typeof this._originalObject.setValue === 'function') {
                this._originalObject.setValue(this.value);
            }
        }

        // 同步占位符
        if (this._originalObject.placeholder !== this.placeholder) {
            this._originalObject.placeholder = this.placeholder;
            if (this._originalObject.setPlaceholder && typeof this._originalObject.setPlaceholder === 'function') {
                this._originalObject.setPlaceholder(this.placeholder);
            }
        }

        // 同步输入类型
        if (this._originalObject.inputType !== this.inputType) {
            this._originalObject.inputType = this.inputType;
            if (this._originalObject.setInputType && typeof this._originalObject.setInputType === 'function') {
                this._originalObject.setInputType(this.inputType);
            }
        }

        // 同步最大长度
        if (this._originalObject.maxLength !== this.maxLength) {
            this._originalObject.maxLength = this.maxLength;
            if (this._originalObject.setMaxLength && typeof this._originalObject.setMaxLength === 'function') {
                this._originalObject.setMaxLength(this.maxLength);
            }
        }

        // 同步只读状态
        if (this._originalObject.readonly !== this.readonly) {
            this._originalObject.readonly = this.readonly;
            if (this._originalObject.setReadonly && typeof this._originalObject.setReadonly === 'function') {
                this._originalObject.setReadonly(this.readonly);
            }
        }

        // 同步字体大小
        if (this._originalObject.fontSize !== this.fontSize) {
            this._originalObject.fontSize = this.fontSize;
            if (this._originalObject.setFontSize && typeof this._originalObject.setFontSize === 'function') {
                this._originalObject.setFontSize(this.fontSize);
            }
        }

        // 同步颜色属性
        if (this._originalObject.textColor !== this.textColor) {
            this._originalObject.textColor = this.textColor;
            if (this._originalObject.setTextColor && typeof this._originalObject.setTextColor === 'function') {
                this._originalObject.setTextColor(this.textColor);
            }
        }

        if (this._originalObject.placeholderColor !== this.placeholderColor) {
            this._originalObject.placeholderColor = this.placeholderColor;
            if (this._originalObject.setPlaceholderColor && typeof this._originalObject.setPlaceholderColor === 'function') {
                this._originalObject.setPlaceholderColor(this.placeholderColor);
            }
        }

        if (this._originalObject.backgroundColor !== this.backgroundColor) {
            this._originalObject.backgroundColor = this.backgroundColor;
            if (this._originalObject.setBackgroundColor && typeof this._originalObject.setBackgroundColor === 'function') {
                this._originalObject.setBackgroundColor(this.backgroundColor);
            }
        }

        if (this._originalObject.borderColor !== this.borderColor) {
            this._originalObject.borderColor = this.borderColor;
            if (this._originalObject.setBorderColor && typeof this._originalObject.setBorderColor === 'function') {
                this._originalObject.setBorderColor(this.borderColor);
            }
        }

        if (this._originalObject.focusBorderColor !== this.focusBorderColor) {
            this._originalObject.focusBorderColor = this.focusBorderColor;
            if (this._originalObject.setFocusBorderColor && typeof this._originalObject.setFocusBorderColor === 'function') {
                this._originalObject.setFocusBorderColor(this.focusBorderColor);
            }
        }

        // 同步边框和内边距
        if (this._originalObject.borderWidth !== this.borderWidth) {
            this._originalObject.borderWidth = this.borderWidth;
            if (this._originalObject.setBorderWidth && typeof this._originalObject.setBorderWidth === 'function') {
                this._originalObject.setBorderWidth(this.borderWidth);
            }
        }

        if (this._originalObject.borderRadius !== this.borderRadius) {
            this._originalObject.borderRadius = this.borderRadius;
            if (this._originalObject.setBorderRadius && typeof this._originalObject.setBorderRadius === 'function') {
                this._originalObject.setBorderRadius(this.borderRadius);
            }
        }

        // 🔑 同步字符间距
        if (this._originalObject.letterSpacing !== this.letterSpacing) {
            this._originalObject.letterSpacing = this.letterSpacing;
            if (this._originalObject.setLetterSpacing && typeof this._originalObject.setLetterSpacing === 'function') {
                this._originalObject.setLetterSpacing(this.letterSpacing);
            }
        }

        // 同步验证规则
        if (this._originalObject.validationPattern !== this.validationPattern) {
            this._originalObject.validationPattern = this.validationPattern;
            if (this._originalObject.setValidationPattern && typeof this._originalObject.setValidationPattern === 'function') {
                this._originalObject.setValidationPattern(this.validationPattern);
            }
        }

        if (this._originalObject.validationMessage !== this.validationMessage) {
            this._originalObject.validationMessage = this.validationMessage;
            if (this._originalObject.setValidationMessage && typeof this._originalObject.setValidationMessage === 'function') {
                this._originalObject.setValidationMessage(this.validationMessage);
            }
        }
    }

    /**
     * 🔑 设置反向同步 - 监听原始对象的值变化
     * 当用户在输入框中输入时，同步到模型
     */
    private setupReverseSync(): void {
        if (!this._originalObject) return;

        // 定期检查原始对象的值是否发生变化
        const checkValueChange = () => {
            if (this._originalObject && this._originalObject._value !== this.value) {
                console.log('🔄 InputModel: 检测到原始对象值变化，同步到模型', {
                    originalValue: this._originalObject._value,
                    modelValue: this.value
                });
                this.value = this._originalObject._value;
            }
        };

        // 使用定时器定期检查（每100ms检查一次）
        if (typeof setInterval !== 'undefined') {
            const intervalId = setInterval(checkValueChange, 100);

            // 保存interval ID以便清理
            if (!this._originalObject._reverseSync) {
                this._originalObject._reverseSync = {};
            }
            this._originalObject._reverseSync.valueCheckInterval = intervalId;

            console.log('🔧 InputModel: 反向同步已设置（定时器模式）');
        }

        // 尝试使用更直接的方式 - 重写原始对象的setValue方法
        if (this._originalObject.setValue) {
            const originalSetValue = this._originalObject.setValue.bind(this._originalObject);
            this._originalObject.setValue = (newValue: any) => {
                originalSetValue(newValue);
                // 同步到模型
                if (this.value !== newValue) {
                    console.log('🔄 InputModel: setValue调用，同步到模型', {
                        newValue,
                        oldModelValue: this.value
                    });
                    this.value = newValue;
                }
            };
            console.log('🔧 InputModel: 已重写setValue方法进行反向同步');
        }
    }

    /**
     * 🔑 检查是否有非空的脚本内容
     */
    private _hasNonEmptyScripts(): boolean {
        if (!this.componentScripts || this.componentScripts.length === 0) return false;

        // 检查是否有启用且有代码的脚本
        return this.componentScripts.some(script =>
            script.enabled && script.code && script.code.trim().length > 0
        );
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 创建 UIInput 对象
        codes.push(`${indent}const ${varName} = new UIInput({`);

        // 基础属性
        codes.push(`${indent}    width: ${this.width},`);
        codes.push(`${indent}    height: ${this.height},`);

        // 输入框特有属性
        codes.push(`${indent}    value: '${this.value.replace(/'/g, "\\'")}',`);
        codes.push(`${indent}    placeholder: '${this.placeholder.replace(/'/g, "\\'")}',`);
        codes.push(`${indent}    inputType: '${this.inputType}',`);
        codes.push(`${indent}    maxLength: ${this.maxLength},`);
        codes.push(`${indent}    readonly: ${this.readonly},`);

        // 样式属性
        codes.push(`${indent}    fontSize: ${this.fontSize},`);
        codes.push(`${indent}    textColor: '${this.textColor}',`);
        codes.push(`${indent}    placeholderColor: '${this.placeholderColor}',`);
        codes.push(`${indent}    backgroundColor: '${this.backgroundColor}',`);
        codes.push(`${indent}    borderColor: '${this.borderColor}',`);
        codes.push(`${indent}    focusBorderColor: '${this.focusBorderColor}',`);
        codes.push(`${indent}    borderWidth: ${this.borderWidth},`);
        codes.push(`${indent}    borderRadius: ${this.borderRadius},`);
        codes.push(`${indent}    letterSpacing: ${this.letterSpacing},`); // 🔑 新增字符间距

        // 验证属性
        if (this.validationPattern) {
            codes.push(`${indent}    validationPattern: ${this.validationPattern.toString()},`);
        }
        codes.push(`${indent}    validationMessage: '${this.validationMessage.replace(/'/g, "\\'")}',`);

        // 🔑 生成组件脚本数组（如果有的话）
        if (this.componentScripts && this._hasNonEmptyScripts()) {
            codes.push(`${indent}    componentScripts: ${JSON.stringify(this.componentScripts, null, 8).replace(/\n/g, `\n${indent}`)}`);
        } else {
            // 移除最后一个逗号
            const lastLine = codes[codes.length - 1];
            codes[codes.length - 1] = lastLine.replace(/,$/, '');
        }

        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 重写特定属性设置代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns Input特定属性设置代码
     */
    protected generateSpecificProperties(varName: string, indent: string): string {
        const codes: string[] = [];

        // 输入框通常不需要额外的属性设置代码
        // 因为所有属性都在构造函数中设置了

        return codes.join('\n');
    }

    /**
     * 克隆当前Input对象 - 调用插件的 clone 方法
     */
    clone(): InputModel {
        console.log('🔄 InputModel: 开始克隆Input对象（调用插件方法）');

        // 1. 调用原始 UIInput 对象的 clone 方法
        const originalUIInput = this.getOriginalObject();
        if (!originalUIInput || typeof originalUIInput.clone !== 'function') {
            console.error('❌ InputModel: 原始对象没有 clone 方法');
            throw new Error('UIInput 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUIInput = originalUIInput.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 InputModel 来包装克隆的对象
        const clonedModel = new InputModel({
            // 从克隆的对象获取属性
            ...clonedUIInput.getProperties(),
            // 确保脚本也被克隆
            componentScripts: this.componentScripts.map(script => ({ ...script }))
        });

        // 4. 设置原始对象引用
        (clonedModel as any)._originalObject = clonedUIInput;

        console.log('✅ InputModel: Input对象克隆完成', {
            originalValue: this.value,
            clonedValue: clonedModel.value,
            position: `(${clonedModel.x}, ${clonedModel.y})`
        });

        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UIInput 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UIInput 特有属性
            value: this.value,
            placeholder: this.placeholder,
            inputType: this.inputType,
            maxLength: this.maxLength,
            readonly: this.readonly,
            fontSize: this.fontSize,
            textColor: this.textColor,
            placeholderColor: this.placeholderColor,
            backgroundColor: this.backgroundColor,
            borderColor: this.borderColor,
            focusBorderColor: this.focusBorderColor,
            borderWidth: this.borderWidth,
            borderRadius: this.borderRadius,
            letterSpacing: this.letterSpacing, // 🔑 新增字符间距
            validationPattern: this.validationPattern,
            validationMessage: this.validationMessage,

            // 🔑 脚本系统
            componentScripts: this.componentScripts
        };
    }

    /**
     * 获取输入框状态信息
     */
    public getInputStatus(): {
        value: string;
        inputType: string;
        maxLength: number;
        readonly: boolean;
        hasValidation: boolean;
    } {
        return {
            value: this.value,
            inputType: this.inputType,
            maxLength: this.maxLength,
            readonly: this.readonly,
            hasValidation: !!this.validationPattern
        };
    }

    /**
     * 设置输入验证规则
     */
    public setValidation(pattern: RegExp | null, message: string = '输入格式不正确') {
        this.validationPattern = pattern;
        this.validationMessage = message;

        // 同步到原始对象
        const originalObject = this.getOriginalObject();
        if (originalObject) {
            originalObject._validationPattern = pattern;
            originalObject._validationMessage = message;
        }
    }

    /**
     * 从JSON反序列化
     */
    static fromJSON(data: any): InputModel {
        return new InputModel(data);
    }
}

// 注册InputModel到基类容器
BaseObjectModel.registerModel('UIInput', InputModel);
BaseObjectModel.registerModel('Input', InputModel);
