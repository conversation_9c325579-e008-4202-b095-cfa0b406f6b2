/**
 * 对象选中拖动工具 - 主工具类
 * 类似Unity的对象选中和拖动功能
 */

import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';
import type {
  ToolConfig,
  ToolEventCallbacks,
  MouseEventInfo,
  RenderContext,
  ArrowType,
  HitTestResult,
  ToolMode
} from './types';

import { SelectionManager } from './SelectionManager';
import { DragController } from './DragController';
import { ScaleController } from './ScaleController';
import { DrawingUtils } from './DrawingUtils';
import { ModelWatcher, type PropertyChanges } from './ModelWatcher.svelte';

export class ObjectSelectionTool {
  private selectionManager: SelectionManager;
  private dragController: DragController;
  private scaleController: ScaleController;
  private config: ToolConfig;
  private callbacks: ToolEventCallbacks;

  private isEnabled: boolean = true;
  private renderContext: RenderContext | null = null;
  private currentMode: ToolMode = 'transform';
  private modelWatcher: ModelWatcher | null = null;

  constructor(config?: Partial<ToolConfig>, callbacks?: ToolEventCallbacks) {
    // 默认配置
    this.config = {
      arrowSize: 8,
      arrowOffset: 25,
      arrowColors: {
        normal: '#2196F3',
        hover: '#1976D2',
        active: '#0D47A1'
      },
      boundingBox: {
        strokeColor: '#2196F3',
        strokeWidth: 2,
        fillColor: 'rgba(33, 150, 243, 0.1)',
        dashPattern: [5, 5]
      },
      dragSensitivity: 1,
      snapToGrid: false,
      gridSize: 10,
      ...config
    };

    this.callbacks = callbacks || {};

    // 初始化管理器
    this.selectionManager = new SelectionManager(this.config, this.callbacks);
    this.dragController = new DragController(this.config, this.callbacks);
    this.scaleController = new ScaleController(this.config, this.callbacks);
  }

  /**
   * 设置渲染上下文
   */
  setRenderContext(context: RenderContext): void {
    this.renderContext = context;

    // 将渲染上下文传递给控制器
    this.dragController.setRenderContext(context);
    this.scaleController.setRenderContext(context);
  }

  /**
   * 设置选中对象
   */
  selectObject(object: BaseObjectModel | null): void {
    if (!this.isEnabled) return;

    // 清理之前的 ModelWatcher
    if (this.modelWatcher) {
      this.modelWatcher.destroy();
      this.modelWatcher = null;
    }

    this.selectionManager.setSelectedObject(object);

    // 如果正在拖动，结束拖动
    if (this.dragController.isDragging() && object) {
      this.dragController.endDrag(object);
    }

    // 为新选中的对象创建 ModelWatcher
    if (object && typeof object === 'object' && object.constructor) {
      this.modelWatcher = new ModelWatcher(
        object,
        this.handleModelPropertyChanges.bind(this),
        {
          enabled: false, // 禁用批量更新，实现实时响应
          delay: 0, // 立即更新
          maxBatchSize: 1
        }
      );

      console.log('🔧 ObjectSelectionTool: 为对象创建 ModelWatcher', {
        className: object.className || object.constructor.name,
        name: object.name || '未命名'
      });
    } else if (object === null) {
      console.log('🔧 ObjectSelectionTool: 清空选择，不创建 ModelWatcher');
    } else {
      console.warn('❌ ObjectSelectionTool: 无效对象，无法创建 ModelWatcher:', object);
    }
  }

  /**
   * 处理模型属性变化的回调
   */
  private handleModelPropertyChanges(changes: PropertyChanges, model: BaseObjectModel): void {
    // 这里可以添加额外的处理逻辑，比如：
    // 1. 更新选中框的位置和大小
    // 2. 触发重绘
    // 3. 通知其他组件

    console.log('🔧 ObjectSelectionTool: 模型属性变化', {
      model: model.className,
      changes: Object.keys(changes)
    });

    // 刷新选中对象信息（更新选中框）
    this.selectionManager.refreshObjectInfo();

    // 通知外部回调
    if (this.callbacks.onModelPropertyChange) {
      this.callbacks.onModelPropertyChange(changes, model);
    }
  }

  /**
   * 获取当前选中对象
   */
  getSelectedObject(): BaseObjectModel | null {
    return this.selectionManager.getSelectedObject();
  }

  /**
   * 设置工具模式
   */
  setToolMode(mode: ToolMode): void {
    if (this.currentMode !== mode) {
      // 如果正在拖动，先结束当前操作
      const selectedObject = this.selectionManager.getSelectedObject();
      if (selectedObject) {
        if (this.dragController.isDragging()) {
          this.dragController.endDrag(selectedObject);
        }
        if (this.scaleController.isScaling()) {
          this.scaleController.endScale(selectedObject);
        }
      }

      this.currentMode = mode;

      // 更新 SelectionManager 的工具模式
      this.selectionManager.setToolMode(mode);

      // 刷新箭头配置
      this.selectionManager.refreshObjectInfo();

      console.log('🔧 切换工具模式:', mode);
    }
  }

  /**
   * 获取当前工具模式
   */
  getToolMode(): ToolMode {
    return this.currentMode;
  }

  /**
   * 处理鼠标按下事件
   */
  onMouseDown(event: MouseEventInfo): boolean {
    if (!this.isEnabled || !this.selectionManager.hasSelection()) {
      return false;
    }

    const selectedObject = this.selectionManager.getSelectedObject()!;
    const hitResult = this.selectionManager.hitTest(event.x, event.y);

    if (hitResult.hit && hitResult.type === 'arrow' && hitResult.arrowType) {
      if (this.currentMode === 'scale') {
        // 开始缩放
        this.scaleController.startScale(
          selectedObject,
          hitResult.arrowType,
          event.x,
          event.y
        );
      } else {
        // 开始拖动
        this.dragController.startDrag(
          selectedObject,
          hitResult.arrowType,
          event.x,
          event.y
        );
      }
      return true;
    }

    return false;
  }

  /**
   * 处理鼠标移动事件
   */
  onMouseMove(event: MouseEventInfo): boolean {
    if (!this.isEnabled) return false;

    let handled = false;

    // 处理拖动或缩放
    if (this.currentMode === 'scale' && this.scaleController.isScaling()) {
      const selectedObject = this.selectionManager.getSelectedObject();
      if (selectedObject) {
        handled = this.scaleController.updateScale(selectedObject, event.x, event.y);
        // 刷新选中对象信息
        this.selectionManager.refreshObjectInfo();
      }
    } else if (this.dragController.isDragging()) {
      const selectedObject = this.selectionManager.getSelectedObject();
      if (selectedObject) {
        handled = this.dragController.updateDrag(selectedObject, event.x, event.y);
        // 刷新选中对象信息
        this.selectionManager.refreshObjectInfo();
      }
    }

    // 处理悬停效果
    if (this.selectionManager.hasSelection()) {
      const hitResult = this.selectionManager.hitTest(event.x, event.y);
      const hoveredArrow = hitResult.hit && hitResult.type === 'arrow' ? hitResult.arrowType : null;
      this.selectionManager.setHoveredArrow(hoveredArrow || null);

      if (hitResult.hit) {
        handled = true;
      }
    }

    return handled;
  }

  /**
   * 处理鼠标释放事件
   */
  onMouseUp(event: MouseEventInfo): boolean {
    if (!this.isEnabled) return false;

    const selectedObject = this.selectionManager.getSelectedObject();
    if (selectedObject) {
      if (this.scaleController.isScaling()) {
        this.scaleController.endScale(selectedObject);
        return true;
      } else if (this.dragController.isDragging()) {
        this.dragController.endDrag(selectedObject);
        return true;
      }
    }

    return false;
  }

  /**
   * 处理键盘事件
   */
  onKeyDown(event: KeyboardEvent): boolean {
    if (!this.isEnabled) return false;

    const selectedObject = this.selectionManager.getSelectedObject();

    // ESC键取消拖动或缩放
    if (event.key === 'Escape') {
      if (selectedObject) {
        if (this.scaleController.isScaling()) {
          this.scaleController.cancelScale(selectedObject);
          this.selectionManager.refreshObjectInfo();
          return true;
        } else if (this.dragController.isDragging()) {
          this.dragController.cancelDrag(selectedObject);
          this.selectionManager.refreshObjectInfo();
          return true;
        }
      }
    }

    // Delete键删除选中对象
    if (event.key === 'Delete' && this.selectionManager.hasSelection()) {
      if (selectedObject) {
        console.log('🗑️ 请求删除对象:', selectedObject.className);
        // 可以添加删除回调
      }
      return true;
    }

    return false;
  }

  /**
   * 渲染选中对象的UI
   */
  render(ctx: CanvasRenderingContext2D): void {
    if (!this.isEnabled || !this.selectionManager.hasSelection()) {
      return;
    }

    const objectInfo = this.selectionManager.getSelectedObjectInfo();
    const arrowConfigs = this.selectionManager.getArrowConfigs();

    if (!objectInfo) return;

    // 绘制选中UI
    DrawingUtils.drawSelectionUI(
      ctx,
      objectInfo,
      arrowConfigs,
      this.config.boundingBox,
      this.selectionManager.getHoveredArrow(),
      this.dragController.getCurrentDragType() || this.scaleController.getCurrentScaleType(),
      this.currentMode
    );

    // 移除文字反馈信息显示
    // if (this.dragController.isDragging() || this.scaleController.isScaling()) {
    //   this.renderOperationFeedback(ctx);
    // }
  }

  // 已删除 renderOperationFeedback 方法 - 不再显示文字反馈信息

  /**
   * 启用/禁用工具
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;

    if (!enabled) {
      // 禁用时清除选择和拖动状态
      const selectedObject = this.selectionManager.getSelectedObject();
      if (selectedObject && this.dragController.isDragging()) {
        this.dragController.endDrag(selectedObject);
      }
    }
  }

  /**
   * 检查工具是否启用
   */
  isToolEnabled(): boolean {
    return this.isEnabled;
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ToolConfig>): void {
    this.config = { ...this.config, ...config };
    this.selectionManager.updateConfig(this.config);
    this.dragController.updateConfig(this.config);
  }

  /**
   * 更新回调
   */
  updateCallbacks(callbacks: Partial<ToolEventCallbacks>): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
    this.selectionManager.updateCallbacks(this.callbacks);
    this.dragController.updateCallbacks(this.callbacks);
  }

  /**
   * 获取当前配置
   */
  getConfig(): ToolConfig {
    return { ...this.config };
  }

  /**
   * 清除选择
   */
  clearSelection(): void {
    this.selectionManager.clearSelection();
  }

  /**
   * 检查是否有选中对象
   */
  hasSelection(): boolean {
    return this.selectionManager.hasSelection();
  }

  /**
   * 检查是否正在拖动
   */
  isDragging(): boolean {
    return this.dragController.isDragging();
  }

  /**
   * 销毁工具
   */
  destroy(): void {
    // 清理 ModelWatcher
    if (this.modelWatcher) {
      this.modelWatcher.destroy();
      this.modelWatcher = null;
    }

    this.clearSelection();
    this.setEnabled(false);
    this.renderContext = null;

    console.log('🔧 ObjectSelectionTool: 工具已销毁');
  }
}
