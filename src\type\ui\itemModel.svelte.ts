import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * UIItem 模型类
 * 事件容器组件，支持数据绑定
 */
export class ItemModel extends BaseObjectModel {
    // 组件类型
    public readonly componentType = 'UIItem';

    // 基础属性
    enabled = $state<boolean>(true);

    // 🔑 数据绑定表达式（参考 UILabel）
    dataBinding = $state<string>('');
    bindingType = $state<string>('none');       // 绑定类型: none/global/parent
    globalDataPath = $state<string>('');        // 全局数据路径
    selectedField = $state<string>('');         // 父级选中字段

    // 🔑 事件代码属性
    onClickCode = $state<string>('');
    onDoubleClickCode = $state<string>('');
    onHoverCode = $state<string>('');
    onPressCode = $state<string>('');
    onReleaseCode = $state<string>('');
    onHoverOutCode = $state<string>('');

    // 🔑 事件执行控制（用于编辑器调试）
    executeEventsInEditor = $state<boolean>(false);

    constructor(originalObject: any) {
        super(originalObject);
        console.log('🎭 ItemModel: 创建事件容器模型', originalObject);

        // 初始化基础属性
        this.enabled = originalObject?.enabled !== false;

        // 🔑 初始化数据绑定表达式
        this.dataBinding = originalObject?.dataBinding || '';
        this.bindingType = originalObject?.bindingType || 'none';
        this.globalDataPath = originalObject?.globalDataPath || '';
        this.selectedField = originalObject?.selectedField || '';

        // 🔑 初始化事件代码
        const eventCodes = originalObject?._eventCodes || {};
        this.onClickCode = eventCodes.onClick || '';
        this.onDoubleClickCode = eventCodes.onDoubleClick || '';
        this.onHoverCode = eventCodes.onHover || '';
        this.onPressCode = eventCodes.onPress || '';
        this.onReleaseCode = eventCodes.onRelease || '';
        this.onHoverOutCode = eventCodes.onHoverOut || '';

        // 🔑 初始化事件执行控制
        this.executeEventsInEditor = originalObject?.executeEventsInEditor || false;

        console.log('✅ ItemModel: 事件容器模型创建完成', {
            enabled: this.enabled,
            dataBinding: this.dataBinding,
            executeEventsInEditor: this.executeEventsInEditor,
            eventCodes: Object.keys(eventCodes).length
        });
    }

    /**
     * 设置特有属性同步（重写基类方法）
     * 🔑 注意：此方法在BaseObjectModel的$effect内部调用，不要再使用$effect
     */
    protected setupSpecificSync(): void {
        // 同步 enabled 属性
        if (this._originalObject.enabled !== this.enabled) {
            this._originalObject.enabled = this.enabled;
        }

        // 🔑 同步数据绑定表达式
        if (this._originalObject.dataBinding !== this.dataBinding) {
            this._originalObject.dataBinding = this.dataBinding;
            // 如果有数据绑定表达式，设置到 UIItem
            if (this.dataBinding && typeof this._originalObject.setDataBinding === 'function') {
                this._originalObject.setDataBinding(this.dataBinding);
            }
        }

        // 🔑 同步其他绑定状态字段
        if (this._originalObject.bindingType !== this.bindingType) {
            this._originalObject.bindingType = this.bindingType;
        }
        if (this._originalObject.globalDataPath !== this.globalDataPath) {
            this._originalObject.globalDataPath = this.globalDataPath;
        }
        if (this._originalObject.selectedField !== this.selectedField) {
            this._originalObject.selectedField = this.selectedField;
        }

        // 🔑 同步事件代码
        if (!this._originalObject._eventCodes) {
            this._originalObject._eventCodes = {};
        }

        const eventCodeMap = {
            onClick: this.onClickCode,
            onDoubleClick: this.onDoubleClickCode,
            onHover: this.onHoverCode,
            onPress: this.onPressCode,
            onRelease: this.onReleaseCode,
            onHoverOut: this.onHoverOutCode
        };

        for (const [eventType, code] of Object.entries(eventCodeMap)) {
            if (this._originalObject._eventCodes[eventType] !== code) {
                this._originalObject._eventCodes[eventType] = code;
                // 同步到 UIItem 的事件代码设置方法
                if (typeof this._originalObject.setEventCode === 'function') {
                    this._originalObject.setEventCode(eventType, code);
                }
            }
        }

        // 🔑 同步事件执行控制
        if (this._originalObject.executeEventsInEditor !== this.executeEventsInEditor) {
            this._originalObject.executeEventsInEditor = this.executeEventsInEditor;
        }
    }

    /**
     * 设置数据绑定表达式
     */
    setDataBinding(expression: string): void {
        console.log('🔗 ItemModel: 设置数据绑定表达式', expression);
        this.dataBinding = expression;
    }

    /**
     * 获取数据绑定表达式
     */
    getDataBinding(): string {
        return this.dataBinding;
    }

    /**
     * 清除数据绑定
     */
    clearDataBinding(): void {
        console.log('🔧 ItemModel: 清除数据绑定');
        this.dataBinding = '';
    }

    /**
     * 设置事件代码
     */
    setEventCode(eventType: string, code: string): void {
        console.log('🔧 ItemModel: 设置事件代码', { eventType, code });

        switch (eventType) {
            case 'onClick':
                this.onClickCode = code;
                break;
            case 'onDoubleClick':
                this.onDoubleClickCode = code;
                break;
            case 'onHover':
                this.onHoverCode = code;
                break;
            case 'onPress':
                this.onPressCode = code;
                break;
            case 'onRelease':
                this.onReleaseCode = code;
                break;
            case 'onHoverOut':
                this.onHoverOutCode = code;
                break;
            default:
                console.warn('ItemModel: 未知的事件类型', eventType);
        }
    }

    /**
     * 获取事件代码
     */
    getEventCode(eventType: string): string {
        switch (eventType) {
            case 'onClick':
                return this.onClickCode;
            case 'onDoubleClick':
                return this.onDoubleClickCode;
            case 'onHover':
                return this.onHoverCode;
            case 'onPress':
                return this.onPressCode;
            case 'onRelease':
                return this.onReleaseCode;
            case 'onHoverOut':
                return this.onHoverOutCode;
            default:
                console.warn('ItemModel: 未知的事件类型', eventType);
                return '';
        }
    }

    /**
     * 获取所有事件代码
     */
    getAllEventCodes(): Record<string, string> {
        return {
            onClick: this.onClickCode,
            onDoubleClick: this.onDoubleClickCode,
            onHover: this.onHoverCode,
            onPress: this.onPressCode,
            onRelease: this.onReleaseCode,
            onHoverOut: this.onHoverOutCode
        };
    }

    /**
     * 切换事件执行模式
     */
    toggleEventExecution(): void {
        this.executeEventsInEditor = !this.executeEventsInEditor;
        console.log('🔧 ItemModel: 切换事件执行模式', this.executeEventsInEditor);
    }

    /**
     * 获取绑定状态信息
     */
    getBindingInfo(): string {
        return this.dataBinding ? `绑定: ${this.dataBinding}` : '未绑定数据';
    }

    /**
     * 克隆当前 UIItem 对象
     */
    clone(): ItemModel {
        console.log('🔄 ItemModel: 开始克隆事件容器对象');

        // 调用原始 UIItem 对象的 clone 方法
        const originalUIItem = this.getOriginalObject();
        if (!originalUIItem || typeof originalUIItem.clone !== 'function') {
            console.error('❌ ItemModel: 原始对象没有 clone 方法');
            throw new Error('UIItem 对象缺少 clone 方法');
        }

        // 使用插件的 clone 方法克隆原始对象
        const clonedUIItem = originalUIItem.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 创建新的 ItemModel 包装克隆的对象
        const clonedModel = new ItemModel(clonedUIItem);

        // 复制属性
        clonedModel.enabled = this.enabled;
        clonedModel.dataBinding = this.dataBinding;
        clonedModel.executeEventsInEditor = this.executeEventsInEditor;

        // 复制事件代码
        clonedModel.onClickCode = this.onClickCode;
        clonedModel.onDoubleClickCode = this.onDoubleClickCode;
        clonedModel.onHoverCode = this.onHoverCode;
        clonedModel.onPressCode = this.onPressCode;
        clonedModel.onReleaseCode = this.onReleaseCode;
        clonedModel.onHoverOutCode = this.onHoverOutCode;

        console.log('✅ ItemModel: 事件容器克隆完成');
        return clonedModel;
    }



    /**
     * 重写获取构造函数参数方法
     */
    protected getConstructorArgs(): any {
        const baseArgs = super.getConstructorArgs();

        return {
            ...baseArgs,
            enabled: this.enabled,
            // 🔑 数据绑定状态
            dataBinding: this.dataBinding,
            bindingType: this.bindingType,
            globalDataPath: this.globalDataPath,
            selectedField: this.selectedField,
            executeEventsInEditor: this.executeEventsInEditor,
            _eventCodes: {
                onClick: this.onClickCode,
                onDoubleClick: this.onDoubleClickCode,
                onHover: this.onHoverCode,
                onPress: this.onPressCode,
                onRelease: this.onReleaseCode,
                onHoverOut: this.onHoverOutCode
            }
        };
    }

    /**
     * 🔑 生成对象创建代码（实现抽象方法）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const args = this.getConstructorArgs();
        const lines: string[] = [];

        // 生成构造函数调用
        lines.push(`${indent}const ${varName} = new UIItem({`);
        lines.push(`${indent}    x: ${args.x},`);
        lines.push(`${indent}    y: ${args.y},`);
        lines.push(`${indent}    width: ${args.width},`);
        lines.push(`${indent}    height: ${args.height},`);
        lines.push(`${indent}    enabled: ${args.enabled},`);

        // 添加数据绑定（如果有）
        if (args.dataBinding) {
            lines.push(`${indent}    dataBinding: "${args.dataBinding}",`);
        }

        // 添加事件执行控制
        lines.push(`${indent}    executeEventsInEditor: ${args.executeEventsInEditor},`);

        // 添加事件代码
        if (args._eventCodes && Object.values(args._eventCodes).some((code: any) => code && typeof code === 'string' && code.trim())) {
            lines.push(`${indent}    _eventCodes: {`);
            Object.entries(args._eventCodes).forEach(([eventName, code]) => {
                if (code && typeof code === 'string' && code.trim()) {
                    // 转义代码中的引号和换行符
                    const escapedCode = code.replace(/\\/g, '\\\\').replace(/"/g, '\\"').replace(/\n/g, '\\n');
                    lines.push(`${indent}        ${eventName}: "${escapedCode}",`);
                }
            });
            lines.push(`${indent}    }`);
        }

        lines.push(`${indent}});`);

        return lines.join('\n');
    }

    /**
     * 🔑 生成特定属性设置代码
     * @param _varName 变量名
     * @param _indent 缩进字符串
     * @returns 特定属性设置代码
     */
    protected generateSpecificProperties(_varName: string, _indent: string): string {
        const codes: string[] = [];

        // 🔑 不在这里生成绑定代码，因为子对象还没创建
        // 绑定代码将在 generateBindingCode() 中生成

        // 如果有其他特殊的 UIItem 配置，可以在这里添加
        // 目前 UIItem 的主要属性都在构造函数中设置了

        return codes.join('\n');
    }

    /**
     * 🔑 生成数据绑定代码（在子对象创建之后）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 绑定代码
     */
    protected generateBindingCode(varName: string, indent: string): string {
        const codes: string[] = [];

        // 如果有数据绑定表达式，生成绑定代码
        // if (this.dataBinding && this.dataBinding.trim()) {
        //     codes.push(`${indent}// 设置数据绑定`);
        //     codes.push(`${indent}${varName}.setDataBinding("${this.dataBinding}");`);
        // }

        return codes.join('\n');
    }

    /**
     * 🔑 重写代码生成方法，确保正确的生成顺序
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 生成的代码字符串
     */
    public generateCreationCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        // 1. 对象创建代码
        codes.push(this.generateObjectCreation(varName, indent));

        // 2. 基础属性设置
        codes.push(this.generateBasicProperties(varName, indent));

        // 3. 特定属性设置（不包含绑定）
        codes.push(this.generateSpecificProperties(varName, indent));

        // 4. 子对象代码生成
        if (this.generateChildrenCode) {
            codes.push(this.generateChildrenCreation(varName, indent));
        }

        // 5. 🔑 绑定代码（在子对象创建之后）
        codes.push(this.generateBindingCode(varName, indent));

        return codes.filter(code => code.trim()).join('\n');
    }

    /**
     * 从JSON反序列化
     */
    static fromJSON(data: any): ItemModel {
        return new ItemModel(data);
    }


}

// 注册 ItemModel 到基类容器
BaseObjectModel.registerModel('UIItem', ItemModel);
BaseObjectModel.registerModel('Item', ItemModel);

// 导出到全局，以便在 ListModel 中使用
if (typeof window !== 'undefined') {
    (window as any).ItemModel = ItemModel;
}
