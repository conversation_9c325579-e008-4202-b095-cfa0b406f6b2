<script lang="ts">
  /**
   * 父级数据选择模态框
   * 专门用于显示和选择父级对象的字段
   */
  import { modalReactive, ModalManager } from '../stores/modalStore.svelte';

  // 响应式状态
  let parentDataState = $derived(modalReactive.parentData);

  // 获取模态框属性
  let modalProps = $derived(ModalManager.getParentDataProps());

  // 选择的字段
  let selectedField = $state('');

  // 🔑 根据父级路径获取数据并提取字段
  let parentData = $derived(getDataFromPath(modalProps.parentPath));
  let fieldsList = $derived(parentData ? extractFields(parentData) : []);

  // 🔑 根据路径获取数据对象
  function getDataFromPath(path: string | undefined): any {
    if (!path) {
      console.log('🔧 ParentDataModal: 没有父级路径');
      return null;
    }

    try {
      console.log('🔧 ParentDataModal: 根据路径获取数据', path);

      // 使用 eval 获取数据对象
      const dataObject = eval(path);

      console.log('🔧 ParentDataModal: 获取到数据对象', {
        path,
        dataObject,
        type: typeof dataObject,
        isArray: Array.isArray(dataObject)
      });

      return dataObject;
    } catch (error) {
      console.error('ParentDataModal: 根据路径获取数据失败', error);
      return null;
    }
  }

  // 处理父级数据模态框的确认
  function handleConfirm() {
    if (selectedField && modalProps.parentPath) {
      // 🔑 构建完整的绑定路径
      const fullPath = buildFullPath(modalProps.parentPath, selectedField);

      console.log('🔧 ParentDataModal: 选择字段', {
        selectedField,
        parentPath: modalProps.parentPath,
        fullPath
      });

      ModalManager.confirmParentData(fullPath);
    }
  }

  // 🔑 构建完整路径
  function buildFullPath(parentPath: string, fieldName: string): string {
    // 如果字段名是数组索引格式 [0], [1] 等
    if (fieldName.startsWith('[') && fieldName.endsWith(']')) {
      return parentPath + fieldName; // 直接拼接数组索引
    } else {
      // 如果是对象属性，添加点号
      return parentPath + '.' + fieldName;
    }
  }

  // 处理取消
  function handleCancel() {
    console.log('🔧 ParentDataModal: 取消选择');
    ModalManager.cancelParentData();
  }

  // 选择字段
  function selectField(fieldName: string) {
    selectedField = fieldName;
    console.log('🔧 ParentDataModal: 选择字段', fieldName);
  }

  // 双击确认选择
  function handleDoubleClick(fieldName: string) {
    selectedField = fieldName;
    handleConfirm();
  }

  // 提取对象的所有字段
  function extractFields(obj: any): Array<{name: string, type: string, value: any}> {
    console.log('🔧 ParentDataModal: extractFields 被调用', {
      obj,
      isArray: Array.isArray(obj),
      type: typeof obj,
      keys: Object.keys(obj || {}).slice(0, 10)
    });

    if (!obj || typeof obj !== 'object') {
      return [];
    }

    const fields = [];

    // 🔑 如果是数组，显示数组元素
    if (Array.isArray(obj)) {
      console.log('🔧 ParentDataModal: 处理数组数据', obj.length);

      obj.forEach((item, index) => {
        if (item !== null && item !== undefined) {
          const valueType = Array.isArray(item) ? 'array' : typeof item;

          // 对于数组元素，显示索引和类型
          fields.push({
            name: `[${index}]`,
            type: valueType,
            value: item
          });
        }
      });
    } else {
      // 🔑 如果是对象，显示对象属性
      console.log('🔧 ParentDataModal: 处理对象数据');

      for (const [key, value] of Object.entries(obj)) {
        // 🔑 只跳过函数，保留以 _ 开头的字段（RPG Maker MZ 中很多重要字段都以 _ 开头）
        if (typeof value === 'function') {
          continue;
        }

        const valueType = Array.isArray(value) ? 'array' : typeof value;

        // 显示所有类型的字段（包括对象和数组，让用户可以进一步选择）
        fields.push({
          name: key,
          type: valueType,
          value: value
        });
      }
    }

    console.log('🔧 ParentDataModal: 提取的字段', fields);
    return fields.sort((a, b) => a.name.localeCompare(b.name));
  }

  // 获取字段值的显示文本
  function getFieldValueDisplay(field: any): string {
    if (field.value === null) return 'null';
    if (field.value === undefined) return 'undefined';

    if (typeof field.value === 'string') {
      return `"${field.value.length > 20 ? field.value.substring(0, 20) + '...' : field.value}"`;
    }

    if (typeof field.value === 'number' || typeof field.value === 'boolean') {
      return String(field.value);
    }

    if (Array.isArray(field.value)) {
      return `Array(${field.value.length})`;
    }

    if (typeof field.value === 'object') {
      const keys = Object.keys(field.value);
      return `Object{${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}}`;
    }

    return String(field.value);
  }

  // 获取字段类型的颜色
  function getFieldTypeColor(type: string): string {
    switch (type) {
      case 'string': return '#10b981'; // green
      case 'number': return '#3b82f6'; // blue
      case 'boolean': return '#f59e0b'; // amber
      case 'object': return '#8b5cf6'; // purple
      case 'array': return '#ef4444'; // red
      case 'undefined': return '#6b7280'; // gray
      case 'null': return '#6b7280'; // gray
      default: return '#6b7280'; // gray
    }
  }
</script>

<!-- 父级数据选择模态框 -->
{#if parentDataState.isOpen}
  <div class="modal-overlay" onclick={handleCancel}>
    <div class="modal-container" onclick={(e) => e.stopPropagation()}>
      <!-- 模态框头部 -->
      <div class="modal-header">
        <h3 class="modal-title">选择父级数据字段</h3>
        <button class="close-button" onclick={handleCancel}>×</button>
      </div>

      <!-- 模态框内容 -->
      <div class="modal-content">
        {#if fieldsList.length > 0}
          <div class="fields-container">
            <div class="fields-header">
              <span>字段名</span>
              <span>类型</span>
              <span>当前值</span>
            </div>
            
            <div class="fields-list">
              {#each fieldsList as field}
                <div 
                  class="field-item" 
                  class:selected={selectedField === field.name}
                  onclick={() => selectField(field.name)}
                  ondblclick={() => handleDoubleClick(field.name)}
                >
                  <span class="field-name">{field.name}</span>
                  <span class="field-type" style="color: {getFieldTypeColor(field.type)}">
                    {field.type}
                  </span>
                  <span class="field-value">{getFieldValueDisplay(field)}</span>
                </div>
              {/each}
            </div>
          </div>
        {:else}
          <div class="no-data">
            <p>没有可用的父级数据字段</p>
            <p class="hint">父级对象没有可绑定的基础类型字段</p>
          </div>
        {/if}
      </div>

      <!-- 模态框底部 -->
      <div class="modal-footer">
        <button class="cancel-button" onclick={handleCancel}>取消</button>
        <button 
          class="confirm-button" 
          disabled={!selectedField}
          onclick={handleConfirm}
        >
          确认选择
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-container {
    background: var(--theme-background, #1f2937);
    border-radius: 8px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    color: var(--theme-text, #f9fafb);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--theme-border, #374151);
  }

  .modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--theme-text-secondary, #9ca3af);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-button:hover {
    color: var(--theme-text, #f9fafb);
  }

  .modal-content {
    flex: 1;
    overflow: hidden;
    padding: 20px;
  }

  .fields-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .fields-header {
    display: grid;
    grid-template-columns: 2fr 1fr 2fr;
    gap: 12px;
    padding: 8px 12px;
    background: var(--theme-surface, #374151);
    border-radius: 4px;
    font-weight: 600;
    font-size: 12px;
    color: var(--theme-text-secondary, #9ca3af);
    margin-bottom: 8px;
  }

  .fields-list {
    flex: 1;
    overflow-y: auto;
    border: 1px solid var(--theme-border, #374151);
    border-radius: 4px;
  }

  .field-item {
    display: grid;
    grid-template-columns: 2fr 1fr 2fr;
    gap: 12px;
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid var(--theme-border, #374151);
    transition: background-color 0.2s;
  }

  .field-item:hover {
    background: var(--theme-surface, #374151);
  }

  .field-item.selected {
    background: var(--theme-primary, #3b82f6);
    color: white;
  }

  .field-item:last-child {
    border-bottom: none;
  }

  .field-name {
    font-weight: 500;
    font-family: monospace;
  }

  .field-type {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
  }

  .field-value {
    font-family: monospace;
    font-size: 12px;
    color: var(--theme-text-secondary, #9ca3af);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .field-item.selected .field-value {
    color: rgba(255, 255, 255, 0.8);
  }

  .no-data {
    text-align: center;
    padding: 40px 20px;
    color: var(--theme-text-secondary, #9ca3af);
  }

  .no-data p {
    margin: 8px 0;
  }

  .hint {
    font-size: 12px;
    font-style: italic;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid var(--theme-border, #374151);
  }

  .cancel-button, .confirm-button {
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .cancel-button {
    background: var(--theme-surface, #374151);
    color: var(--theme-text, #f9fafb);
  }

  .cancel-button:hover {
    background: var(--theme-border, #4b5563);
  }

  .confirm-button {
    background: var(--theme-primary, #3b82f6);
    color: white;
  }

  .confirm-button:hover:not(:disabled) {
    background: var(--theme-primary-dark, #2563eb);
  }

  .confirm-button:disabled {
    background: var(--theme-surface, #374151);
    color: var(--theme-text-secondary, #9ca3af);
    cursor: not-allowed;
  }
</style>
