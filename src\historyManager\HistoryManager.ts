/**
 * 历史记录管理器
 * 支持撤销/重做功能，保存模型对象、变化字段和历史值
 */

import type { BaseObjectModel } from '../type/baseObjectModel.svelte';
import type {
  HistoryEntry,
  OperationGroup,
  HistoryConfig,
  HistoryEvent,
  HistoryListener
} from './types';

export class HistoryManager {
  private undoStack: OperationGroup[] = [];
  private redoStack: OperationGroup[] = [];
  private currentGroup: OperationGroup | null = null;
  private listeners: HistoryListener[] = [];



  private config: HistoryConfig = {
    maxHistorySize: 100,
    enabled: true,
    autoRecord: true,
    debug: false
  };

  constructor(config?: Partial<HistoryConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }

    if (this.config.debug) {
      console.log('🕰️ HistoryManager: 初始化完成', this.config);
    }
  }

  /**
   * 配置历史记录管理器
   */
  configure(config: Partial<HistoryConfig>): void {
    this.config = { ...this.config, ...config };
    if (this.config.debug) {
      console.log('🕰️ HistoryManager: 配置更新', this.config);
    }
  }

  /**
   * 开始一个操作组
   */
  startGroup(name: string, description?: string): void {
    if (!this.config.enabled) return;

    if (this.currentGroup) {
      console.warn('🕰️ HistoryManager: 已有活动的操作组，自动结束前一个');
      this.endGroup();
    }

    this.currentGroup = {
      name,
      description,
      timestamp: Date.now(),
      entries: []
    };

    if (this.config.debug) {
      console.log('🕰️ HistoryManager: 开始操作组', name);
    }
  }

  /**
   * 记录字段变更
   */
  recordChange(
    modelObject: BaseObjectModel,
    fieldName: string,
    oldValue: any,
    newValue: any
  ): void {
    if (!this.config.enabled || !this.config.autoRecord) return;

    // 检查对象是否有效
    if (!this.isObjectValid(modelObject)) {
      console.warn('🕰️ HistoryManager: 尝试记录无效对象的变更');
      return;
    }

    // 直接记录变更
    this.recordChangeImmediate(modelObject, fieldName, oldValue, newValue);
  }

  /**
   * 立即记录变更（内部方法）
   */
  private recordChangeImmediate(
    modelObject: BaseObjectModel,
    fieldName: string,
    oldValue: any,
    newValue: any
  ): void {
    const entry: HistoryEntry = {
      timestamp: Date.now(),
      modelObject,
      fieldName,
      oldValue,
      newValue,
      objectId: this.getObjectId(modelObject)
    };

    if (this.currentGroup) {
      // 添加到当前操作组
      this.currentGroup.entries.push(entry);
    } else {
      // 创建单独的操作组
      const singleGroup: OperationGroup = {
        name: `修改${modelObject.className}.${fieldName}`,
        timestamp: Date.now(),
        entries: [entry]
      };
      this.pushToUndoStack(singleGroup);
    }

    if (this.config.debug) {
      console.log('🕰️ HistoryManager: 记录变更', {
        object: modelObject.className,
        field: fieldName,
        oldValue,
        newValue
      });
    }
  }



  /**
   * 结束当前操作组
   */
  endGroup(): void {
    if (!this.config.enabled) return;

    if (this.currentGroup && this.currentGroup.entries.length > 0) {
      this.pushToUndoStack(this.currentGroup);

      if (this.config.debug) {
        console.log('🕰️ HistoryManager: 结束操作组', {
          name: this.currentGroup.name,
          entries: this.currentGroup.entries.length
        });
      }
    }

    this.currentGroup = null;
  }

  /**
   * 撤销操作
   */
  undo(): boolean {
    if (!this.config.enabled || this.undoStack.length === 0) {
      return false;
    }

    // 如果有未完成的操作组，先结束它
    if (this.currentGroup) {
      this.endGroup();
    }

    const operation = this.undoStack.pop();
    if (!operation) return false;

    // 逆序执行撤销（后修改的先撤销）
    const validEntries = operation.entries.filter(entry =>
      this.isObjectValid(entry.modelObject)
    );

    if (validEntries.length === 0) {
      console.warn('🕰️ HistoryManager: 操作中的所有对象都已无效');
      return false;
    }

    // 临时禁用自动记录，避免撤销操作被记录
    const wasAutoRecord = this.config.autoRecord;
    this.config.autoRecord = false;

    try {
      validEntries.reverse().forEach(entry => {
        // 🔑 特殊处理删除恢复操作
        if (entry.fieldName === '_deleteRestore') {
          this.handleDeleteRestore(entry);
        } else {
          (entry.modelObject as any)[entry.fieldName] = entry.oldValue;
        }
      });

      // 添加到重做栈
      this.redoStack.push(operation);

      // 限制重做栈大小
      if (this.redoStack.length > this.config.maxHistorySize) {
        this.redoStack.shift();
      }

      this.notifyListeners({
        type: 'undo',
        operation,
        timestamp: Date.now()
      });

      if (this.config.debug) {
        console.log('🕰️ HistoryManager: 撤销操作', operation.name);
      }

      return true;
    } finally {
      // 恢复自动记录设置
      this.config.autoRecord = wasAutoRecord;
    }
  }

  /**
   * 重做操作
   */
  redo(): boolean {
    if (!this.config.enabled || this.redoStack.length === 0) {
      return false;
    }

    const operation = this.redoStack.pop();
    if (!operation) return false;

    // 检查对象有效性
    const validEntries = operation.entries.filter(entry =>
      this.isObjectValid(entry.modelObject)
    );

    if (validEntries.length === 0) {
      console.warn('🕰️ HistoryManager: 重做操作中的所有对象都已无效');
      return false;
    }

    // 临时禁用自动记录
    const wasAutoRecord = this.config.autoRecord;
    this.config.autoRecord = false;

    try {
      validEntries.forEach(entry => {
        (entry.modelObject as any)[entry.fieldName] = entry.newValue;
      });

      // 添加回撤销栈
      this.undoStack.push(operation);

      this.notifyListeners({
        type: 'redo',
        operation,
        timestamp: Date.now()
      });

      if (this.config.debug) {
        console.log('🕰️ HistoryManager: 重做操作', operation.name);
      }

      return true;
    } finally {
      // 恢复自动记录设置
      this.config.autoRecord = wasAutoRecord;
    }
  }

  /**
   * 清空历史记录
   */
  clear(): void {
    this.undoStack = [];
    this.redoStack = [];
    this.currentGroup = null;

    this.notifyListeners({
      type: 'clear',
      timestamp: Date.now()
    });

    if (this.config.debug) {
      console.log('🕰️ HistoryManager: 清空历史记录');
    }
  }

  /**
   * 获取历史记录状态
   */
  getState() {
    return {
      canUndo: this.undoStack.length > 0,
      canRedo: this.redoStack.length > 0,
      undoCount: this.undoStack.length,
      redoCount: this.redoStack.length,
      currentGroup: this.currentGroup?.name || null,
      lastOperation: this.undoStack[this.undoStack.length - 1]?.name || null
    };
  }

  /**
   * 添加事件监听器
   */
  addListener(listener: HistoryListener): void {
    this.listeners.push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeListener(listener: HistoryListener): void {
    const index = this.listeners.indexOf(listener);
    if (index !== -1) {
      this.listeners.splice(index, 1);
    }
  }



  /**
   * 推送到撤销栈
   */
  private pushToUndoStack(operation: OperationGroup): void {
    this.undoStack.push(operation);

    // 限制栈大小
    if (this.undoStack.length > this.config.maxHistorySize) {
      this.undoStack.shift();
    }

    // 清空重做栈（新操作会使重做无效）
    this.redoStack = [];

    this.notifyListeners({
      type: 'record',
      operation,
      timestamp: Date.now()
    });
  }

  /**
   * 检查对象是否有效
   */
  private isObjectValid(obj: BaseObjectModel): boolean {
    // 简单检查：对象存在且有className属性
    return obj && typeof obj === 'object' && 'className' in obj;
  }

  /**
   * 处理删除恢复操作
   */
  private handleDeleteRestore(entry: HistoryEntry): void {
    try {
      const restoreData = entry.newValue; // 撤销时使用 newValue（删除时保存的数据）
      if (!restoreData || !restoreData.deletedObject) {
        console.error('❌ HistoryManager: 删除恢复数据无效');
        return;
      }

      console.log('🔄 HistoryManager: 开始删除恢复操作', {
        className: restoreData.deletedObject.className,
        parentExists: !!restoreData.parentObject,
        insertIndex: restoreData.insertIndex
      });

      // 1. 从快照恢复对象
      const restoredObject = (window as any).BaseObjectModel?.restoreFromSnapshot(restoreData.deletedObject);
      if (!restoredObject) {
        console.error('❌ HistoryManager: 无法从快照恢复对象');
        return;
      }

      // 2. 将恢复的对象添加到父对象中
      const parentObject = restoreData.parentObject;
      if (parentObject && parentObject.children) {
        const insertIndex = restoreData.insertIndex;

        // 暂时禁用历史记录，避免恢复操作被记录
        const wasRecording = this.isRecording();
        if (wasRecording) this.pauseRecording();

        try {
          // 插入到指定位置
          if (insertIndex >= 0 && insertIndex <= parentObject.children.length) {
            parentObject.children.splice(insertIndex, 0, restoredObject);
          } else {
            parentObject.children.push(restoredObject);
          }

          // 设置父子关系
          restoredObject.parent = parentObject;

          // 添加到原始 PIXI 对象
          const parentOriginal = parentObject.getOriginalObject();
          const restoredOriginal = restoredObject.getOriginalObject();
          if (parentOriginal && restoredOriginal && parentOriginal.addChild) {
            // 确保对象不会重复添加
            if (restoredOriginal.parent !== parentOriginal) {
              parentOriginal.addChild(restoredOriginal);
              console.log('🔄 HistoryManager: 已将恢复对象添加到原始父对象');
            }
          }

          // 🔑 延迟一帧确保PIXI渲染系统正确处理新的对象结构
          setTimeout(() => {
            console.log('🔄 HistoryManager: 延迟渲染刷新完成');
          }, 16); // 一帧的时间

          console.log('✅ HistoryManager: 删除恢复操作完成');

        } finally {
          if (wasRecording) this.resumeRecording();
        }
      } else {
        console.error('❌ HistoryManager: 无法找到父对象进行恢复');
      }

    } catch (error) {
      console.error('❌ HistoryManager: 删除恢复操作失败', error);
    }
  }

  /**
   * 获取对象ID
   */
  private getObjectId(obj: BaseObjectModel): string {
    // 使用对象的className和一些属性生成ID
    // 这里可以根据实际需求改进
    return `${obj.className}_${obj.x}_${obj.y}_${Date.now()}`;
  }

  /**
   * 通知监听器
   */
  private notifyListeners(event: HistoryEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('🕰️ HistoryManager: 监听器执行错误', error);
      }
    });
  }

  /**
   * 是否正在记录
   */
  isRecording(): boolean {
    return this.config.enabled && this.config.autoRecord;
  }

  /**
   * 暂停记录
   */
  pauseRecording(): void {
    this.config.autoRecord = false;
  }

  /**
   * 恢复记录
   */
  resumeRecording(): void {
    this.config.autoRecord = true;
  }


}
