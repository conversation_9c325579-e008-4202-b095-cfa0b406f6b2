/**
 * UILayout - 通用布局管理器
 * 支持垂直、水平、网格等多种布局方式
 * 继承自PIXI.Container，专注于子元素的位置管理
 */

(() => {
    'use strict';

    // 确保 PIXI 可用
    if (typeof PIXI === 'undefined') {
        console.error('UILayout: PIXI 未找到');
        return;
    }

    /**
     * UILayout - 通用布局容器
     */
    class UILayout extends PIXI.Container {
        constructor(properties = {}) {
            super();

            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'UILayout';

            // 🔑 使用UIComponentUtils添加UIComponent功能
            if (window.UIComponentUtils) {
                window.UIComponentUtils.applyToSprite(this, properties);
            }

            // 🔑 使用UIScriptManager添加脚本功能
            if (window.UIScriptManager) {
                window.UIScriptManager.applyToObject(this, properties);
                console.log('🔧 UILayout: 脚本系统初始化完成', {
                    hasComponentScripts: !!this.componentScripts,
                    scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                    scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                });
            }





            // 布局类型：'vertical', 'horizontal', 'grid'
            this.layoutType = properties.layoutType || 'vertical';

            // 基础布局参数
            this.spacing = properties.spacing || 5;              // 项目间距
            this.padding = properties.padding || 0;              // 内边距
            this.horizontalSpacing = properties.horizontalSpacing || this.spacing; // 水平间距
            this.verticalSpacing = properties.verticalSpacing || this.spacing;     // 垂直间距

            // 网格布局参数
            this.columns = properties.columns || 2;              // 网格列数
            this.rows = properties.rows || 0;                    // 网格行数（0表示自动）

            // 对齐方式
            this.mainAxisAlignment = properties.mainAxisAlignment || 'start';     // 主轴对齐：start, center, end, space-between, space-around
            this.crossAxisAlignment = properties.crossAxisAlignment || 'start';   // 交叉轴对齐：start, center, end, stretch

            // 🔑 容器尺寸直接使用Layout对象自身的宽高，确保不为负数
            this.containerWidth = Math.max(0, this.width || 0);
            this.containerHeight = Math.max(0, this.height || 0);

            // 自动更新布局
            this.autoUpdate = properties.autoUpdate !== false;

            // 布局状态
            this._needsLayout = true;
            this._isUpdatingLayout = false;

            // 🔑 模板绑定属性
            this.templateData = properties.templateData || [];      // 模板数据数组
            this.generatedItems = [];                               // 生成的列表项
            this.isTemplateMode = properties.isTemplateMode || false; // 是否启用模板模式
            this._cachedTemplate = null;                            // 缓存的模板对象

            // 🔑 滚动相关属性
            this.scrollEnabled = properties.scrollEnabled !== false; // 是否启用滚动
            this.scrollTop = 0;                                     // 当前滚动位置
            this.maxScrollTop = 0;                                  // 最大滚动位置
            this.scrollSpeed = properties.scrollSpeed || 40;        // 滚动速度
            this._contentContainer = null;                          // 内容容器


            // 🔑 初始化滚动功能
            this.initializeScrolling();

            // 标记为已创建并执行onStart
            this._isCreated = true;
            if (this.executeScript) {
                this.executeScript('onStart');
            }
        }


        /**
         * 添加子元素并更新布局
         */
        addChild(child) {
            const result = super.addChild(child);
            
            if (this.autoUpdate) {
                this.requestLayoutUpdate();
            }
            return result;
        }

        /**
         * 移除子元素并更新布局
         */
        removeChild(child) {
            try {
                // 🔧 安全检查：确保子对象存在且有效
                if (!child) {
                    console.warn('🚨 UILayout: 尝试移除null/undefined子对象');
                    return null;
                }

                // 🔧 检查子对象是否真的是这个容器的子对象
                if (child.parent !== this) {
                    console.warn('🚨 UILayout: 子对象的父容器不是当前Layout', {
                        childParent: child.parent,
                        currentLayout: this
                    });
                    return null;
                }

                // 🔧 安全移除
                const result = super.removeChild(child);

                // 🔧 确保子对象的transform等属性仍然有效
                if (child.transform) {
                    console.log('✅ UILayout: 子对象安全移除', child.constructor.name);
                } else {
                    console.warn('🚨 UILayout: 子对象移除后transform为null', child.constructor.name);
                }

                if (this.autoUpdate) {
                    this.requestLayoutUpdate();
                }
                return result;
            } catch (error) {
                console.error('🚨 UILayout: 移除子对象时发生错误', error, child);
                return null;
            }
        }

        /**
         * 请求布局更新（异步）
         */
        requestLayoutUpdate() {
            this._needsLayout = true;
        }

        /**
         * 立即更新布局
         */
        updateLayout() {
            if (this._isUpdatingLayout) return;
            this._isUpdatingLayout = true;
            this._needsLayout = false;

            // 🔑 在布局更新前同步容器尺寸
            this.syncContainerSize();

            try {
                switch (this.layoutType) {
                    case 'vertical':
                        this.updateVerticalLayout();
                        break;
                    case 'horizontal':
                        this.updateHorizontalLayout();
                        break;
                    case 'grid':
                        this.updateGridLayout();
                        break;
                    default:
                        console.warn('UILayout: 未知的布局类型', this.layoutType);
                        this.updateVerticalLayout(); // 默认使用垂直布局
                }

                // 🔑 布局更新完成后，更新滚动范围
                this.updateScrollRange();

            } catch (error) {
                console.error('UILayout: 布局更新失败', error);
            } finally {
                this._isUpdatingLayout = false;
            }
        }

        /**
         * 垂直布局
         */
        updateVerticalLayout() {
            let currentY = this.padding;
            const containerCenterX = this.containerWidth / 2;
            this.children.forEach((child, index) => {
                if (!child.visible) return;
                // 设置Y位置
                child.y = currentY;

                // 设置X位置（根据对齐方式）
                switch (this.crossAxisAlignment) {
                    case 'center':
                        child.x = containerCenterX - (child.width || 0) / 2;
                        break;
                    case 'end':
                        child.x = this.containerWidth - (child.width || 0) - this.padding;
                        break;
                    case 'start':
                    default:
                        child.x = this.padding;
                        break;
                }

                // 更新下一个元素的Y位置
                const childHeight = child.height || 0;
                currentY += childHeight + this.verticalSpacing;


            });

            // 🔑 计算内容的实际高度，但不修改容器高度
            this._contentHeight = currentY - this.verticalSpacing + this.padding;

            // 🔑 只有在容器高度为0且Layout对象高度也为0时，才自动设置高度
            if (this.containerHeight === 0 && (this.height === 0 || this.height === undefined)) {
                this.height = this._contentHeight;
                this.containerHeight = this._contentHeight;
                console.log('🔄 UILayout: 自动设置容器高度', this.containerHeight);
            }
        }

        /**
         * 水平布局
         */
        updateHorizontalLayout() {
            let currentX = this.padding;
            const containerCenterY = this.containerHeight / 2;

            this.children.forEach((child, index) => {
                if (!child.visible) return;

                // 设置X位置
                child.x = currentX;

                // 设置Y位置（根据对齐方式）
                switch (this.crossAxisAlignment) {
                    case 'center':
                        child.y = containerCenterY - (child.height || 0) / 2;
                        break;
                    case 'end':
                        child.y = this.containerHeight - (child.height || 0) - this.padding;
                        break;
                    case 'start':
                    default:
                        child.y = this.padding;
                        break;
                }

                // 更新下一个元素的X位置
                currentX += (child.width || 0) + this.horizontalSpacing;
            });

            // 🔑 计算内容的实际宽度，但不修改容器宽度
            this._contentWidth = currentX - this.horizontalSpacing + this.padding;

            // 🔑 只有在容器宽度为0且Layout对象宽度也为0时，才自动设置宽度
            if (this.containerWidth === 0 && (this.width === 0 || this.width === undefined)) {
                this.width = this._contentWidth;
                this.containerWidth = this._contentWidth;
                console.log('🔄 UILayout: 自动设置容器宽度', this.containerWidth);
            }
        }

        /**
         * 网格布局
         */
        updateGridLayout() {
            const visibleChildren = this.children.filter(child => child.visible);
            
            visibleChildren.forEach((child, index) => {
                const row = Math.floor(index / this.columns);
                const col = index % this.columns;

                // 计算位置
                child.x = this.padding + col * ((child.width || 0) + this.horizontalSpacing);
                child.y = this.padding + row * ((child.height || 0) + this.verticalSpacing);
            });

            // 自动计算容器尺寸
            if (visibleChildren.length > 0) {
                const totalRows = Math.ceil(visibleChildren.length / this.columns);
                const maxChildWidth = Math.max(...visibleChildren.map(child => child.width || 0));
                const maxChildHeight = Math.max(...visibleChildren.map(child => child.height || 0));

                if (this.containerWidth === 0) {
                    this.containerWidth = this.padding * 2 + 
                        this.columns * maxChildWidth + 
                        (this.columns - 1) * this.horizontalSpacing;
                }

                if (this.containerHeight === 0) {
                    this.containerHeight = this.padding * 2 + 
                        totalRows * maxChildHeight + 
                        (totalRows - 1) * this.verticalSpacing;
                }
            }
        }

        /**
         * 设置布局类型
         */
        setLayoutType(layoutType) {
            if (this.layoutType !== layoutType) {

                this.layoutType = layoutType;
                this.requestLayoutUpdate();
            }
        }

        /**
         * 设置间距
         */
        setSpacing(spacing) {
            this.spacing = spacing;
            this.horizontalSpacing = spacing;
            this.verticalSpacing = spacing;
            this.requestLayoutUpdate();
        }

        /**
         * 设置容器尺寸
         */
        setContainerSize(width, height) {
            // 🔑 同时更新Layout对象自身的尺寸和容器尺寸，确保不为负数
            if (width !== undefined) {
                this.width = Math.max(0, width);
                this.containerWidth = Math.max(0, width);
            }
            if (height !== undefined) {
                this.height = Math.max(0, height);
                this.containerHeight = Math.max(0, height);
            }
            this.requestLayoutUpdate();
        }

        /**
         * 获取计算后的容器尺寸
         */
        getCalculatedSize() {
            return {
                width: this.containerWidth,
                height: this.containerHeight
            };
        }

        /**
         * 🔑 同步容器尺寸（当Layout对象尺寸改变时调用）
         */
        syncContainerSize() {
            const oldWidth = this.containerWidth;
            const oldHeight = this.containerHeight;

            // 🔧 确保尺寸不为负数
            this.containerWidth = Math.max(0, this.width || 0);
            this.containerHeight = Math.max(0, this.height || 0);

            // 如果尺寸发生变化，请求布局更新
            if (oldWidth !== this.containerWidth || oldHeight !== this.containerHeight) {
                console.log('🔄 UILayout: 容器尺寸同步', {
                    oldSize: `${oldWidth}x${oldHeight}`,
                    newSize: `${this.containerWidth}x${this.containerHeight}`,
                    originalSize: `${this.width}x${this.height}`
                });
                this.requestLayoutUpdate();
            }
        }











        /**
         * 🔑 获取或缓存模板对象（自动使用第一个子元素）
         */
        _getTemplate() {
            // 如果已经有缓存的模板，直接返回
            if (this._cachedTemplate) {
                return this._cachedTemplate;
            }

            // 如果有子元素，使用第一个作为模板
            if (this.children && this.children.length > 0) {
                const firstChild = this.children[0];

                // 克隆第一个子元素作为模板
                if (typeof firstChild.clone === 'function') {
                    this._cachedTemplate = firstChild.clone({ offsetPosition: false });
                    this._cachedTemplate._isTemplate = true;

                    console.log('🔗 UILayout: 缓存第一个子元素作为模板', {
                        templateType: this._cachedTemplate.uiComponentType,
                        templateId: this._cachedTemplate.componentId,
                        originalChildType: firstChild.uiComponentType
                    });

                    return this._cachedTemplate;
                } else {
                    console.warn('⚠️ UILayout: 第一个子元素没有 clone 方法');
                }
            }
            console.warn('⚠️ UILayout: 没有可用的子元素作为模板');
            return null;
        }

        /**
         * 🔑 初始化滚动功能
         */
        initializeScrolling() {
            if (!this.scrollEnabled) return;

            console.log('🖱️ UILayout: 初始化滚动功能');

            // 🔑 不创建额外容器，直接使用 UILayout 自身作为滚动容器
            this._contentContainer = this;
            this._originalY = this.y; // 保存原始Y位置

            // 设置滚动事件监听
            this.setupScrolling();

            console.log('✅ UILayout: 滚动功能初始化完成');
        }

        /**
         * 🔑 设置滚轮滚动（参考 UIList）
         */
        setupScrolling() {
            console.log('🖱️ UILayout: 设置滚轮滚动');

            // 使用 RPG Maker MZ 的循环事件系统
            this.setupRPGMakerEventLoop();

            console.log('✅ UILayout: 循环事件设置完成');
        }

        /**
         * 🔑 设置 RPG Maker MZ 的循环事件系统（参考 UIList）
         */
        setupRPGMakerEventLoop() {
            // 注册到 Graphics.app.ticker（PIXI 渲染循环）
            if (typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                this._tickerCallback = () => this.processWheelScroll();
                Graphics.app.ticker.add(this._tickerCallback);
                console.log('✅ UILayout: 已注册到 PIXI ticker 循环');
                return;
            }

            console.warn('⚠️ UILayout: 无法注册到 RPG Maker MZ 循环');
        }

        /**
         * 🔑 处理滚轮滚动（参考 UIList）
         */
        processWheelScroll() {
            if (!this.scrollEnabled || !this._contentContainer) return;
            if (typeof TouchInput === 'undefined') return;

            // 检查是否在 UILayout 区域内
            if (!this.isTouchedInsideFrame()) return;

            // 使用 RPG Maker MZ 的滚轮检测
            const threshold = 20;
            if (TouchInput.wheelY >= threshold) {
                // 向下滚动
                this.scrollDown();
            }
            if (TouchInput.wheelY <= -threshold) {
                // 向上滚动
                this.scrollUp();
            }
        }

        /**
         * 🔑 检查鼠标是否在 UILayout 区域内
         */
        isTouchedInsideFrame() {
            if (typeof TouchInput === 'undefined') return false;

            // 获取鼠标在世界坐标中的位置
            const globalPos = { x: TouchInput.x, y: TouchInput.y };

            // 转换为本地坐标
            const localPos = this.toLocal(globalPos);

            return localPos.x >= 0 && localPos.x <= this.containerWidth &&
                   localPos.y >= 0 && localPos.y <= this.containerHeight;
        }

        /**
         * 🔑 向下滚动
         */
        scrollDown() {
            this.scrollTop = Math.min(this.scrollTop + this.scrollSpeed, this.maxScrollTop);
            this.updateScrollPosition();
            console.log('🖱️ UILayout: 向下滚动', { scrollTop: this.scrollTop, maxScrollTop: this.maxScrollTop });
        }

        /**
         * 🔑 向上滚动
         */
        scrollUp() {
            this.scrollTop = Math.max(this.scrollTop - this.scrollSpeed, 0);
            this.updateScrollPosition();
            console.log('🖱️ UILayout: 向上滚动', { scrollTop: this.scrollTop, maxScrollTop: this.maxScrollTop });
        }

        /**
         * 🔑 更新滚动位置
         */
        updateScrollPosition() {
            if (!this.scrollEnabled) return;

            // 🔑 直接移动所有子对象，而不是移动容器
            if (this.generatedItems && this.generatedItems.length > 0) {
                this.generatedItems.forEach(item => {
                    if (item._originalY === undefined) {
                        item._originalY = item.y; // 保存原始位置
                    }
                    item.y = item._originalY - this.scrollTop;
                });
            }

            console.log('🖱️ UILayout: 滚动位置更新', {
                scrollTop: this.scrollTop,
                maxScrollTop: this.maxScrollTop,
                itemsCount: this.generatedItems ? this.generatedItems.length : 0
            });
        }

        /**
         * 🔑 更新滚动范围
         */
        updateScrollRange() {
            if (!this.scrollEnabled || !this._contentContainer) return;

            // 🔑 使用布局计算时已经得到的内容高度
            let contentHeight = this._contentHeight || 0;

            // 如果没有预计算的内容高度，则重新计算
            if (contentHeight === 0) {
                if (this.generatedItems.length > 0) {
                    // 使用生成的项目计算高度
                    this.generatedItems.forEach(item => {
                        const itemBottom = item.y + item.height;
                        contentHeight = Math.max(contentHeight, itemBottom);
                    });
                } else if (this.children && this.children.length > 0) {
                    // 使用所有子对象计算高度
                    this.children.forEach(child => {
                        if (child !== this._contentContainer) {
                            const childBottom = child.y + (child.height || 0);
                            contentHeight = Math.max(contentHeight, childBottom);
                        }
                    });
                }
            }

            // 🔑 计算最大滚动距离：内容高度 - 容器显示高度
            this.maxScrollTop = Math.max(0, contentHeight - this.containerHeight);

            console.log('📏 UILayout: 滚动范围更新', {
                contentHeight: contentHeight,
                containerHeight: this.containerHeight,
                maxScrollTop: this.maxScrollTop,
                scrollEnabled: this.maxScrollTop > 0
            });
        }

        /**
         * 🔑 滚动到指定位置
         */
        scrollTo(scrollTop) {
            this.scrollTop = Math.max(0, Math.min(scrollTop, this.maxScrollTop));
            this.updateScrollPosition();
        }

        /**
         * 🔑 设置模板数据并渲染
         */
        setTemplateData(dataArray) {
            console.log('🔄 UILayout: 设置模板数据', {
                dataCount: dataArray ? dataArray.length : 0,
                currentChildrenCount: this.children ? this.children.length : 0,
                isTemplateMode: this.isTemplateMode
            });

            this.templateData = dataArray || [];

            // 🔑 自动启用模板模式（如果有数据的话）
            if (this.templateData.length > 0) {
                this.isTemplateMode = true;
                this.renderFromTemplate();
            }
        }

        /**
         * 🔑 从模板渲染列表项
         */
        renderFromTemplate() {
            if (!this.isTemplateMode) {
                console.warn('⚠️ UILayout: 未启用模板模式');
                return;
            }

            console.log('🔄 UILayout: 开始从模板渲染列表项', {
                dataCount: this.templateData.length,
                currentChildrenCount: this.children ? this.children.length : 0
            });

            // 🔑 第一步：保存第一个元素作为模板（如果还没有缓存）
            if (!this._cachedTemplate) {
                this._getTemplate(); // 这会缓存第一个子元素
            }

            const template = this._cachedTemplate;
            if (!template) {
                console.warn('⚠️ UILayout: 无法获取模板对象');
                return;
            }

            console.log('🔄 UILayout: 使用模板渲染', {
                templateType: template.uiComponentType,
                templateId: template.componentId
            });

            // 🔑 第二步：清空所有子元素（包括原始的第一个元素）
            this.clearAllChildren();

            // 🔑 第三步：为每个数据项克隆模板并渲染
            this.templateData.forEach((itemData, index) => {
                try {
                    const clonedItem = template.clone({ offsetPosition: false });

                    // 🔑 使用 updateFieldsToChildren 方法初始化数据（级联到所有子对象）
                    if (typeof clonedItem.updateFieldsToChildren === 'function') {
                        const context = {
                            index: index,
                            isTemplate: true,
                            templateMode: true,
                            parentLayout: this,
                            data: itemData  // 🔑 将数据放在 context 中
                        };
                        clonedItem.updateFieldsToChildren(context);
                    } else if (typeof clonedItem.onFieldUpdate === 'function') {
                        // 备用方案：如果没有级联方法，使用单层方法
                        const context = {
                            index: index,
                            isTemplate: true,
                            templateMode: true,
                            parentLayout: this,
                            data: itemData
                        };
                        clonedItem.onFieldUpdate(context);
                    }

                    // 显示克隆的项目
                    clonedItem.visible = true;
                    clonedItem._isTemplate = false;

                    // 🔑 直接添加到布局中
                    this.addChild(clonedItem);
                    this.generatedItems.push(clonedItem);

                    console.log(`✅ UILayout: 生成列表项 [${index}]`, {
                        itemType: clonedItem.uiComponentType,
                        itemId: clonedItem.componentId
                    });

                } catch (error) {
                    console.error(`❌ UILayout: 生成列表项 [${index}] 失败`, error);
                }
            });

            // 更新布局
            this.updateLayout();

            // 🔑 第五步：更新滚动范围（如果启用滚动）
            if (this.scrollEnabled) {
                this.updateScrollRange();
            }

            console.log('✅ UILayout: 模板渲染完成', {
                generatedCount: this.generatedItems.length,
                totalChildren: this.children ? this.children.length : 0,
                scrollEnabled: this.scrollEnabled,
                maxScrollTop: this.maxScrollTop
            });
        }

        /**
         * 🔑 清除所有子元素
         */
        clearAllChildren() {
            console.log('🔄 UILayout: 清除所有子元素', {
                currentCount: this.children ? this.children.length : 0
            });

            // 🔧 安全清除所有子元素
            const childrenToRemove = [...(this.children || [])]; // 创建副本避免迭代时修改
            childrenToRemove.forEach(child => {
                try {
                    if (child && child.parent === this) {
                        this.removeChild(child);

                        // 🔧 安全清理资源
                        if (typeof child.destroy === 'function' && child.transform) {
                            child.destroy();
                        }
                    }
                } catch (error) {
                    console.error('🚨 UILayout: 清理子对象时发生错误', error, child);
                }
            });

            // 清空生成项目数组
            this.generatedItems = [];
            console.log('✅ UILayout: 所有子元素清除完成');
        }

        /**
         * 🔑 清除生成的列表项
         */
        clearGeneratedItems() {
            console.log('🔄 UILayout: 清除生成的列表项', {
                currentCount: this.generatedItems.length
            });

            // 🔧 安全清理生成的项目
            this.generatedItems.forEach(item => {
                try {
                    if (item && item.parent === this) {
                        this.removeChild(item);
                    }
                    // 🔧 安全清理资源
                    if (typeof item.destroy === 'function' && item.transform) {
                        item.destroy();
                    }
                } catch (error) {
                    console.error('🚨 UILayout: 清理生成项目时发生错误', error, item);
                }
            });

            this.generatedItems = [];
            console.log('✅ UILayout: 生成的列表项清除完成');
        }

        /**
         * 🔑 更新模板数据（不重新渲染，直接更新现有项目）
         */
        updateTemplateData(newDataArray) {
            console.log('🔄 UILayout: 更新模板数据', {
                newDataCount: newDataArray ? newDataArray.length : 0,
                currentGeneratedCount: this.generatedItems.length,
                hasTemplate: !!this._cachedTemplate
            });

            this.templateData = newDataArray || [];

            // 如果已经有生成的项目，直接更新它们的数据
            if (this.generatedItems.length > 0 && this.templateData.length > 0) {
                this.generatedItems.forEach((item, index) => {
                    if (index < this.templateData.length) {
                        const context = {
                            index: index,
                            isUpdate: true,
                            templateMode: true,
                            parentLayout: this,
                            data: this.templateData[index]  // 🔑 将数据放在 context 中
                        };

                        // 🔑 优先使用级联更新方法
                        if (typeof item.updateFieldsToChildren === 'function') {
                            item.updateFieldsToChildren(context);
                        } else if (typeof item.onFieldUpdate === 'function') {
                            item.onFieldUpdate(context);
                        }
                    }
                });
                console.log('✅ UILayout: 现有项目数据更新完成');
            } else {
                // 否则重新渲染
                this.renderFromTemplate();
            }
        }

        /**
         * 销毁时清理资源
         */
        destroy() {
            // 🔑 安全执行销毁脚本
            try {
                if (this.executeScript && !this._isDestroying) {
                    this._isDestroying = true; // 防止重复销毁
                    this.executeScript('onDestroy');
                }
            } catch (error) {
                console.warn('⚠️ UILayout: 销毁脚本执行失败', error);
            }

            // 🔑 清理滚动相关资源
            if (this._tickerCallback && typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                Graphics.app.ticker.remove(this._tickerCallback);
                this._tickerCallback = null;
                console.log('🗑️ UILayout: 已清理滚动事件监听');
            }

            // 清理生成的项目
            this.clearGeneratedItems();

            // 清理模板缓存
            if (this._cachedTemplate && typeof this._cachedTemplate.destroy === 'function') {
                this._cachedTemplate.destroy();
            }
            this._cachedTemplate = null;
            this.templateData = [];

            // 调用父类的销毁方法
            if (super.destroy) {
                super.destroy();
            }
        }

        /**
         * 🔑 获取克隆属性
         */
        getCloneProperties() {
            return {
                x:this.x,
                y:this.y,
                // 基础属性
                width: this.containerWidth,
                height: this.containerHeight,

                // 布局属性
                layoutType: this.layoutType,
                spacing: this.spacing,
                padding: this.padding,
                horizontalSpacing: this.horizontalSpacing,
                verticalSpacing: this.verticalSpacing,
                columns: this.columns,
                rows: this.rows,
                mainAxisAlignment: this.mainAxisAlignment,
                crossAxisAlignment: this.crossAxisAlignment,
                autoUpdate: this.autoUpdate,

                // 高级设置
                wrapContent: this.wrapContent || false,
                reverseOrder: this.reverseOrder || false,

                // 🔑 滚动相关设置
                scrollEnabled: this.scrollEnabled,
                scrollSpeed: this.scrollSpeed,

                // 🔑 深度克隆组件脚本数组
                componentScripts: this.componentScripts ?
                    this.componentScripts.map(script => ({
                        id: script.id,
                        name: script.name,
                        type: script.type,
                        enabled: script.enabled,
                        code: script.code,
                        description: script.description
                    })) : []
            };
        }

        /**
         * 克隆布局管理器
         */
        clone(options = {}) {


            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = this.getCloneProperties();

            // 2. 创建克隆对象
            const clonedLayout = new UILayout(cloneProperties);
console.log('--------------------------------------------------------------------');
console.log(this.x, this.y);

            // 3. 设置位置和变换属性
            clonedLayout.x = this.x + (offsetPosition ? offsetX : 0);
            clonedLayout.y = this.y + (offsetPosition ? offsetY : 0);
            clonedLayout.scale.x = this.scale.x;
            clonedLayout.scale.y = this.scale.y;
            clonedLayout.rotation = this.rotation;
            clonedLayout.alpha = this.alpha;
            clonedLayout.visible = this.visible;
            // clonedLayout.anchor.x = this.anchor.x;
            // clonedLayout.anchor.y = this.anchor.y;
            clonedLayout.pivot.x = this.pivot.x;
            clonedLayout.pivot.y = this.pivot.y;
            clonedLayout.skew.x = this.skew.x;
            clonedLayout.skew.y = this.skew.y;
            clonedLayout.zIndex = this.zIndex;

            // 4. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child) {
                    let clonedChild = null;

                    if (typeof child.clone === 'function') {
                        // UI组件有 clone 方法，直接调用
                        clonedChild = child.clone({ offsetPosition: false });
                    } else {
                        // 对于没有 clone 方法的对象（如 PIXI.Container），创建一个基础副本
                        console.log(`🔄 UILayout: 克隆基础对象 [${i}]: ${child.constructor.name}`);

                        // 创建同类型的新对象
                        const ChildClass = child.constructor;
                        clonedChild = new ChildClass();

                        // 复制基础属性
                        if (typeof child.x === 'number') clonedChild.x = child.x;
                        if (typeof child.y === 'number') clonedChild.y = child.y;
                        if (typeof child.visible === 'boolean') clonedChild.visible = child.visible;
                        if (typeof child.alpha === 'number') clonedChild.alpha = child.alpha;
                        if (typeof child.rotation === 'number') clonedChild.rotation = child.rotation;

                        // 复制 scale 属性
                        if (child.scale && clonedChild.scale) {
                            if (typeof child.scale.x === 'number') clonedChild.scale.x = child.scale.x;
                            if (typeof child.scale.y === 'number') clonedChild.scale.y = child.scale.y;
                        }

                        // 递归克隆子对象
                        if (child.children && child.children.length > 0) {
                            for (let j = 0; j < child.children.length; j++) {
                                const grandChild = child.children[j];
                                if (grandChild && typeof grandChild.clone === 'function') {
                                    const clonedGrandChild = grandChild.clone({ offsetPosition: false });
                                    clonedChild.addChild(clonedGrandChild);
                                }
                            }
                        }
                    }

                    if (clonedChild) {
                        clonedLayout.addChild(clonedChild);
                        clonedChildren.push(clonedChild);
                        console.log(`✅ UILayout: 子对象 [${i}] 克隆成功: ${child.constructor.name}`);
                    }
                }
            }


            return clonedLayout;
        }
    }

    // 导出到全局
    window.UILayout = UILayout;


})();
