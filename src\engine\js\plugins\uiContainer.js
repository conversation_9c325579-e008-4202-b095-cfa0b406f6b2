/*:
 * @target MZ
 * @plugindesc UIContainer v1.0.0
 * <AUTHOR>
 * @version 1.0.0
 * @description 专门的容器UI组件，继承自PIXI.Container，具有UIComponent和脚本功能
 *
 * @help uiContainer.js
 *
 * 使用方法：
 * const container = new UIContainer({
 *   x: 100,
 *   y: 100,
 *   width: 300,
 *   height: 200,
 *   name: 'MyContainer'
 * });
 */

(() => {
    'use strict';

    /**
     * UIContainer - 专门的容器UI组件，继承自PIXI.Container
     * 具有UIComponent功能和脚本管理功能
     */
    class UIContainer extends PIXI.Container {
        constructor(properties = {}) {
            super();

            console.log('📦 UIContainer: 创建容器组件', properties);

            // 标识为UI组件
            this.isUIComponent = true;
            this.uiComponentType = 'UIContainer';

            // 🔑 使用UIComponentUtils添加UIComponent功能
            if (window.UIComponentUtils) {
                window.UIComponentUtils.applyToSprite(this, properties);
            }

            // 🔑 使用UIScriptManager添加脚本功能
            if (window.UIScriptManager) {
                window.UIScriptManager.applyToObject(this, properties);
                console.log('🔧 UIContainer: 脚本系统初始化完成', {
                    hasComponentScripts: !!this.componentScripts,
                    scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                    scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                });
            }

            // 初始化容器组件
            this.initializeContainer(properties);

            // 🔑 标记为已创建，onStart会在添加到父容器时执行
            this._isCreated = true;
        }

        /**
         * 初始化容器组件
         * @param {Object} properties 容器属性
         */
        initializeContainer(properties) {
            // 设置默认属性
            this.setupDefaultProperties(properties);

            // 设置容器的交互属性
            this.interactive = properties.interactive !== false;
            this.interactiveChildren = properties.interactiveChildren !== false;

            console.log('✅ UIContainer: 容器初始化完成', {
                size: { width: this.containerWidth, height: this.containerHeight },
                interactive: this.interactive,
                interactiveChildren: this.interactiveChildren
            });
        }

        /**
         * 设置默认属性
         */
        setupDefaultProperties(properties) {
            // 容器尺寸属性
            this.containerWidth = properties.width || 100;
            this.containerHeight = properties.height || 100;

            // 背景相关属性
            this.backgroundColor = properties.backgroundColor || null;
            this.backgroundAlpha = properties.backgroundAlpha || 1;
            this.borderColor = properties.borderColor || null;
            this.borderWidth = properties.borderWidth || 0;
            this.borderRadius = properties.borderRadius || 0;

            // 布局相关属性
            this.padding = properties.padding || 0;
            this.margin = properties.margin || 0;

            // 如果设置了背景色，创建背景图形
            // if (this.backgroundColor) {
            //     this.createBackground();
            // }
        }

        /**
         * 创建背景图形
         */
        createBackground() {
            if (this.backgroundGraphics) {
                this.removeChild(this.backgroundGraphics);
            }

            this.backgroundGraphics = new PIXI.Graphics();
            this.backgroundGraphics.beginFill(parseInt(this.backgroundColor.replace('#', ''), 16), this.backgroundAlpha);

            if (this.borderWidth > 0 && this.borderColor) {
                this.backgroundGraphics.lineStyle(this.borderWidth, parseInt(this.borderColor.replace('#', ''), 16));
            }

            if (this.borderRadius > 0) {
                this.backgroundGraphics.drawRoundedRect(0, 0, this.containerWidth, this.containerHeight, this.borderRadius);
            } else {
                this.backgroundGraphics.drawRect(0, 0, this.containerWidth, this.containerHeight);
            }

            this.backgroundGraphics.endFill();
            this.addChildAt(this.backgroundGraphics, 0); // 添加到最底层

            console.log('🎨 UIContainer: 背景图形已创建');
        }

        /**
         * 更新容器尺寸
         */
        updateSize(width, height) {
            this.containerWidth = width;
            this.containerHeight = height;

            // 如果有背景，重新创建
            if (this.backgroundColor) {
                this.createBackground();
            }

            console.log('📏 UIContainer: 尺寸已更新', { width, height });
        }

        /**
         * 设置背景色
         */
        setBackgroundColor(color, alpha = 1) {
            this.backgroundColor = color;
            this.backgroundAlpha = alpha;
            this.createBackground();
        }

        /**
         * 设置边框
         */
        setBorder(color, width, radius = 0) {
            this.borderColor = color;
            this.borderWidth = width;
            this.borderRadius = radius;
            if (this.backgroundColor) {
                this.createBackground();
            }
        }

        /**
         * 获取宽度
         */
        get width() {
            return this.containerWidth;
        }

        /**
         * 设置宽度
         */
        set width(value) {
            this.updateSize(value, this.containerHeight);
        }

        /**
         * 获取高度
         */
        get height() {
            return this.containerHeight;
        }

        /**
         * 设置高度
         */
        set height(value) {
            this.updateSize(this.containerWidth, value);
        }

        /**
         * 克隆UIContainer对象
         * @param {Object} options 克隆选项
         * @returns {UIContainer} 克隆的UIContainer对象
         */
        clone(options = {}) {
            console.log('🔄 UIContainer: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 创建新的UIContainer实例
            const clonedContainer = new UIContainer({
                // 基础属性
                x: this.x + (offsetPosition ? offsetX : 0),
                y: this.y + (offsetPosition ? offsetY : 0),
                width: this.containerWidth,
                height: this.containerHeight,
                visible: this.visible,
                alpha: this.alpha,

                // 容器特有属性
                backgroundColor: this.backgroundColor,
                backgroundAlpha: this.backgroundAlpha,
                borderColor: this.borderColor,
                borderWidth: this.borderWidth,
                borderRadius: this.borderRadius,
                padding: this.padding,
                margin: this.margin,
                interactive: this.interactive,
                interactiveChildren: this.interactiveChildren,

                // UIComponent 属性
                name: (this.name || '') + '_clone',

                // 克隆脚本数组
                componentScripts: this.componentScripts ?
                    this.componentScripts.map(script => ({ ...script })) : []
            });

            // 2. 克隆所有子对象
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                // 跳过背景图形，因为它会在构造函数中重新创建
                if (child === this.backgroundGraphics) continue;

                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false });
                    clonedContainer.addChild(clonedChild);
                }
            }

            console.log('✅ UIContainer: 克隆完成');
            return clonedContainer;
        }

        /**
         * 销毁UIContainer并清理资源
         */
        destroy() {
            console.log('🗑️ UIContainer.destroy() 被调用！', this.name || 'unnamed');

            // 安全执行销毁脚本
            try {
                if (this.executeScript && !this._isDestroying) {
                    this._isDestroying = true;
                    this.executeScript('onDestroy');
                }
            } catch (error) {
                console.warn('⚠️ UIContainer: 销毁脚本执行失败', error);
            }

            // 清理背景图形
            if (this.backgroundGraphics) {
                this.backgroundGraphics.destroy();
                this.backgroundGraphics = null;
            }

            // 调用父类销毁方法
            super.destroy();

            console.log('✅ UIContainer: 销毁完成');
        }
    }

    // 导出到全局
    window.UIContainer = UIContainer;

    console.log('✅ UIContainer 插件加载完成');

})();
