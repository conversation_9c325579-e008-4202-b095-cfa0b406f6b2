

(() => {
    'use strict';
    //=============================================================================
    // UILabel Class - 专门的文本组件
    //=============================================================================

    /**
     * 文本组件类 - 继承自 Sprite
     * 专门用于显示文本，不混合其他功能
     */
    class UILabel extends Sprite {
        constructor(properties = {}) {
            super();

            console.log('🏷️ UILabel: 创建文本组件', properties);

            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'UILabel';

            // 🔑 使用UIComponentUtils添加UIComponent功能
            if (window.UIComponentUtils) {
                window.UIComponentUtils.applyToSprite(this, properties);
            }

            // 🔑 使用UIScriptManager添加脚本功能
            if (window.UIScriptManager) {
                window.UIScriptManager.applyToObject(this, properties);
                console.log('🔧 UILabel: 脚本系统初始化完成', {
                    hasComponentScripts: !!this.componentScripts,
                    scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                    scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                });
            }

            this.initializeLabel(properties);

            // 🔑 标记为已创建，onStart会在添加到父容器时执行
            this._isCreated = true;
        }


        /**
         * 初始化文本组件
         * @param {Object} properties 文本属性
         */
        initializeLabel(properties) {
            // � 设置为非交互式，避免拦截父容器的事件
            this.interactive = false;
            this.interactiveChildren = false;

            // 设置默认属性
            this.setupDefaultProperties(properties);

            // 设置文本位图
            this.setupTextBitmap();

            // 绘制文本
            this.redrawText();

            console.log('UILabel created:', this.labelWidth, 'x', this.labelHeight);
        }

        /**
         * 设置默认属性
         */
        setupDefaultProperties(properties) {
            // 基础属性 - 如果没有传入宽高，使用默认值
            this.labelWidth = properties.width || 200;
            this.labelHeight = properties.height || 40;

            // 文本属性 - 使用内部属性存储
            this._text = properties.text || 'Label Text';
            this.prefix = properties.prefix || '';  // 前缀
            this.suffix = properties.suffix || '';  // 后缀
            this.fontSize = properties.fontSize || 16;
            this.fontFace = properties.fontFace || 'GameFont';
            this.fontBold = properties.fontBold || false;
            this.fontItalic = properties.fontItalic || false;

            // 颜色属性
            this.textColor = properties.textColor || '#ffffff';
            this.outlineColor = properties.outlineColor || '#000000';
            this.outlineWidth = properties.outlineWidth || 4;

            // 对齐属性
            this.textAlign = properties.textAlign || 'center'; // left, center, right
            this.verticalAlign = properties.verticalAlign || 'middle'; // top, middle, bottom
            // 🔧 新增：垂直偏移调节参数（可以为负数向上偏移，正数向下偏移）
            this.verticalOffset = properties.verticalOffset || 0;

            // 🔑 间距属性
            this.letterSpacing = properties.letterSpacing || 0;
            this.wordSpacing = properties.wordSpacing || 0;
            this.lineHeight = properties.lineHeight || 1.2;

            // 🔑 渲染模式
            this.spacingMode = properties.spacingMode || 'auto'; // 'auto', 'bitmap', 'canvas'

            // 🔑 自动换行属性
            this.wordWrap = properties.wordWrap !== false; // 默认开启自动换行
            this.maxLines = properties.maxLines || 0; // 0表示不限制行数
            this.ellipsis = properties.ellipsis || '...'; // 省略号

            // 🔑 打字效果属性
            this.typewriterSpeed = properties.typewriterSpeed || 50; // 打字速度（毫秒/字符）
            this.typewriterDelay = properties.typewriterDelay || 0; // 开始前延迟（毫秒）
            this.typewriterCursor = properties.typewriterCursor !== false; // 是否显示光标
            this.typewriterCursorChar = properties.typewriterCursorChar || '|'; // 光标字符
            this.typewriterCursorBlink = properties.typewriterCursorBlink || 500; // 光标闪烁间隔

            // 🔑 打字效果状态
            this._typewriterState = {
                isActive: false,        // 是否正在打字
                isPaused: false,        // 是否暂停
                currentIndex: 0,        // 当前显示到第几个字符
                targetText: '',         // 目标文本
                displayText: '',        // 当前显示的文本
                timer: null,            // 定时器
                cursorTimer: null,      // 光标闪烁定时器
                showCursor: true,       // 光标显示状态
                onComplete: null,       // 完成回调
                onProgress: null        // 进度回调
            };

            // 背景属性
            this.backgroundColor = properties.backgroundColor || 'transparent';
            this.backgroundOpacity = properties.backgroundOpacity || 1;

            // 🔑 内边距属性（防止文本裁切）
            this.padding = properties.padding || 3; // 默认3像素内边距
            this.paddingTop = properties.paddingTop !== undefined ? properties.paddingTop : this.padding;
            this.paddingRight = properties.paddingRight !== undefined ? properties.paddingRight : this.padding;
            this.paddingBottom = properties.paddingBottom !== undefined ? properties.paddingBottom : this.padding;
            this.paddingLeft = properties.paddingLeft !== undefined ? properties.paddingLeft : this.padding;
        }

        /**
         * 获取标签宽度
         */
        get width() {
            return this.labelWidth;
        }

        /**
         * 设置标签宽度
         */
        set width(value) {
            this.labelWidth = value;
            this.setupTextBitmap();
            this.redrawText();
        }

        /**
         * 获取标签高度
         */
        get height() {
            return this.labelHeight;
        }

        /**
         * 设置标签高度
         */
        set height(value) {
            this.labelHeight = value;
            this.setupTextBitmap();
            this.redrawText();
        }

        /**
         * 设置文本位图
         */
        setupTextBitmap() {
            this.bitmap = new window.Bitmap(this.labelWidth, this.labelHeight);

            // 设置字体属性
            this.bitmap.fontSize = this.fontSize;
            this.bitmap.fontFace = this.fontFace;
            this.bitmap.fontBold = this.fontBold;
            this.bitmap.fontItalic = this.fontItalic;
            this.bitmap.textColor = this.textColor;
            this.bitmap.outlineColor = this.outlineColor;
            this.bitmap.outlineWidth = this.outlineWidth;
        }

        /**
         * 重绘文本
         */
        redrawText() {
            if (!this.bitmap) return;

            // 清除画布
            this.bitmap.clear();

            // 重新设置bitmap的所有属性（确保颜色正确）
            this.bitmap.fontSize = this.fontSize;
            this.bitmap.fontFace = this.fontFace;
            this.bitmap.fontBold = this.fontBold;
            this.bitmap.fontItalic = this.fontItalic;
            this.bitmap.textColor = this.textColor;
            this.bitmap.outlineColor = this.outlineColor;
            this.bitmap.outlineWidth = this.outlineWidth;

            console.log('🔧 UILabel: redrawText - textColor:', this.textColor, 'outlineColor:', this.outlineColor);

            // 绘制背景（如果需要）
            this.drawBackground();

            // 获取最终文本
            const finalText = this.getFinalText();

            // 根据是否需要间距选择渲染方法
            if (this.needsCustomSpacing()) {
                this.drawTextWithSpacing(finalText);
            } else {
                this.drawTextNormal(finalText);
            }
        }

        /**
         * 绘制背景
         */
        drawBackground() {
            if (this.backgroundColor !== 'transparent') {
                this.bitmap.fillRect(0, 0, this.labelWidth, this.labelHeight, this.backgroundColor);
            }
        }

        /**
         * 获取最终显示文本
         */
        getFinalText() {
            // 处理文本内容（检查是否为表达式）
            let displayText = this._text;
            if (window.EDITOR_MODE && this._text && this._text.startsWith('{{') && this._text.endsWith('}}')) {
                // 编辑器中用eval()预览表达式
                try {
                    const expression = this._text.slice(2, -2); // 去掉{{}}
                    displayText = String(eval(expression)) || this._text;
                } catch (error) {
                    console.warn('表达式预览失败:', this._text, error);
                    displayText = this._text; // 出错就显示原字符串
                }
            }

            // 添加前缀和后缀
            return this.prefix + displayText + this.suffix;
        }

        /**
         * 检查是否需要自定义间距
         */
        needsCustomSpacing() {
            return this.letterSpacing > 0 ||
                   this.wordSpacing > 0 ||
                   this.lineHeight !== 1.2;
        }

        /**
         * 使用标准方法绘制文本
         */
        drawTextNormal(finalText) {
            if (this.wordWrap) {
                // 🔑 多行文本绘制
                this.drawMultiLineText(finalText);
            } else {
                // 单行文本绘制
                const textY = this.calculateTextY();
                this.bitmap.drawText(
                    finalText,
                    0, textY,
                    this.labelWidth, this.fontSize,
                    this.textAlign
                );
            }
        }

        /**
         * 🔑 绘制多行文本
         */
        drawMultiLineText(text) {
            const lines = this.wrapText(text);
            const lineHeight = this.fontSize * this.lineHeight;
            const totalHeight = lines.length * lineHeight;
            // 🔑 考虑内边距的可用高度
            const availableHeight = this.labelHeight - this.paddingTop - this.paddingBottom;
            const maxVisibleLines = Math.floor(availableHeight / lineHeight);

            let displayLines = lines;
            let startY = 0;

            // 🔑 如果文本超出高度，始终保持最新行可见
            if (totalHeight > availableHeight && lines.length > maxVisibleLines) {
                // 显示最后几行，确保最新内容可见
                displayLines = lines.slice(-maxVisibleLines);

                // 根据垂直对齐方式调整起始位置
                switch (this.verticalAlign) {
                    case 'top':
                        startY = this.paddingTop;
                        break;
                    case 'bottom':
                        startY = this.labelHeight - this.paddingBottom - (displayLines.length * lineHeight);
                        break;
                    case 'middle':
                    default:
                        startY = this.paddingTop + (availableHeight - (displayLines.length * lineHeight)) / 2;
                        break;
                }
            } else {
                // 文本没有超出高度，按正常的垂直对齐处理
                switch (this.verticalAlign) {
                    case 'top':
                        startY = this.paddingTop;
                        break;
                    case 'bottom':
                        startY = this.labelHeight - this.paddingBottom - totalHeight;
                        break;
                    case 'middle':
                    default:
                        startY = this.paddingTop + (availableHeight - totalHeight) / 2;
                        break;
                }
            }

            // 绘制每一行
            displayLines.forEach((line, index) => {
                const y = startY + (index * lineHeight);
                this.bitmap.drawText(
                    line,
                    0, y,
                    this.labelWidth, this.fontSize,
                    this.textAlign
                );
            });
        }

        /**
         * 🔑 文本换行处理
         */
        wrapText(text) {
            if (!text) return [''];

            const words = text.split('');
            const lines = [];
            let currentLine = '';
            // 🔑 考虑内边距的可用宽度
            const availableWidth = this.labelWidth - this.paddingLeft - this.paddingRight;

            for (let i = 0; i < words.length; i++) {
                const char = words[i];
                const testLine = currentLine + char;
                const testWidth = this.calculateTextWidth(testLine);

                if (testWidth <= availableWidth) {
                    currentLine = testLine;
                } else {
                    // 当前行已满，开始新行
                    if (currentLine) {
                        lines.push(currentLine);
                        currentLine = char;
                    } else {
                        // 单个字符就超宽，强制添加
                        lines.push(char);
                        currentLine = '';
                    }
                }
            }

            // 添加最后一行
            if (currentLine) {
                lines.push(currentLine);
            }

            // 处理最大行数限制
            if (this.maxLines > 0 && lines.length > this.maxLines) {
                const truncatedLines = lines.slice(0, this.maxLines);

                // 在最后一行添加省略号
                if (truncatedLines.length > 0) {
                    const lastLine = truncatedLines[truncatedLines.length - 1];
                    let truncatedLine = lastLine;

                    // 逐步缩短最后一行直到能容纳省略号
                    while (truncatedLine.length > 0) {
                        const testLine = truncatedLine + this.ellipsis;
                        if (this.calculateTextWidth(testLine) <= this.labelWidth) {
                            truncatedLines[truncatedLines.length - 1] = testLine;
                            break;
                        }
                        truncatedLine = truncatedLine.slice(0, -1);
                    }

                    // 如果连省略号都放不下，就只显示省略号
                    if (truncatedLine.length === 0) {
                        truncatedLines[truncatedLines.length - 1] = this.ellipsis;
                    }
                }

                return truncatedLines;
            }

            return lines;
        }

        /**
         * 使用自定义间距绘制文本
         */
        drawTextWithSpacing(finalText) {
            if (this.wordWrap) {
                // � 多行文本绘制（带字符间距）
                this.drawMultiLineTextWithSpacing(finalText);
            } else {
                // 单行文本绘制（带字符间距）
                console.log('🔧 UILabel: 使用逐字符绘制，确保Y坐标一致');
                this.drawCharByChar(finalText);
            }
        }

        /**
         * 🔑 绘制多行文本（带字符间距）
         */
        drawMultiLineTextWithSpacing(text) {
            const lines = this.wrapTextWithSpacing(text);
            const lineHeight = this.fontSize * this.lineHeight;
            const totalHeight = lines.length * lineHeight;
            // 🔑 考虑内边距的可用高度
            const availableHeight = this.labelHeight - this.paddingTop - this.paddingBottom;
            const maxVisibleLines = Math.floor(availableHeight / lineHeight);

            let displayLines = lines;
            let startY = 0;

            // 🔑 如果文本超出高度，始终保持最新行可见
            if (totalHeight > availableHeight && lines.length > maxVisibleLines) {
                // 显示最后几行，确保最新内容可见
                displayLines = lines.slice(-maxVisibleLines);

                // 根据垂直对齐方式调整起始位置
                switch (this.verticalAlign) {
                    case 'top':
                        startY = this.paddingTop;
                        break;
                    case 'bottom':
                        startY = this.labelHeight - this.paddingBottom - (displayLines.length * lineHeight);
                        break;
                    case 'middle':
                    default:
                        startY = this.paddingTop + (availableHeight - (displayLines.length * lineHeight)) / 2;
                        break;
                }
            } else {
                // 文本没有超出高度，按正常的垂直对齐处理
                switch (this.verticalAlign) {
                    case 'top':
                        startY = this.paddingTop;
                        break;
                    case 'bottom':
                        startY = this.labelHeight - this.paddingBottom - totalHeight;
                        break;
                    case 'middle':
                    default:
                        startY = this.paddingTop + (availableHeight - totalHeight) / 2;
                        break;
                }
            }

            // 绘制每一行（带字符间距）
            displayLines.forEach((line, index) => {
                const y = startY + (index * lineHeight);
                this.drawLineWithSpacing(line, y);
            });
        }

        /**
         * 🔑 文本换行处理（带字符间距）
         */
        wrapTextWithSpacing(text) {
            if (!text) return [''];

            const words = text.split('');
            const lines = [];
            let currentLine = '';
            // 🔑 考虑内边距的可用宽度
            const availableWidth = this.labelWidth - this.paddingLeft - this.paddingRight;

            for (let i = 0; i < words.length; i++) {
                const char = words[i];
                const testLine = currentLine + char;
                const testWidth = this.calculateTextWidthWithSpacing(testLine);

                if (testWidth <= availableWidth) {
                    currentLine = testLine;
                } else {
                    // 当前行已满，开始新行
                    if (currentLine) {
                        lines.push(currentLine);
                        currentLine = char;
                    } else {
                        // 单个字符就超宽，强制添加
                        lines.push(char);
                        currentLine = '';
                    }
                }
            }

            // 添加最后一行
            if (currentLine) {
                lines.push(currentLine);
            }

            // 处理最大行数限制
            if (this.maxLines > 0 && lines.length > this.maxLines) {
                const truncatedLines = lines.slice(0, this.maxLines);

                // 在最后一行添加省略号
                if (truncatedLines.length > 0) {
                    const lastLine = truncatedLines[truncatedLines.length - 1];
                    let truncatedLine = lastLine;

                    // 逐步缩短最后一行直到能容纳省略号
                    while (truncatedLine.length > 0) {
                        const testLine = truncatedLine + this.ellipsis;
                        if (this.calculateTextWidthWithSpacing(testLine) <= this.labelWidth) {
                            truncatedLines[truncatedLines.length - 1] = testLine;
                            break;
                        }
                        truncatedLine = truncatedLine.slice(0, -1);
                    }

                    // 如果连省略号都放不下，就只显示省略号
                    if (truncatedLine.length === 0) {
                        truncatedLines[truncatedLines.length - 1] = this.ellipsis;
                    }
                }

                return truncatedLines;
            }

            return lines;
        }

        /**
         * 🔑 绘制单行文本（带字符间距）
         */
        drawLineWithSpacing(line, y) {
            const characters = line.split('');
            let currentX = this.calculateStartXWithSpacing(line);

            characters.forEach((char, index) => {
                // 绘制单个字符
                this.bitmap.drawText(
                    char,
                    currentX, y,
                    this.getCharWidth(char), this.fontSize,
                    'left'
                );

                // 计算下一个字符的位置
                const charWidth = this.getCharWidth(char);
                currentX += charWidth;

                // 添加字符间距
                if (index < characters.length - 1) {
                    if (char === ' ') {
                        currentX += this.wordSpacing;
                    } else {
                        currentX += this.letterSpacing;
                    }
                }
            });
        }

        /**
         * 🔑 计算文本起始X位置（带字符间距）
         */
        calculateStartXWithSpacing(text) {
            const totalWidth = this.calculateTextWidthWithSpacing(text);
            // 🔧 修复：为描边和内边距预留空间，避免裁切
            const outlinePadding = this.outlineWidth > 0 ? Math.ceil(this.outlineWidth / 2) : 0;
            const leftPadding = Math.max(this.paddingLeft, outlinePadding);
            const rightPadding = Math.max(this.paddingRight, outlinePadding);
            const availableWidth = this.labelWidth - leftPadding - rightPadding;

            switch (this.textAlign) {
                case 'center':
                    return leftPadding + Math.max(0, (availableWidth - totalWidth) / 2);
                case 'right':
                    return leftPadding + Math.max(0, availableWidth - totalWidth);
                case 'left':
                default:
                    return leftPadding;
            }
        }

        /**
         * 🔑 计算文本总宽度（带字符间距）
         */
        calculateTextWidthWithSpacing(text) {
            const characters = text.split('');
            let totalWidth = 0;

            characters.forEach((char, index) => {
                totalWidth += this.getCharWidth(char);

                // 添加间距
                if (index < characters.length - 1) {
                    if (char === ' ') {
                        totalWidth += this.wordSpacing;
                    } else {
                        totalWidth += this.letterSpacing;
                    }
                }
            });

            return totalWidth;
        }

        /**
         * 尝试使用 Canvas 2D Context 的 letterSpacing
         */
        tryCanvasSpacing(text) {
            const context = this.bitmap.context;
            if (!context || !('letterSpacing' in context)) {
                return false;
            }

            try {
                // 设置字体属性
                context.font = this.buildFontString();
                context.fillStyle = this.textColor;
                context.strokeStyle = this.outlineColor;
                context.lineWidth = this.outlineWidth;

                // 🔑 设置字符间距
                context.letterSpacing = `${this.letterSpacing}px`;

                // 🔧 修复：Canvas 2D Context 基线调整
                // Canvas 2D Context 从基线绘制，Bitmap.drawText 从顶部绘制
                const topY = this.calculateTextY(); // 获取顶部位置
                const textY = topY + this.fontSize * 0.85; // 基线约在85%位置
                const textX = this.calculateStartX(text);

                // 绘制描边
                if (this.outlineWidth > 0) {
                    context.strokeText(text, textX, textY);
                }

                // 绘制填充
                context.fillText(text, textX, textY);

                return true;
            } catch (error) {
                console.warn('Canvas letterSpacing 失败:', error);
                return false;
            }
        }

        /**
         * 构建字体字符串
         */
        buildFontString() {
            let font = '';
            if (this.fontItalic) font += 'italic ';
            if (this.fontBold) font += 'bold ';
            font += `${this.fontSize}px `;
            font += this.fontFace;
            return font;
        }

        /**
         * 逐字符绘制文本
         */
        drawCharByChar(text) {
            const characters = text.split('');
            let currentX = this.calculateStartX(text);
            const textY = this.calculateTextY();

            characters.forEach((char, index) => {
                // 绘制单个字符
                // 🔧 修复：lineHeight 应该是字体大小，不是容器高度
                this.bitmap.drawText(
                    char,
                    currentX, textY,
                    this.getCharWidth(char), this.fontSize,  // 使用 fontSize 作为 lineHeight
                    'left'
                );

                // 计算下一个字符的位置
                const charWidth = this.getCharWidth(char);
                currentX += charWidth;

                // 添加字符间距
                if (index < characters.length - 1) {
                    if (char === ' ') {
                        currentX += this.wordSpacing;
                    } else {
                        currentX += this.letterSpacing;
                    }
                }
            });
        }

        /**
         * 获取字符宽度（带缓存优化）
         */
        getCharWidth(char) {
            // 创建缓存键
            const cacheKey = `${char}_${this.fontSize}_${this.fontFace}_${this.fontBold}_${this.fontItalic}`;

            // 检查缓存
            if (!this._charWidthCache) {
                this._charWidthCache = {};
            }

            if (this._charWidthCache[cacheKey] !== undefined) {
                return this._charWidthCache[cacheKey];
            }

            // 创建临时bitmap测量字符宽度
            const tempBitmap = new window.Bitmap(100, 100);
            tempBitmap.fontSize = this.fontSize;
            tempBitmap.fontFace = this.fontFace;
            tempBitmap.fontBold = this.fontBold;
            tempBitmap.fontItalic = this.fontItalic;

            const width = tempBitmap.measureTextWidth(char);

            // 缓存结果
            this._charWidthCache[cacheKey] = width;

            // 销毁临时bitmap
            tempBitmap.destroy();

            return width;
        }

        /**
         * 计算文本起始X位置
         */
        calculateStartX(text) {
            const totalWidth = this.calculateTextWidth(text);
            // 🔧 修复：为描边和内边距预留空间，避免裁切
            const outlinePadding = this.outlineWidth > 0 ? Math.ceil(this.outlineWidth / 2) : 0;
            const leftPadding = Math.max(this.paddingLeft, outlinePadding);
            const rightPadding = Math.max(this.paddingRight, outlinePadding);
            const availableWidth = this.labelWidth - leftPadding - rightPadding;

            switch (this.textAlign) {
                case 'left':
                    return leftPadding;
                case 'right':
                    return leftPadding + Math.max(0, availableWidth - totalWidth);
                case 'center':
                default:
                    return leftPadding + Math.max(0, (availableWidth - totalWidth) / 2);
            }
        }

        /**
         * 计算文本总宽度（包含间距）
         */
        calculateTextWidth(text) {
            if (!this.needsCustomSpacing()) {
                // 使用标准测量
                const tempBitmap = new window.Bitmap(1000, 100);
                tempBitmap.fontSize = this.fontSize;
                tempBitmap.fontFace = this.fontFace;
                tempBitmap.fontBold = this.fontBold;
                tempBitmap.fontItalic = this.fontItalic;
                return tempBitmap.measureTextWidth(text);
            }

            // 计算带间距的宽度
            const characters = text.split('');
            let totalWidth = 0;

            characters.forEach((char, index) => {
                totalWidth += this.getCharWidth(char);

                // 添加间距
                if (index < characters.length - 1) {
                    if (char === ' ') {
                        totalWidth += this.wordSpacing;
                    } else {
                        totalWidth += this.letterSpacing;
                    }
                }
            });

            return totalWidth;
        }



        /**
         * 计算文本Y位置（垂直对齐）
         * 🔧 修复：考虑内边距，防止文本裁切
         */
        calculateTextY() {
            // 🔑 考虑内边距的可用高度
            const availableHeight = this.labelHeight - this.paddingTop - this.paddingBottom;

            switch (this.verticalAlign) {
                case 'top':
                    // 🔧 顶部对齐：从顶部内边距开始
                    return this.paddingTop + (this.verticalOffset || 0);
                case 'bottom':
                    // 🔧 底部对齐：从底部内边距减去字体大小
                    return this.labelHeight - this.paddingBottom - this.fontSize + (this.verticalOffset || 0);
                case 'middle':
                default:
                    // 🔧 居中对齐：在可用空间内居中
                    return this.paddingTop + (availableHeight - this.fontSize) / 2 + (this.verticalOffset || 0);
            }
        }

        /**
         * 🔑 text 属性的 getter
         */
        get text() {
            return this._text;
        }

        /**
         * 🔑 text 属性的 setter - 统一的文本设置方式，自动转换类型
         */
        set text(value) {
            // 🔑 自动类型转换
            let stringValue;
            if (value === null || value === undefined) {
                stringValue = '';
            } else if (typeof value === 'string') {
                stringValue = value;
            } else if (typeof value === 'number') {
                stringValue = value.toString();
            } else if (typeof value === 'boolean') {
                stringValue = value ? 'true' : 'false';
            } else if (typeof value === 'object') {
                try {
                    // 对于对象，尝试 JSON 序列化
                    stringValue = JSON.stringify(value);
                } catch (error) {
                    // 如果序列化失败，使用 toString
                    stringValue = value.toString();
                }
            } else {
                // 其他类型直接转换
                stringValue = String(value);
            }

            if (this._text !== stringValue) {
                this._text = stringValue;
                this.redrawText();
            }
        }

        /**
         * 设置文本内容（保持向后兼容）
         * @deprecated 推荐直接使用 this.text = value
         */
        setText(text) {
            this.text = text;
        }

        /**
         * 设置前缀
         */
        setPrefix(prefix) {
            this.prefix = prefix;
            this.redrawText();
        }

        /**
         * 设置后缀
         */
        setSuffix(suffix) {
            this.suffix = suffix;
            this.redrawText();
        }

        /**
         * 设置字符间距
         */
        setLetterSpacing(spacing) {
            this.letterSpacing = spacing;
            this.redrawText();
        }

        /**
         * 设置单词间距
         */
        setWordSpacing(spacing) {
            this.wordSpacing = spacing;
            this.redrawText();
        }

        /**
         * 设置行高
         */
        setLineHeight(height) {
            this.lineHeight = height;
            this.redrawText();
        }

        /**
         * 批量设置间距
         */
        setSpacing(options) {
            if (options.letter !== undefined) {
                this.letterSpacing = options.letter;
            }
            if (options.word !== undefined) {
                this.wordSpacing = options.word;
            }
            if (options.line !== undefined) {
                this.lineHeight = options.line;
            }
            this.redrawText();
        }

        /**
         * 设置字体大小
         */
        setFontSize(size) {
            this.fontSize = size;
            this.bitmap.fontSize = size;
            this._clearCharWidthCache(); // 清除字符宽度缓存
            this.redrawText();
        }

        /**
         * 清除字符宽度缓存
         */
        _clearCharWidthCache() {
            this._charWidthCache = {};
        }

        /**
         * 设置文本颜色
         */
        setTextColor(color) {
            console.log('🔧 UILabel: setTextColor调用', color);
            this.textColor = color;
            this.bitmap.textColor = color;
            console.log('🔧 UILabel: bitmap.textColor设置为', this.bitmap.textColor);
            this.redrawText();
        }

        /**
         * 设置描边颜色
         */
        setOutlineColor(color) {
            console.log('🔧 UILabel: setOutlineColor调用', color);
            this.outlineColor = color;
            this.bitmap.outlineColor = color;
            console.log('🔧 UILabel: bitmap.outlineColor设置为', this.bitmap.outlineColor);
            this.redrawText();
        }

        /**
         * 设置描边宽度
         */
        setOutlineWidth(width) {
            this.outlineWidth = width;
            this.bitmap.outlineWidth = width;
            this.redrawText();
        }

        /**
         * 设置文本对齐
         */
        setTextAlign(align) {
            this.textAlign = align;
            this.redrawText();
        }

        /**
         * 设置垂直对齐
         */
        setVerticalAlign(align) {
            this.verticalAlign = align;
            this.redrawText();
        }

        /**
         * 🔧 设置垂直偏移（用于微调文本位置）
         * @param {number} offset 偏移量，负数向上，正数向下
         */
        setVerticalOffset(offset) {
            this.verticalOffset = offset;
            this.redrawText();
        }

        /**
         * 设置尺寸
         */
        setSize(width, height) {
            this.labelWidth = width;
            this.labelHeight = height;
            this.setupTextBitmap();
            this.redrawText();
        }

        /**
         * 设置背景颜色
         */
        setBackgroundColor(color) {
            this.backgroundColor = color;
            this.redrawText();
        }

        // 🔑 ========== 打字效果方法 ==========

        /**
         * 开始打字效果
         * @param {string} text 要显示的文本
         * @param {object} options 选项
         * @param {number} options.speed 打字速度（毫秒/字符）
         * @param {number} options.delay 开始前延迟（毫秒）
         * @param {function} options.onProgress 进度回调
         * @param {function} options.onComplete 完成回调
         */
        startTypewriter(text, options = {}) {
            console.log('🔤 UILabel: 开始打字效果', text);

            // 停止当前的打字效果
            this.stopTypewriter();

            // 设置状态
            this._typewriterState.targetText = text || '';
            this._typewriterState.currentIndex = 0;
            this._typewriterState.displayText = '';
            this._typewriterState.isActive = true;
            this._typewriterState.isPaused = false;
            this._typewriterState.onProgress = options.onProgress || null;
            this._typewriterState.onComplete = options.onComplete || null;

            // 获取速度设置
            const speed = options.speed !== undefined ? options.speed : this.typewriterSpeed;
            const delay = options.delay !== undefined ? options.delay : this.typewriterDelay;

            // 清空当前文本
            this._text = '';
            this.redrawText();

            // 启动光标闪烁
            this._startCursorBlink();

            // 延迟开始打字
            if (delay > 0) {
                setTimeout(() => {
                    if (this._typewriterState.isActive) {
                        this._startTypewriterTimer(speed);
                    }
                }, delay);
            } else {
                this._startTypewriterTimer(speed);
            }
        }

        /**
         * 停止打字效果
         */
        stopTypewriter() {
            if (this._typewriterState.timer) {
                clearTimeout(this._typewriterState.timer);
                this._typewriterState.timer = null;
            }

            if (this._typewriterState.cursorTimer) {
                clearInterval(this._typewriterState.cursorTimer);
                this._typewriterState.cursorTimer = null;
            }

            this._typewriterState.isActive = false;
            this._typewriterState.isPaused = false;
        }

        /**
         * 暂停打字效果
         */
        pauseTypewriter() {
            if (this._typewriterState.isActive && !this._typewriterState.isPaused) {
                this._typewriterState.isPaused = true;
                if (this._typewriterState.timer) {
                    clearTimeout(this._typewriterState.timer);
                    this._typewriterState.timer = null;
                }
            }
        }

        /**
         * 恢复打字效果
         */
        resumeTypewriter() {
            if (this._typewriterState.isActive && this._typewriterState.isPaused) {
                this._typewriterState.isPaused = false;
                this._startTypewriterTimer(this.typewriterSpeed);
            }
        }

        /**
         * 立即完成打字效果
         */
        completeTypewriter() {
            if (this._typewriterState.isActive) {
                // 停止定时器
                this.stopTypewriter();

                // 立即显示全部文本
                this._text = this._typewriterState.targetText;
                this.redrawText();

                // 触发完成回调
                if (this._typewriterState.onComplete) {
                    this._typewriterState.onComplete();
                }

                console.log('🔤 UILabel: 打字效果立即完成');
            }
        }

        /**
         * 检查打字效果是否完成
         */
        isTypewriterComplete() {
            return !this._typewriterState.isActive ||
                   this._typewriterState.currentIndex >= this._typewriterState.targetText.length;
        }

        /**
         * 获取打字进度（0-100）
         */
        getTypewriterProgress() {
            if (!this._typewriterState.targetText) return 100;
            return Math.round((this._typewriterState.currentIndex / this._typewriterState.targetText.length) * 100);
        }

        // 🔑 ========== 打字效果内部方法 ==========

        /**
         * 启动打字定时器
         */
        _startTypewriterTimer(speed) {
            if (!this._typewriterState.isActive || this._typewriterState.isPaused) {
                return;
            }

            this._typewriterState.timer = setTimeout(() => {
                this._typewriterTick(speed);
            }, speed);
        }

        /**
         * 打字效果核心逻辑
         */
        _typewriterTick(speed) {
            if (!this._typewriterState.isActive || this._typewriterState.isPaused) {
                return;
            }

            const targetText = this._typewriterState.targetText;
            const currentIndex = this._typewriterState.currentIndex;

            // 检查是否完成
            if (currentIndex >= targetText.length) {
                this._completeTypewriter();
                return;
            }

            // 添加下一个字符
            this._typewriterState.currentIndex++;
            this._updateTypewriterDisplay();

            // 触发进度回调
            if (this._typewriterState.onProgress) {
                const progress = this.getTypewriterProgress();
                const currentText = targetText.substring(0, this._typewriterState.currentIndex);
                this._typewriterState.onProgress(progress, currentText);
            }

            // 继续下一个字符
            this._startTypewriterTimer(speed);
        }

        /**
         * 更新打字效果显示
         */
        _updateTypewriterDisplay() {
            const currentText = this._typewriterState.targetText.substring(0, this._typewriterState.currentIndex);

            // 添加光标
            if (this.typewriterCursor && this._typewriterState.showCursor && this._typewriterState.isActive) {
                this._text = currentText + this.typewriterCursorChar;
            } else {
                this._text = currentText;
            }

            // 重新绘制文本（利用已有的换行功能）
            this.redrawText();
        }

        /**
         * 启动光标闪烁
         */
        _startCursorBlink() {
            if (!this.typewriterCursor) return;

            // 清除现有的光标定时器
            if (this._typewriterState.cursorTimer) {
                clearInterval(this._typewriterState.cursorTimer);
            }

            this._typewriterState.showCursor = true;
            this._typewriterState.cursorTimer = setInterval(() => {
                if (this._typewriterState.isActive) {
                    this._typewriterState.showCursor = !this._typewriterState.showCursor;
                    this._updateTypewriterDisplay();
                }
            }, this.typewriterCursorBlink);
        }

        /**
         * 完成打字效果（内部方法）
         */
        _completeTypewriter() {
            // 停止定时器
            this.stopTypewriter();

            // 显示完整文本（不带光标）
            this._text = this._typewriterState.targetText;
            this.redrawText();

            // 触发完成回调
            if (this._typewriterState.onComplete) {
                this._typewriterState.onComplete();
            }

            console.log('🔤 UILabel: 打字效果自然完成');
        }

        /**
         * 获取所有属性（用于模型同步）
         */
        getProperties() {
            return {
                name:this.name,
                // 基础属性
                x: this.x,
                y: this.y,
                width: this.labelWidth,
                height: this.labelHeight,

                // 文本属性
                text: this._text,
                fontSize: this.fontSize,
                fontFace: this.fontFace,
                fontBold: this.fontBold,
                fontItalic: this.fontItalic,

                // 颜色属性
                textColor: this.textColor,
                outlineColor: this.outlineColor,
                outlineWidth: this.outlineWidth,

                // 对齐属性
                textAlign: this.textAlign,
                verticalAlign: this.verticalAlign,

                // 背景属性
                backgroundColor: this.backgroundColor,
                backgroundOpacity: this.backgroundOpacity
            };
        }

        /**
         * 克隆当前 UILabel 对象
         * @param {Object} options 克隆选项
         * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
         * @param {number} options.offsetX 水平偏移量 (默认: 20)
         * @param {number} options.offsetY 垂直偏移量 (默认: 20)
         * @returns {UILabel} 克隆的 UILabel 对象
         */
        clone(options = {}) {
            console.log('🔄 UILabel: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = {
                // 基础属性
                width: this.labelWidth,
                height: this.labelHeight,
                visible: this.visible,

                // 🔑 UIComponent 属性（安全访问）
                name: this.name || '',
                enabled: this.enabled !== false,
                dataBinding: this.dataBinding || '',

                // 🔑 深度克隆组件脚本数组
                componentScripts: this.componentScripts ?
                    this.componentScripts.map(script => ({
                        id: script.id,
                        name: script.name,
                        type: script.type,
                        enabled: script.enabled,
                        code: script.code,
                        description: script.description
                    })) : [],

                // UILabel 特有属性
                text: this._text,
                prefix: this.prefix,
                suffix: this.suffix,
                fontSize: this.fontSize,
                fontFace: this.fontFace,
                fontBold: this.fontBold,
                fontItalic: this.fontItalic,
                textColor: this.textColor,
                outlineColor: this.outlineColor,
                outlineWidth: this.outlineWidth,
                textAlign: this.textAlign,
                verticalAlign: this.verticalAlign,
                backgroundColor: this.backgroundColor,
                backgroundOpacity: this.backgroundOpacity,

                // 🔑 间距属性
                letterSpacing: this.letterSpacing,
                wordSpacing: this.wordSpacing,
                lineHeight: this.lineHeight,
                spacingMode: this.spacingMode
            };

            // 2. 创建克隆对象
            const clonedLabel = new UILabel(cloneProperties);

            // 🔧 调试：验证脚本是否被克隆
            console.log('🔄 UILabel: 克隆验证', {
                原始组件名称: this.name,
                克隆组件名称: clonedLabel.name,
                原始脚本数量: this.componentScripts ? this.componentScripts.length : 0,
                克隆脚本数量: clonedLabel.componentScripts ? clonedLabel.componentScripts.length : 0,
                原始脚本详情: this.componentScripts ? this.componentScripts.map(s => `${s.name}(${s.enabled ? '启用' : '禁用'})`) : [],
                克隆脚本详情: clonedLabel.componentScripts ? clonedLabel.componentScripts.map(s => `${s.name}(${s.enabled ? '启用' : '禁用'})`) : []
            });

            // 3. 设置位置和变换属性
            clonedLabel.x = this.x + (offsetPosition ? offsetX : 0);
            clonedLabel.y = this.y + (offsetPosition ? offsetY : 0);
            clonedLabel.scale.x = this.scale.x;
            clonedLabel.scale.y = this.scale.y;
            clonedLabel.rotation = this.rotation;
            clonedLabel.alpha = this.alpha;
            clonedLabel.anchor.x = this.anchor.x;
            clonedLabel.anchor.y = this.anchor.y;
            clonedLabel.pivot.x = this.pivot.x;
            clonedLabel.pivot.y = this.pivot.y;
            clonedLabel.skew.x = this.skew.x;
            clonedLabel.skew.y = this.skew.y;
            clonedLabel.zIndex = this.zIndex;

            // 4. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                    clonedLabel.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                }
            }

            console.log('✅ UILabel: 克隆完成，包含', clonedChildren.length, '个子对象');
            return clonedLabel;
        }
        /**
         * 🔑 获取克隆属性
         */
        getCloneProperties() {
            return {
                // 基础属性
                width: this.labelWidth,
                height: this.labelHeight,

                // 🔑 UIComponent 属性
                name: this.name || '',
                enabled: this.enabled !== false,
                dataBinding: this.dataBinding || '',
                componentScript: this.componentScript ? {
                    lifecycle: { ...(this.componentScript.lifecycle || {}) },
                    dataEvents: { ...(this.componentScript.dataEvents || {}) },
                    interactionEvents: { ...(this.componentScript.interactionEvents || {}) },
                    customFunctions: { ...(this.componentScript.customFunctions || {}) },
                    variables: { ...(this.componentScript.variables || {}) }
                } : undefined,

                // 文本属性
                text: this._text,
                prefix: this.prefix,
                suffix: this.suffix,
                fontSize: this.fontSize,
                fontFace: this.fontFace,
                fontBold: this.fontBold,
                fontItalic: this.fontItalic,

                // 颜色和样式
                textColor: this.textColor,
                outlineColor: this.outlineColor,
                outlineWidth: this.outlineWidth,

                // 对齐
                textAlign: this.textAlign,
                verticalAlign: this.verticalAlign,

                // 背景
                backgroundColor: this.backgroundColor,
                backgroundOpacity: this.backgroundOpacity,

                // 间距
                letterSpacing: this.letterSpacing || 0,
                wordSpacing: this.wordSpacing || 0,
                lineHeight: this.lineHeight || 1.2,
                spacingMode: this.spacingMode || 'auto'
            };
        }

        /**
         * 每帧更新 - 使用UIComponent的update方法
         */
        update() {
            // 🔑 调用UIComponent的update方法，它会处理脚本执行
            // 不需要调用super.update()，因为UIComponent的update方法已经包含了所有逻辑
            if (this._updateEnabled && this._isStarted && this.executeScript) {
                const currentTime = Date.now();
                const deltaTime = this._lastUpdateTime ? currentTime - this._lastUpdateTime : 16;
                this._lastUpdateTime = currentTime;

                // 执行 onUpdate 脚本
                this.executeScript('onUpdate', { deltaTime, currentTime });
            }
        }

        /**
         * 销毁文本组件
         */
        destroy() {
            // 🔑 安全执行销毁脚本
            try {
                if (this.executeScript && !this._isDestroying) {
                    this._isDestroying = true; // 防止重复销毁
                    this.executeScript('onDestroy');
                }
            } catch (error) {
                console.warn('⚠️ UILabel: 销毁脚本执行失败', error);
            }

            // 清理缓存
            this._clearCharWidthCache();

            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }
                try {
               if (super.destroy){
              super.destroy();
               } } catch (error) {
                  console.warn('⚠️ UILabel: 销毁脚本执行失败', error);
              }

        }
    }
    // 将类添加到全局
    window.UILabel = UILabel;

    console.log('UI Base Components Plugin loaded - UIImage and UILabel classes available');

})();