<script lang="ts">
  import type { SceneDataFlow } from '../types';
  import ButtonItem from './ButtonItem.svelte';

  interface Props {
    sceneData: SceneDataFlow;
  }

  let { sceneData }: Props = $props();
</script>

<!-- 场景容器 -->
<div class="scene-container">
  <!-- 场景标题 -->
  <div class="scene-header">
    <h2>{sceneData.sceneName}</h2>
    <div class="scene-stats">
      <span class="scene-count">{sceneData.buttons.length} 个UI元素</span>
      {#if sceneData.dataResources && sceneData.dataResources.length > 0}
        <span class="data-count">{sceneData.dataResources.length} 个数据资源</span>
      {/if}
    </div>
  </div>

  <!-- 数据资源部分 -->
  {#if sceneData.dataResources && sceneData.dataResources.length > 0}
    <div class="data-resources-section">
      <h3>📊 场景数据资源</h3>
      <div class="data-resources-grid">
        {#each sceneData.dataResources as resource, index}
          <div class="data-resource-item">
            <span class="resource-number">{index + 1}.</span>
            <code class="resource-path">{resource.dataPath}</code>
            <span class="resource-description">{resource.description}</span>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- UI按钮列表 -->
  <div class="buttons-section">
    <h3>🔘 UI交互按钮</h3>
    <div class="buttons-list">
      {#each sceneData.buttons as button, index}
        <ButtonItem buttonData={button} {index} />
      {/each}
    </div>
  </div>
</div>

<style>
  .scene-container {
    max-width: 100%;
    margin: 0 auto;
  }

  /* 场景标题 */
  .scene-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-3);
    border-bottom: 2px solid var(--theme-border);
  }

  .scene-header h2 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--theme-text);
  }

  .scene-stats {
    display: flex;
    gap: var(--spacing-2);
  }

  .scene-count, .data-count {
    background: var(--theme-primary-light);
    color: var(--theme-primary-dark);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
  }

  .data-count {
    background: var(--theme-accent-light);
    color: var(--theme-accent-dark);
  }

  /* 数据资源部分 */
  .data-resources-section {
    margin-bottom: var(--spacing-4);
    padding: var(--spacing-3);
    background: var(--theme-surface-elevated);
    border-radius: var(--border-radius);
    border: 1px solid var(--theme-border);
  }

  .data-resources-section h3 {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-text);
  }

  .data-resources-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .data-resource-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
    background: var(--theme-surface);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--theme-border);
  }

  .resource-number {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
    font-weight: 500;
    min-width: 20px;
  }

  .resource-path {
    font-family: var(--font-mono);
    font-size: var(--font-size-sm);
    color: var(--theme-success);
    background: var(--theme-surface-elevated);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--theme-border);
    font-weight: 500;
    flex: 1;
  }

  .resource-description {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
    font-style: italic;
    flex-shrink: 0;
    max-width: 200px;
  }

  /* UI按钮部分 */
  .buttons-section {
    margin-top: var(--spacing-4);
  }

  .buttons-section h3 {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-text);
  }

  .buttons-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
  }
</style>
