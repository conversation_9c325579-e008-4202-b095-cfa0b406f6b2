<script lang="ts">
  /**
   * 属性面板组件 - 响应式版本
   * 使用 SceneModelState 中的选中对象
   */

  import {
    sceneModelState,
    primarySelectedObjectInfo,
    selectedObjectsInfo,
    hasSelectedObjects
  } from '../../stores/sceneModelStore';
  import ReactiveBasePropertyPanel from './panels/ReactiveBasePropertyPanel.svelte';
  import ImagePropertyPanel from './panels/imagePropertyPanel.svelte';
  import SliderPropertyPanel from './panels/sliderPropertyPanel.svelte';
  import SwitchPropertyPanel from './panels/switchPropertyPanel.svelte';
  import UIImagePropertyPanel from './panels/uiImagePropertyPanel.svelte';
  import UILabelPropertyPanel from './panels/uiLabelPropertyPanel.svelte';
  import UIButtonPropertyPanel from './panels/uiButtonPropertyPanel.svelte';
  import UIInputPropertyPanel from './panels/uiInputPropertyPanel.svelte';
  import UIAtlasPropertyPanel from './panels/uiAtlasPropertyPanel.svelte';
  import MaskPropertyPanel from './panels/maskPropertyPanel.svelte';
  import UILayoutPropertyPanel from './panels/uiLayoutPropertyPanel.svelte';

  // 监听选中的对象
  let currentState = $derived($sceneModelState);
  let primaryObjectInfo = $derived($primarySelectedObjectInfo);
  let allSelectedInfo = $derived($selectedObjectsInfo);
  let hasSelected = $derived($hasSelectedObjects);

  // 判断是否应该显示图片属性面板
  let shouldShowImagePanel = $derived(() => {
    if (!currentState.selectedObjects || currentState.selectedObjects.length === 0) {
      return false;
    }

    const firstObject = currentState.selectedObjects[0];

    // 检查是否为Sprite对象或继承自Sprite的对象
    if (!isInheritedFromSprite(firstObject)) {
      return false;
    }

    // 检查是否有bitmap和url
    const spriteData = firstObject as any;
    const hasValidUrl = spriteData?.bitmap?.url && spriteData.bitmap.url.trim() !== '';

    return hasValidUrl;
  });


  // 判断是否应该显示滑动条属性面板
  let shouldShowSliderPanel = $derived(() => {
    if (!currentState.selectedObjects || currentState.selectedObjects.length === 0) {
      return false;
    }

    const firstObject = currentState.selectedObjects[0];

    // 检查是否为 Slider 对象
    return isSliderObject(firstObject);
  });

  // 判断是否应该显示开关属性面板
  let shouldShowSwitchPanel = $derived(() => {
    if (!currentState.selectedObjects || currentState.selectedObjects.length === 0) {
      return false;
    }

    const firstObject = currentState.selectedObjects[0];

    // 检查是否为 Switch 对象
    return isSwitchObject(firstObject);
  });

  // 判断是否应该显示UIImage属性面板
  let shouldShowUIImagePanel = $derived(() => {
    if (!currentState.selectedObjects || currentState.selectedObjects.length === 0) {
      return false;
    }

    const firstObject = currentState.selectedObjects[0];

    // 检查是否为 UIImage 对象
    return isUIImageObject(firstObject);
  });

  // 判断是否应该显示UILabel属性面板
  let shouldShowUILabelPanel = $derived(() => {
    if (!currentState.selectedObjects || currentState.selectedObjects.length === 0) {
      return false;
    }

    const firstObject = currentState.selectedObjects[0];

    // 检查是否为 UILabel 对象
    return isUILabelObject(firstObject);
  });

  // 判断是否应该显示UIButton属性面板
  let shouldShowUIButtonPanel = $derived(() => {
    if (!currentState.selectedObjects || currentState.selectedObjects.length === 0) {
      return false;
    }

    const firstObject = currentState.selectedObjects[0];

    // 检查是否为 UIButton 对象
    return isUIButtonObject(firstObject);
  });

  // 判断是否应该显示UIInput属性面板
  let shouldShowUIInputPanel = $derived(() => {
    if (!currentState.selectedObjects || currentState.selectedObjects.length === 0) {
      return false;
    }

    const firstObject = currentState.selectedObjects[0];

    // 检查是否为 UIInput 对象
    return isUIInputObject(firstObject);
  });

  // 判断是否应该显示UIAtlas属性面板
  let shouldShowUIAtlasPanel = $derived(() => {
    if (!currentState.selectedObjects || currentState.selectedObjects.length === 0) {
      return false;
    }

    const firstObject = currentState.selectedObjects[0];

    // 检查是否为 UIAtlas 对象
    return isUIAtlasObject(firstObject);
  });


  // 判断是否应该显示UILayout属性面板
  let shouldShowUILayoutPanel = $derived(() => {
    if (!currentState.selectedObjects || currentState.selectedObjects.length === 0) {
      return false;
    }

    const firstObject = currentState.selectedObjects[0];

    // 检查是否为 UILayout 对象
    return isUILayoutObject(firstObject);
  });

  // 判断是否应该显示UIMask属性面板
  let shouldShowUIMaskPanel = $derived(() => {
    if (!currentState.selectedObjects || currentState.selectedObjects.length === 0) {
      return false;
    }

    const firstObject = currentState.selectedObjects[0];

    // 检查是否为 UIMask 对象
    return isUIMaskObject(firstObject);
  });


  /**
   * 检查对象是否继承自Sprite
   */
  function isInheritedFromSprite(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 直接检查 className 属性
    if (obj.className === 'Sprite') {
      return true;
    }

    // 检查 className 是否包含 Sprite（如 SpriteButton, Sprite_Clickable 等）
    if (obj.className && obj.className.includes('Sprite')) {
      return true;
    }

    // 检查原始对象的构造函数名称
    if (obj._originalObject && obj._originalObject.constructor) {
      const originalClassName = obj._originalObject.constructor.name;
      if (originalClassName === 'Sprite' || originalClassName.includes('Sprite')) {
        return true;
      }
    }

    return false;
  }


  /**
   * 检查对象是否为 Slider 对象
   */
  function isSliderObject(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 检查是否为 SliderModel
    if (obj.constructor && obj.constructor.name === 'SliderModel') {
      return true;
    }

    // 检查 className 属性
    if (obj.className === 'Slider') {
      return true;
    }

    // 检查原始对象是否为 Slider
    if (obj._originalObject && obj._originalObject.constructor) {
      const originalClassName = obj._originalObject.constructor.name;
      if (originalClassName === 'Slider') {
        return true;
      }
    }

    // 检查是否有 UI 组件标识
    if (obj._originalObject && obj._originalObject.isUIComponent && obj._originalObject.uiComponentType === 'Slider') {
      return true;
    }

    return false;
  }

  /**
   * 检查对象是否为 Switch 对象
   */
  function isSwitchObject(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 检查是否为 SwitchModel
    if (obj.constructor && obj.constructor.name === 'SwitchModel') {
      return true;
    }

    // 检查 className 属性
    if (obj.className === 'Switch') {
      return true;
    }

    // 检查原始对象是否为 Switch
    if (obj._originalObject && obj._originalObject.constructor) {
      const originalClassName = obj._originalObject.constructor.name;
      if (originalClassName === 'Switch' || originalClassName === 'UISwitch') {
        return true;
      }
    }

    // 检查是否有 UI 组件标识
    if (obj._originalObject && obj._originalObject.isUIComponent && obj._originalObject.uiComponentType === 'Switch') {
      return true;
    }

    return false;
  }

  /**
   * 检查对象是否为 UIImage 对象
   */
  function isUIImageObject(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 只检查 className 属性
    return obj.className === 'UIImage';
  }

  /**
   * 检查对象是否为 UILabel 对象
   */
  function isUILabelObject(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 只检查 className 属性
    return obj.className === 'UILabel';
  }

  /**
   * 检查对象是否为 UIButton 对象
   */
  function isUIButtonObject(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 检查是否为 ButtonModel
    if (obj.constructor && obj.constructor.name === 'ButtonModel') {
      return true;
    }

    // 检查 className 属性
    if (obj.className === 'UIButton') {
      return true;
    }

    // 检查原始对象是否为 UIButton
    if (obj._originalObject && obj._originalObject.constructor) {
      const originalClassName = obj._originalObject.constructor.name;
      if (originalClassName === 'UIButton') {
        return true;
      }
    }

    // 检查是否有 UI 组件标识
    if (obj._originalObject && obj._originalObject.isUIComponent && obj._originalObject.uiComponentType === 'UIButton') {
      return true;
    }

    return false;
  }

  /**
   * 检查对象是否为 UIInput 对象
   */
  function isUIInputObject(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 检查是否为 InputModel
    if (obj.constructor && obj.constructor.name === 'InputModel') {
      return true;
    }

    // 检查原始对象是否为 UIInput
    if (obj._originalObject && obj._originalObject.constructor) {
      const originalClassName = obj._originalObject.constructor.name;
      if (originalClassName === 'UIInput') {
        return true;
      }
    }

    // 检查是否有 UI 组件标识
    if (obj._originalObject && obj._originalObject.isUIComponent && obj._originalObject.uiComponentType === 'UIInput') {
      return true;
    }

    return false;
  }

  /**
   * 检查对象是否为 UIAtlas 对象
   */
  function isUIAtlasObject(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 检查是否为 AtlasModel
    if (obj.constructor && obj.constructor.name === 'AtlasModel') {
      return true;
    }

    // 检查 className 属性
    if (obj.className === 'UIAtlas') {
      return true;
    }

    return false;
  }

  /**
   * 检查对象是否为 UIList 对象
   */
  function isUIListObject(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 检查是否为 ListModel
    if (obj.constructor && obj.constructor.name === 'ListModel') {
      return true;
    }

    // 检查 className 属性
    if (obj.className === 'UIList') {
      return true;
    }

    // 检查原始对象是否为 UIList
    if (obj._originalObject && obj._originalObject.constructor) {
      const originalClassName = obj._originalObject.constructor.name;
      if (originalClassName === 'UIList') {
        return true;
      }
    }

    // 检查是否有 UI 组件标识
    if (obj._originalObject && obj._originalObject.isUIComponent && obj._originalObject.uiComponentType === 'UIList') {
      return true;
    }

    return false;
  }

  /**
   * 检查对象是否为 UIItem 对象
   */
  function isUIItemObject(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 检查是否为 ItemModel
    if (obj.constructor && obj.constructor.name === 'ItemModel') {
      return true;
    }

    // 检查 className 属性
    if (obj.className === 'UIItem') {
      return true;
    }

    // 检查原始对象是否为 UIItem
    if (obj._originalObject && obj._originalObject.constructor) {
      const originalClassName = obj._originalObject.constructor.name;
      if (originalClassName === 'UIItem') {
        return true;
      }
    }

    // 检查是否有 UI 组件标识
    if (obj._originalObject && obj._originalObject.isUIComponent && obj._originalObject.uiComponentType === 'UIItem') {
      return true;
    }

    return false;
  }

  /**
   * 检查对象是否为 UILayout 对象
   */
  function isUILayoutObject(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 检查是否为 LayoutModel
    if (obj.constructor && obj.constructor.name === 'LayoutModel') {
      return true;
    }

    // 检查 className 属性
    if (obj.className === 'UILayout') {
      return true;
    }

    // 检查原始对象是否为 UILayout
    if (obj._originalObject && obj._originalObject.constructor) {
      const originalClassName = obj._originalObject.constructor.name;
      if (originalClassName === 'UILayout') {
        return true;
      }
    }

    // 检查是否有 UI 组件标识
    if (obj._originalObject && obj._originalObject.isUIComponent && obj._originalObject.uiComponentType === 'UILayout') {
      return true;
    }

    return false;
  }

  /**
   * 检查对象是否为 UIMask 对象
   */
  function isUIMaskObject(obj: any): boolean {
    if (!obj) {
      return false;
    }

    // 检查是否为 MaskModel
    if (obj.constructor && obj.constructor.name === 'MaskModel') {
      return true;
    }

    // 检查 className 属性
    if (obj.className === 'UIMask') {
      return true;
    }

    // 检查原始对象是否为 UIMask
    if (obj._originalObject && obj._originalObject.constructor) {
      const originalClassName = obj._originalObject.constructor.name;
      if (originalClassName === 'UIMask') {
        return true;
      }
    }

    // 检查是否有 UI 组件标识
    if (obj._originalObject && obj._originalObject.isUIComponent && obj._originalObject.uiComponentType === 'UIMask') {
      return true;
    }

    return false;
  }

  console.log('PropertyPanel 组件已加载');
</script>

<div class="property-panel">
  <!-- 紧凑头部 -->
  <div class="panel-header">
    <span class="panel-title">属性面板</span>

    {#if hasSelected}
      <div class="object-info">
        <span class="object-type-badge">
          {primaryObjectInfo?.className || '未知'}
        </span>
        {#if currentState.selectedObjects.length > 1}
          <span class="multi-select-badge">
            +{currentState.selectedObjects.length - 1}
          </span>
        {/if}
      </div>
    {:else}
      <div class="no-object-indicator">
        <span class="indicator-dot"></span>
      </div>
    {/if}
  </div>

  <!-- 内容区域 -->
  <div class="panel-content">
    {#if hasSelected}
      <ReactiveBasePropertyPanel />
      {#if shouldShowSliderPanel()}
        <SliderPropertyPanel />
      {/if}
      {#if shouldShowSwitchPanel()}
        <SwitchPropertyPanel />
      {/if}
      {#if shouldShowUIImagePanel()}
        <UIImagePropertyPanel model={currentState.selectedObjects[0]} />
      {/if}
      {#if shouldShowUILabelPanel()}
        <UILabelPropertyPanel model={currentState.selectedObjects[0]} />
      {/if}
      {#if shouldShowUIButtonPanel()}
        <UIButtonPropertyPanel model={currentState.selectedObjects[0]} />
      {/if}
      {#if shouldShowUIInputPanel()}
        <UIInputPropertyPanel model={currentState.selectedObjects[0]} />
      {/if}
      {#if shouldShowUIAtlasPanel()}
        <UIAtlasPropertyPanel model={currentState.selectedObjects[0]} />
      {/if}

      {#if shouldShowUILayoutPanel()}
        <UILayoutPropertyPanel model={currentState.selectedObjects[0]} />
      {/if}
      {#if shouldShowUIMaskPanel()}
        <MaskPropertyPanel model={currentState.selectedObjects[0]} />
      {/if}
      {#if shouldShowImagePanel()}
        <ImagePropertyPanel />
      {/if}

      <!-- 多选信息 -->
      {#if currentState.selectedObjects.length > 1}
        <div class="multi-select-info">
          <h4>多选对象 ({currentState.selectedObjects.length})</h4>
          <div class="selected-objects-list">
            {#each allSelectedInfo as objInfo, index}
              <div class="selected-object-item" class:primary={objInfo.isPrimary}>
                <span class="object-icon">
                  {objInfo.isPrimary ? '🎯' : '📄'}
                </span>
                <span class="object-name">{objInfo.className}</span>
                {#if objInfo.name}
                  <span class="object-detail">({objInfo.name})</span>
                {/if}
              </div>
            {/each}
          </div>
        </div>
      {/if}
    {:else}
      <div class="empty-state">
        <div class="empty-content">
          <div class="empty-icon">🎯</div>
          <div class="empty-text">
            <span class="empty-title">未选中对象</span>
            <span class="empty-subtitle">点击左侧对象树选择</span>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .property-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--theme-surface, #ffffff);
    border-left: 1px solid var(--theme-border, #e2e8f0);
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 8px;
    background: var(--theme-surface-light, #f8f9fa);
    border-bottom: 1px solid var(--theme-border, #e2e8f0);
    min-height: 32px;
  }

  .panel-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--theme-text, #1a202c);
  }

  .object-info {
    display: flex;
    align-items: center;
  }

  .object-type-badge {
    font-size: 9px;
    font-weight: 500;
    color: var(--theme-primary, #3182ce);
    background: var(--theme-primary-light, rgba(49, 130, 206, 0.1));
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid var(--theme-primary-light, rgba(49, 130, 206, 0.2));
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .no-object-indicator {
    display: flex;
    align-items: center;
  }

  .indicator-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--theme-text-muted, #cbd5e0);
    opacity: 0.5;
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 4px;
    min-height: 0;
    max-height: calc(100vh - var(--title-bar-height, 32px) - var(--status-bar-height, 24px) - 100px);
  }

  /* 自定义滚动条样式 */
  .panel-content::-webkit-scrollbar {
    width: 6px;
  }

  .panel-content::-webkit-scrollbar-track {
    background: var(--theme-surface-dark, #1a202c);
    border-radius: 3px;
  }

  .panel-content::-webkit-scrollbar-thumb {
    background: var(--theme-border, rgba(255, 255, 255, 0.2));
    border-radius: 3px;
    transition: background-color var(--transition-fast, 0.15s ease);
  }

  .panel-content::-webkit-scrollbar-thumb:hover {
    background: var(--theme-border-dark, rgba(255, 255, 255, 0.3));
  }

  .empty-state {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 8px;
  }

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
  }

  .empty-icon {
    font-size: 24px;
    opacity: 0.3;
  }

  .empty-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .empty-title {
    font-size: 11px;
    font-weight: 600;
    color: var(--theme-text, #1a202c);
  }

  .empty-subtitle {
    font-size: 9px;
    color: var(--theme-text-secondary, #718096);
    line-height: 1.3;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .panel-header {
      padding: 4px 6px;
      min-height: 28px;
    }

    .panel-title {
      font-size: 11px;
    }

    .object-type-badge {
      font-size: 8px;
      padding: 1px 4px;
    }

    .empty-icon {
      font-size: 20px;
    }

    .empty-title {
      font-size: 10px;
    }

    .empty-subtitle {
      font-size: 8px;
    }
  }
</style>