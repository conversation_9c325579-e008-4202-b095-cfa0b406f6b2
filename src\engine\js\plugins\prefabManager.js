/**
 * 预制体管理器
 * 简化版本：使用内置的 clone 方法来实现预制体功能
 */

(() => {
    'use strict';

    /**
     * 预制体管理器
     * 提供预制体的注册、实例化功能
     */
    window.PrefabManager = {
        // 存储所有预制体模板
        prefabs: new Map(),

        /**
         * 注册预制体 - 提取组件数据而不是保存引用
         * @param {string} name - 预制体名称
         * @param {object} templateComponent - 模板组件
         * @param {string} description - 描述信息
         */
        register(name, templateComponent, description = '') {
            console.log('🔧 PrefabManager: 注册预制体', { name, description });

            if (this.prefabs.has(name)) {
                console.warn(`🚨 PrefabManager: 预制体 ${name} 已存在，将被覆盖`);
            }

            try {
                // 🔑 提取组件数据（包括子集）
                const templateData = this.extractComponentData(templateComponent);

                // 存储预制体信息
                const prefabInfo = {
                    name: name,
                    description: description,
                    templateData: templateData,
                    createdAt: Date.now(),
                    type: templateComponent.constructor.name,
                    version: "1.0.0"
                };

                this.prefabs.set(name, prefabInfo);
                console.log(`✅ PrefabManager: 预制体 ${name} 注册成功`, {
                    type: prefabInfo.type,
                    hasChildren: templateData.children && templateData.children.length > 0
                });

                return prefabInfo;

            } catch (error) {
                console.error(`❌ PrefabManager: 注册预制体 ${name} 失败`, error);
                throw error;
            }
        },

        /**
         * 🔑 提取组件数据（递归处理子集）
         * @param {object} component - 组件实例
         * @returns {object} 组件数据
         */
        extractComponentData(component) {
            const data = {
                type: component.constructor.name,
                name: component.name || '',

                // 🔑 使用 getCloneProperties 获取完整属性
                properties: this.getComponentProperties(component),

                // 🔑 递归提取子组件数据
                children: []
            };

            // 处理子组件
            if (component.children && component.children.length > 0) {
                component.children.forEach(child => {
                    // 只处理UI组件
                    if (child.isUIComponent) {
                        data.children.push(this.extractComponentData(child));
                    }
                });
            }

            return data;
        },

        /**
         * 🔑 获取组件属性（必须有 getCloneProperties 方法）
         * @param {object} component - 组件实例
         * @returns {object} 组件属性
         */
        getComponentProperties(component) {
            if (!component.getCloneProperties || typeof component.getCloneProperties !== 'function') {
                const error = `组件 ${component.constructor.name} 没有实现 getCloneProperties 方法，无法创建预制体`;
                console.error('❌ PrefabManager:', error);
                throw new Error(error);
            }

            return component.getCloneProperties();
        },

        /**
         * 实例化预制体 - 根据提取的数据重建组件
         * @param {string} name - 预制体名称
         * @param {object} overrideProperties - 覆盖属性
         * @returns {object} 新的组件实例
         */
        instantiate(name, overrideProperties = {}) {
            console.log('🔧 PrefabManager: 实例化预制体', { name, overrideProperties });

            const prefabInfo = this.prefabs.get(name);
            if (!prefabInfo) {
                const error = `预制体 ${name} 不存在`;
                console.error('❌ PrefabManager:', error);
                throw new Error(error);
            }

            try {
                // 🔑 根据模板数据重建组件
                const instance = this.createComponentFromData(prefabInfo.templateData, overrideProperties);

                console.log(`✅ PrefabManager: 预制体 ${name} 实例化成功`, {
                    type: instance.constructor.name,
                    name: instance.name,
                    hasChildren: instance.children && instance.children.length > 0
                });

                return instance;

            } catch (error) {
                console.error(`❌ PrefabManager: 实例化预制体 ${name} 失败`, error);
                throw error;
            }
        },

        /**
         * 🔑 根据数据创建组件实例（递归处理子集）
         * @param {object} data - 组件数据
         * @param {object} overrideProperties - 覆盖属性（只应用到根组件）
         * @returns {object} 组件实例
         */
        createComponentFromData(data, overrideProperties = {}) {
            // 获取组件构造函数
            const ComponentClass = window[data.type];
            if (!ComponentClass) {
                throw new Error(`组件类型 ${data.type} 不存在`);
            }

            // 合并属性（根组件应用覆盖属性）
            const properties = { ...data.properties, ...overrideProperties };

            // 创建组件实例
            const component = new ComponentClass(properties);

            // 设置名称
            if (data.name) {
                component.name = data.name;
            }

            // 生成新的组件ID
            if (component.componentId) {
                component.componentId = this.generateComponentId(component);
            }

            // 🔑 标记为预制体实例
            component._prefabInfo = {
                prefabName: name,
                instanceId: this.generateInstanceId(),
                createdAt: Date.now(),
                isInstance: true
            };

            // 🔑 递归创建子组件
            if (data.children && data.children.length > 0) {
                data.children.forEach(childData => {
                    const childComponent = this.createComponentFromData(childData);
                    component.addChild(childComponent);
                });
            }

            return component;
        },

        /**
         * 获取所有预制体列表
         * @returns {Array} 预制体信息列表
         */
        getAllPrefabs() {
            return Array.from(this.prefabs.values());
        },

        /**
         * 获取预制体信息
         * @param {string} name - 预制体名称
         * @returns {object|null} 预制体信息
         */
        getPrefabInfo(name) {
            return this.prefabs.get(name) || null;
        },

        /**
         * 删除预制体
         * @param {string} name - 预制体名称
         * @returns {boolean} 是否删除成功
         */
        unregister(name) {
            const existed = this.prefabs.has(name);
            this.prefabs.delete(name);
            
            if (existed) {
                console.log(`✅ PrefabManager: 预制体 ${name} 已删除`);
            } else {
                console.warn(`🚨 PrefabManager: 预制体 ${name} 不存在`);
            }
            
            return existed;
        },

        /**
         * 检查预制体是否存在
         * @param {string} name - 预制体名称
         * @returns {boolean} 是否存在
         */
        exists(name) {
            return this.prefabs.has(name);
        },

        /**
         * 生成组件ID
         * @param {object} component - 组件实例
         * @returns {string} 新的组件ID
         */
        generateComponentId(component) {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substr(2, 9);
            const type = component.constructor.name;
            return `${type}_${timestamp}_${random}`;
        },

        /**
         * 生成预制体实例ID
         * @returns {string} 实例ID
         */
        generateInstanceId() {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substr(2, 9);
            return `instance_${timestamp}_${random}`;
        },

        /**
         * 清空所有预制体
         */
        clear() {
            const count = this.prefabs.size;
            this.prefabs.clear();
            console.log(`🔧 PrefabManager: 已清空 ${count} 个预制体`);
        },

        /**
         * 导出预制体数据（用于保存到文件）
         * @param {string} name - 预制体名称
         * @returns {object} 预制体数据
         */
        export(name) {
            const prefabInfo = this.prefabs.get(name);
            if (!prefabInfo) {
                throw new Error(`预制体 ${name} 不存在`);
            }

            return {
                name: prefabInfo.name,
                description: prefabInfo.description,
                type: prefabInfo.type,
                createdAt: prefabInfo.createdAt,
                templateData: prefabInfo.templateData,
                version: prefabInfo.version
            };
        },

        /**
         * 从数据导入预制体（用于从文件加载）
         * @param {object} data - 预制体数据
         */
        import(data) {
            if (!data.name || !data.templateData) {
                throw new Error('预制体数据格式不正确');
            }

            // 直接使用数据创建预制体信息
            const prefabInfo = {
                name: data.name,
                description: data.description || '',
                templateData: data.templateData,
                createdAt: data.createdAt || Date.now(),
                type: data.type,
                version: data.version || "1.0.0"
            };

            this.prefabs.set(data.name, prefabInfo);
            console.log(`✅ PrefabManager: 从数据导入预制体 ${data.name}`);

            return prefabInfo;
        },

        /**
         * 🔑 保存预制体到文件
         * @param {string} name - 预制体名称
         */
        saveToFile(name) {
            try {
                const data = this.export(name);
                const jsonData = JSON.stringify(data, null, 2);
                const blob = new Blob([jsonData], { type: 'application/json' });
                const url = URL.createObjectURL(blob);

                // 触发下载
                const a = document.createElement('a');
                a.href = url;
                a.download = `${name}.prefab.json`;
                a.click();

                URL.revokeObjectURL(url);
                console.log(`✅ PrefabManager: 预制体 ${name} 已保存到文件`);

            } catch (error) {
                console.error(`❌ PrefabManager: 保存预制体 ${name} 失败`, error);
                throw error;
            }
        },

        /**
         * 🔑 从文件加载预制体
         * @param {File} file - 预制体文件
         * @returns {Promise<object>} 预制体信息
         */
        async loadFromFile(file) {
            try {
                const text = await file.text();
                const data = JSON.parse(text);
                return this.import(data);

            } catch (error) {
                console.error('❌ PrefabManager: 从文件加载预制体失败', error);
                throw error;
            }
        }
    };

    // 🔧 调试：输出管理器信息
    console.log('✅ PrefabManager: 预制体管理器已加载');

})();
