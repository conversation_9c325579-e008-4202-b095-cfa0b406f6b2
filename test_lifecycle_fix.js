// 🔍 测试生命周期修复的脚本
// 在浏览器控制台中运行此脚本来验证修复是否有效

function testLifecycleFix() {
    console.log('🔍 ===== 测试生命周期修复 =====');
    
    // 1. 检查必要的管理器是否存在
    console.log('1. 检查管理器:');
    console.log('   - UIScriptManager存在:', !!window.UIScriptManager);
    console.log('   - UIComponentUtils存在:', !!window.UIComponentUtils);
    console.log('   - UIUpdateManager存在:', !!window.UIUpdateManager);
    console.log('   - UILabel存在:', !!window.UILabel);
    console.log('   - UIButton存在:', !!window.UIButton);
    
    if (!window.UILabel || !window.UIScriptManager) {
        console.error('❌ 必要的类不存在，无法进行测试');
        return;
    }
    
    // 2. 创建测试UILabel
    console.log('\n2. 创建测试UILabel:');
    const testLabel = new window.UILabel({
        text: 'Test Label',
        width: 200,
        height: 50,
        componentScripts: [{
            id: 'test_script',
            name: '测试脚本',
            enabled: true,
            code: `
function onStart() {
    console.log("✅ UILabel onStart 执行成功:", self.name || "unnamed");
    this.text = "onStart executed!";
}

function onUpdate() {
    // 只在前几次更新时输出，避免刷屏
    if (!this._updateCount) this._updateCount = 0;
    this._updateCount++;
    if (this._updateCount <= 3) {
        console.log("✅ UILabel onUpdate 执行成功:", this._updateCount);
    }
}

function onClick() {
    console.log("✅ UILabel onClick 执行成功");
    this.text = "Clicked!";
}
            `,
            description: '测试脚本'
        }]
    });
    
    console.log('   - UILabel创建完成');
    console.log('   - executeScript方法存在:', typeof testLabel.executeScript === 'function');
    console.log('   - componentScripts存在:', !!testLabel.componentScripts);
    console.log('   - 脚本数量:', testLabel.componentScripts ? testLabel.componentScripts.length : 0);
    
    // 3. 检查生命周期状态
    console.log('\n3. 检查生命周期状态:');
    console.log('   - _isCreated:', testLabel._isCreated);
    console.log('   - _isStarted:', testLabel._isStarted);
    console.log('   - _isRegisteredForUpdate:', testLabel._isRegisteredForUpdate);
    
    // 4. 手动测试executeScript
    console.log('\n4. 手动测试executeScript:');
    if (typeof testLabel.executeScript === 'function') {
        try {
            console.log('   - 手动执行onStart...');
            testLabel.executeScript('onStart');
            console.log('   - onStart执行完成');
        } catch (error) {
            console.error('   - onStart执行失败:', error);
        }
    } else {
        console.error('   - executeScript方法不存在');
    }
    
    // 5. 测试添加到场景
    console.log('\n5. 测试添加到场景:');
    if (window.SceneManager && window.SceneManager._scene) {
        const scene = window.SceneManager._scene;
        console.log('   - 当前场景:', scene.constructor.name);
        
        try {
            // 添加到场景
            scene.addChild(testLabel);
            console.log('   - 已添加到场景');
            
            // 检查生命周期状态变化
            setTimeout(() => {
                console.log('\n6. 添加到场景后的状态:');
                console.log('   - _isStarted:', testLabel._isStarted);
                console.log('   - _isRegisteredForUpdate:', testLabel._isRegisteredForUpdate);
                console.log('   - UIUpdateManager组件数量:', window.UIUpdateManager._components.length);
                
                // 7. 测试update循环
                console.log('\n7. 测试update循环:');
                if (testLabel._isRegisteredForUpdate) {
                    console.log('   - 组件已注册到更新循环');
                    console.log('   - 等待几秒查看onUpdate输出...');
                    
                    // 8. 测试点击事件
                    setTimeout(() => {
                        console.log('\n8. 测试点击事件:');
                        if (typeof testLabel.executeScript === 'function') {
                            testLabel.executeScript('onClick');
                            console.log('   - onClick测试完成');
                        }
                        
                        // 9. 清理测试
                        setTimeout(() => {
                            console.log('\n9. 清理测试:');
                            try {
                                scene.removeChild(testLabel);
                                console.log('   - 已从场景移除');
                                console.log('   - UIUpdateManager组件数量:', window.UIUpdateManager._components.length);
                            } catch (error) {
                                console.error('   - 清理失败:', error);
                            }
                            
                            console.log('\n🔍 ===== 测试完成 =====');
                        }, 2000);
                    }, 3000);
                } else {
                    console.error('   - 组件未注册到更新循环');
                }
            }, 100);
            
        } catch (error) {
            console.error('   - 添加到场景失败:', error);
        }
    } else {
        console.error('   - 当前没有活动场景');
    }
}

// 运行测试
testLifecycleFix();
