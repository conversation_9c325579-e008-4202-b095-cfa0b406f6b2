<script lang="ts">
  import type { Category } from './types';

  let {
    categories,
    selectedCategory,
    onSelect
  }: {
    categories: Category[];
    selectedCategory: Category | null;
    onSelect: (category: Category) => void;
  } = $props();

  function handleSelect(category: Category) {
    onSelect(category);
  }
</script>

<div class="category-selector">
  {#each categories as category}
    <button
      class="category-item"
      class:selected={selectedCategory?.id === category.id}
      onclick={() => handleSelect(category)}
      title={category.description}
    >
      <span class="category-icon">{category.icon}</span>
      <div class="category-info">
        <div class="category-label">{category.label}</div>
        <div class="category-count">
          {Object.keys(category.items).length} 项
        </div>
      </div>
    </button>
  {/each}
</div>

<style>
  .category-selector {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .category-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    margin-bottom: 4px;
    border: none;
    border-radius: 6px;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s;
    text-align: left;
  }

  .category-item:hover {
    background: var(--hover-bg, #f0f0f0);
  }

  .category-item.selected {
    background: var(--primary-bg, #e3f2fd);
    border: 1px solid var(--primary-color, #2196f3);
  }

  .category-icon {
    font-size: 20px;
    flex-shrink: 0;
  }

  .category-info {
    flex: 1;
    min-width: 0;
  }

  .category-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary, #333);
    margin-bottom: 2px;
  }

  .category-count {
    font-size: 12px;
    color: var(--text-muted, #666);
  }

  .category-item.selected .category-label {
    color: var(--primary-color, #2196f3);
    font-weight: 600;
  }

  .category-item.selected .category-count {
    color: var(--primary-color, #2196f3);
  }
</style>
