<script lang="ts">
  /**
   * 响应式基础属性面板 - 支持多选增量同步
   * 编辑主对象时，其他选中对象会自动跟随相同的变化量
   */

  import {
    sceneModelState
  } from '../../../stores/sceneModelStore';
  import { historyManager } from '../../../historyManager';
  import LabelInput from '../../../components/LabelInput.svelte';
  import SafeInput from '../../../components/SafeInput.svelte';
  import Slider from '../../../components/Slider.svelte';
  import Label from '../../../components/Label.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';

  // 监听选中的对象
  let currentState = $derived($sceneModelState);
  let selectedObjects = $derived(currentState.selectedObjects);
  let primaryObject = $derived(selectedObjects[0] || null);
  let isMultiSelection = $derived(selectedObjects.length > 1);

  // � 存储上一次的属性值，用于计算增量
  let previousValues = $state<Record<string, any>>({});

  // 🔑 初始化或更新上一次的值
  $effect(() => {
    if (primaryObject) {
      previousValues = {
        x: primaryObject.x,
        y: primaryObject.y,
        width: primaryObject.width,
        height: primaryObject.height,
        anchorX: primaryObject.anchorX,
        anchorY: primaryObject.anchorY,
        scaleX: primaryObject.scaleX,
        scaleY: primaryObject.scaleY,
        rotation: primaryObject.rotation,
        alpha: primaryObject.alpha,
        visible: primaryObject.visible
      };
    }
  });

  // 🔑 监听主选对象的属性变化，同步到其他对象
  $effect(() => {
    if (!primaryObject || !isMultiSelection) return;

    // 检查每个数值属性的变化
    const numericProperties = ['x', 'y', 'width', 'height', 'anchorX', 'anchorY', 'scaleX', 'scaleY', 'rotation', 'alpha'];

    numericProperties.forEach(prop => {
      const currentValue = (primaryObject as any)[prop];
      const previousValue = previousValues[prop];

      if (currentValue !== previousValue && previousValue !== undefined) {
        const delta = currentValue - previousValue;

        if (Math.abs(delta) > 0.001) { // 避免浮点数精度问题
          console.log(`🔄 检测到${prop}变化: ${previousValue} → ${currentValue} (Δ${delta})`);
          applyDeltaToOtherObjects(prop, delta, currentValue);
        }
      }
    });

    // 检查布尔属性的变化
    const booleanProperties = ['visible'];
    booleanProperties.forEach(prop => {
      const currentValue = (primaryObject as any)[prop];
      const previousValue = previousValues[prop];

      if (currentValue !== previousValue && previousValue !== undefined) {
        console.log(`🔄 检测到${prop}变化: ${previousValue} → ${currentValue}`);
        applyValueToOtherObjects(prop, currentValue);
      }
    });

    // 更新上一次的值
    previousValues = {
      x: primaryObject.x,
      y: primaryObject.y,
      width: primaryObject.width,
      height: primaryObject.height,
      anchorX: primaryObject.anchorX,
      anchorY: primaryObject.anchorY,
      scaleX: primaryObject.scaleX,
      scaleY: primaryObject.scaleY,
      rotation: primaryObject.rotation,
      alpha: primaryObject.alpha,
      visible: primaryObject.visible
    };
  });

  // 🔑 将增量应用到其他选中对象（数值属性）
  function applyDeltaToOtherObjects(propertyName: string, delta: number, newPrimaryValue: any) {
    const otherObjects = selectedObjects.slice(1); // 排除主选对象

    if (otherObjects.length === 0) return;

    // 开始历史记录组
    historyManager.startGroup(
      `批量修改${propertyName}`,
      `主对象${propertyName}变化${delta}，同步到${otherObjects.length}个对象`
    );

    // 暂停自动记录，避免重复记录主对象的变化
    historyManager.pauseRecording();

    try {
      otherObjects.forEach((obj, index) => {
        const oldValue = (obj as any)[propertyName];
        let newValue;

        // 🔑 根据属性类型决定如何应用变化
        if (propertyName === 'scaleX' || propertyName === 'scaleY') {
          // 缩放属性：使用比例而不是增量
          const ratio = newPrimaryValue / (newPrimaryValue - delta);
          newValue = oldValue * ratio;
        } else if (propertyName === 'alpha') {
          // 透明度：确保在0-1范围内
          newValue = Math.max(0, Math.min(1, oldValue + delta));
        } else if (propertyName === 'anchorX' || propertyName === 'anchorY') {
          // 锚点：确保在0-1范围内
          newValue = Math.max(0, Math.min(1, oldValue + delta));
        } else {
          // 其他数值属性：直接加增量
          newValue = oldValue + delta;
        }

        console.log(`  📝 对象${index + 1}: ${propertyName} ${oldValue} → ${newValue} (Δ${delta})`);

        // 恢复记录，记录其他对象的变化
        historyManager.resumeRecording();
        (obj as any)[propertyName] = newValue;
        historyManager.pauseRecording();
      });
    } finally {
      // 恢复自动记录
      historyManager.resumeRecording();
      historyManager.endGroup();
    }
  }

  // 🔑 将值应用到其他选中对象（布尔属性）
  function applyValueToOtherObjects(propertyName: string, newValue: any) {
    const otherObjects = selectedObjects.slice(1);

    if (otherObjects.length === 0) return;

    historyManager.startGroup(
      `批量修改${propertyName}`,
      `同步${propertyName}到${otherObjects.length}个对象`
    );

    historyManager.pauseRecording();

    try {
      otherObjects.forEach((obj, index) => {
        const oldValue = (obj as any)[propertyName];
        console.log(`  📝 对象${index + 1}: ${propertyName} ${oldValue} → ${newValue}`);

        historyManager.resumeRecording();
        (obj as any)[propertyName] = newValue;
        historyManager.pauseRecording();
      });
    } finally {
      historyManager.resumeRecording();
      historyManager.endGroup();
    }
  }

  // 整个面板的展开状态
  let isExpanded = $state(true);
</script>

{#if primaryObject}
  <AccordionPanel
    title="基础属性"
    icon="⚙️"
    badge={isMultiSelection ? `${selectedObjects.length}个对象` : (primaryObject.className || '未知类型')}
    badgeVariant={isMultiSelection ? "active" : "info"}
    bind:expanded={isExpanded}
  >
    <!-- 🔑 多选提示 -->
    {#if isMultiSelection}
      <div class="multi-selection-hint">
        <p>🔄 正在编辑 {selectedObjects.length} 个对象</p>
        <p>修改主对象的属性，其他对象会自动跟随变化</p>
      </div>
    {/if}

    <!-- 🔑 名称属性 - 多选时禁用 -->
    <PropertyContainer>
      <Label text="名称:" />
      <SafeInput
        bind:value={primaryObject.name}
        type="text"
        placeholder={isMultiSelection ? "多选时不可编辑" : "组件名称"}
        disabled={isMultiSelection}
        class="flex-1"
      />
    </PropertyContainer>

    <!-- 位置属性 -->
    <PropertyContainer>
      <Label text="X:" />
      <LabelInput
        bind:value={primaryObject.x}
        type="number"
        step={1}
        targetObject={primaryObject}
        fieldName="x"
        name="X坐标"
      />
            <Label text="Y:" />
      <LabelInput
        bind:value={primaryObject.y}
        type="number"
        step={1}
        targetObject={primaryObject}
        fieldName="y"
        name="Y坐标"
      />
    </PropertyContainer>

    <!-- 尺寸属性 -->
    <PropertyContainer>
      <Label text="宽度:" />
      <LabelInput
        bind:value={primaryObject.width}
        type="number"
        step={1}
        targetObject={primaryObject}
        fieldName="width"
        name="宽度"
      />
            <Label text="高度:" />
      <LabelInput
        bind:value={primaryObject.height}
        type="number"
        step={1}
        targetObject={primaryObject}
        fieldName="height"
        name="高度"
      />
    </PropertyContainer>


    <!-- 锚点属性 -->
    <PropertyContainer>
      <Label text="锚点X:" />
      <LabelInput
        bind:value={primaryObject.anchorX}
        type="number"
        step={0.01}
        min={0}
        max={1}
        precision={2}
        targetObject={primaryObject}
        fieldName="anchorX"
        name="锚点X"
      />
            <Label text="锚点Y:" />
      <LabelInput
        bind:value={primaryObject.anchorY}
        type="number"
        step={0.01}
        min={0}
        max={1}
        precision={2}
        targetObject={primaryObject}
        fieldName="anchorY"
        name="锚点Y"
      />
    </PropertyContainer>

    <!-- 缩放属性 -->
    <PropertyContainer>
      <Label text="缩放X:" />
      <LabelInput
        bind:value={primaryObject.scaleX}
        type="number"
        step={0.1}
        min={0.1}
        max={3}
        precision={1}
        targetObject={primaryObject}
        fieldName="scaleX"
        name="X缩放"
      />
            <Label text="缩放Y:" />
      <LabelInput
        bind:value={primaryObject.scaleY}
        type="number"
        step={0.1}
        min={0.1}
        max={3}
        precision={1}
        targetObject={primaryObject}
        fieldName="scaleY"
        name="Y缩放"
      />
    </PropertyContainer>


    <!-- 旋转属性 -->
    <PropertyContainer>
      <Label text="旋转:" />
      <LabelInput
        bind:value={primaryObject.rotation}
        type="number"
        step={0.1}
        precision={1}
        targetObject={primaryObject}
        fieldName="rotation"
        name="旋转角度"
      />
    </PropertyContainer>

    <!-- 透明度属性 -->
    <PropertyContainer>
      <Label text="透明度:" />
      <Slider
        bind:value={primaryObject.alpha}
        min={0}
        max={1}
        step={0.01}
        targetObject={primaryObject}
        fieldName="alpha"
        name="透明度"
      />
      <Label text={primaryObject.alpha.toFixed(2)} size="sm" variant="secondary" />
    </PropertyContainer>

    <!-- 可见性属性 -->
    <PropertyContainer>
      <Label text="可见性:" />
      <Checkbox
        checked={primaryObject.visible}
        targetObject={primaryObject}
        fieldName="visible"
        name="可见性"
        onChange={(checked: boolean) => {
          console.log("🔧 ReactiveBasePropertyPanel: 可见性变化", {from: primaryObject.visible, to: checked});
          primaryObject.visible = checked;
        }}
      />
      <Label text={primaryObject.visible ? '显示' : '隐藏'} size="sm" variant="secondary" />
    </PropertyContainer>
  </AccordionPanel>
{:else}
  <div class="no-selection">
    <p>请选择一个对象来查看其属性</p>
  </div>
{/if}

<style>
  .multi-selection-hint {
    background: var(--theme-warning-light, #fff3cd);
    border: 1px solid var(--theme-warning, #ffc107);
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 12px;
  }

  .multi-selection-hint p {
    margin: 0;
    font-size: 11px;
    color: var(--theme-warning-dark, #856404);
    line-height: 1.4;
  }

  .multi-selection-hint p:first-child {
    font-weight: 600;
    margin-bottom: 4px;
  }

  .no-selection {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 16px;
    text-align: center;
    color: var(--theme-text-secondary, #718096);
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 6px;
    border: 2px dashed var(--theme-border, #e2e8f0);
  }

  .no-selection p {
    margin: 0;
    font-size: 12px;
  }


</style>
