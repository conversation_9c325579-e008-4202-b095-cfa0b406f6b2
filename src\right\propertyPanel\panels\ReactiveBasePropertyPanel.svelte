<script lang="ts">
  /**
   * 响应式基础属性面板 - 支持多选增量同步
   * 编辑主对象时，其他选中对象会自动跟随相同的变化量
   */

  import {
    sceneModelState
  } from '../../../stores/sceneModelStore';
  import { historyManager } from '../../../historyManager';
  import LabelInput from '../../../components/LabelInput.svelte';
  import SafeInput from '../../../components/SafeInput.svelte';
  import Slider from '../../../components/Slider.svelte';
  import Label from '../../../components/Label.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';

  // 监听选中的对象
  let currentState = $derived($sceneModelState);
  let selectedObjects = $derived(currentState.selectedObjects);
  let primaryObject = $derived(selectedObjects[0] || null);
  let isMultiSelection = $derived(selectedObjects.length > 1);

  // 🔑 存储拖拽开始时的初始值，用于实时同步计算
  let dragInitialValues = $state<Record<string, any>>({});



  // 🔑 事件驱动的多选同步函数
  function handlePropertyChange(propertyName: string, newValue: any, oldValue: any) {
    console.log(`🔧 handlePropertyChange 被调用:`, {
      propertyName,
      newValue,
      oldValue,
      isMultiSelection,
      selectedCount: selectedObjects.length,
      selectedObjects: selectedObjects.map(obj => obj.className)
    });

    if (!isMultiSelection || selectedObjects.length <= 1) {
      console.log(`❌ 跳过同步: isMultiSelection=${isMultiSelection}, selectedCount=${selectedObjects.length}`);
      return;
    }

    const delta = typeof newValue === 'number' && typeof oldValue === 'number'
      ? newValue - oldValue
      : null;

    console.log(`🔄 手动同步${propertyName}: ${oldValue} → ${newValue}`, delta ? `(Δ${delta})` : '');

    // 开始历史记录组
    historyManager.startGroup(
      `批量修改${propertyName}`,
      `主对象${propertyName}变化，同步到${selectedObjects.length - 1}个对象`
    );

    // 同步到其他对象
    const otherObjects = selectedObjects.slice(1);
    console.log(`🎯 要同步的其他对象:`, otherObjects.map(obj => obj.className));

    otherObjects.forEach((obj, index) => {
      const oldObjValue = (obj as any)[propertyName];
      let newObjValue;

      if (delta !== null) {
        // 数值属性：使用增量
        if (propertyName === 'scaleX' || propertyName === 'scaleY') {
          // 缩放：使用比例
          const ratio = newValue / oldValue;
          newObjValue = oldObjValue * ratio;
        } else if (propertyName === 'alpha' || propertyName === 'anchorX' || propertyName === 'anchorY') {
          // 范围限制属性
          newObjValue = Math.max(0, Math.min(1, oldObjValue + delta));
        } else {
          // 普通数值属性
          newObjValue = oldObjValue + delta;
        }
      } else {
        // 非数值属性：直接赋值
        newObjValue = newValue;
      }

      console.log(`  📝 对象${index + 1} (${obj.className}): ${propertyName} ${oldObjValue} → ${newObjValue}`);

      // 🔑 手动记录每个其他对象的历史变化
      console.log(`📝 手动记录历史: ${obj.className}.${propertyName} ${oldObjValue} → ${newObjValue}`);
      historyManager.recordChange(obj, propertyName, oldObjValue, newObjValue);

      // 🔑 暂停自动记录，避免重复记录
      historyManager.pauseRecording();
      (obj as any)[propertyName] = newObjValue;
      historyManager.resumeRecording();
    });

    historyManager.endGroup();

    // 🔍 调试：显示历史记录状态
    const historyState = historyManager.getState();
    console.log(`📊 历史记录状态:`, {
      canUndo: historyState.canUndo,
      canRedo: historyState.canRedo,
      lastOperation: historyState.lastOperation
    });
  }

  // 🔑 通用属性更新函数（拖拽完成时调用，带历史记录）
  function updateProperty(propertyName: string, newValue: any, initialValue?: any) {
    console.log(`🔧 updateProperty 被调用:`, {
      propertyName,
      newValue,
      initialValue,
      isMultiSelection,
      selectedCount: selectedObjects.length,
      primaryObject: primaryObject?.className
    });

    if (!primaryObject) {
      console.warn('❌ updateProperty: 没有主选对象');
      return;
    }

    // 🔑 使用传入的初始值，如果没有则使用当前值
    const oldValue = initialValue !== undefined ? initialValue : (primaryObject as any)[propertyName];
    console.log(`📝 属性变化: ${propertyName} ${oldValue} → ${newValue}`);

    // 🔑 不要在这里更新主对象，让 bind:value 自然更新
    // (primaryObject as any)[propertyName] = newValue;

    // 如果是多选，同步其他对象
    if (isMultiSelection && selectedObjects.length > 1) {
      // 🔑 检查是否已经通过 onMove 进行了实时同步
      const hasRealTimeSync = dragInitialValues[propertyName] !== undefined;

      if (hasRealTimeSync) {
        console.log(`🔄 最终同步：记录实时同步的历史`);
        // 🔑 为实时同步的其他对象记录历史
        handlePropertyChangeForRealTimeSync(propertyName, newValue, oldValue);
      } else {
        console.log(`🔄 开始多选同步（无实时同步）: ${selectedObjects.length - 1} 个其他对象`);
        // 🔑 对于没有实时同步的组件（如 Slider、Checkbox），需要手动记录主对象历史
        handlePropertyChangeWithPrimaryObject(propertyName, newValue, oldValue);
      }
    } else {
      console.log(`ℹ️ 单选模式，不需要同步其他对象`);
    }

    // 🔑 清理拖拽状态
    clearDragState(propertyName);
  }

  // 🔑 拖拽过程中的实时同步函数（不记录历史）
  function updatePropertyMove(propertyName: string, newValue: any, initialValue?: any) {
    if (!primaryObject || !isMultiSelection || selectedObjects.length <= 1) {
      return;
    }

    // 使用传入的初始值计算增量
    const oldValue = initialValue !== undefined ? initialValue : (primaryObject as any)[propertyName];
    const delta = typeof newValue === 'number' && typeof oldValue === 'number'
      ? newValue - oldValue
      : null;

    // 同步到其他对象（不记录历史）
    const otherObjects = selectedObjects.slice(1);

    // 🔑 存储其他对象的初始值（如果还没有存储）
    if (!dragInitialValues[propertyName]) {
      dragInitialValues[propertyName] = otherObjects.map(obj => (obj as any)[propertyName]);
    }

    otherObjects.forEach((obj, index) => {
      const objInitialValue = dragInitialValues[propertyName][index];
      let newObjValue;

      if (delta !== null) {
        // 数值属性：使用增量
        if (propertyName === 'scaleX' || propertyName === 'scaleY') {
          // 缩放：使用比例
          const ratio = newValue / oldValue;
          newObjValue = objInitialValue * ratio;
        } else if (propertyName === 'alpha' || propertyName === 'anchorX' || propertyName === 'anchorY') {
          // 范围限制属性
          newObjValue = Math.max(0, Math.min(1, objInitialValue + delta));
        } else {
          // 普通数值属性
          newObjValue = objInitialValue + delta;
        }
      } else {
        // 非数值属性：直接赋值
        newObjValue = newValue;
      }

      // 直接更新，不记录历史
      (obj as any)[propertyName] = newObjValue;
    });
  }

  // 🔑 为实时同步的对象记录历史（不再更新值，只记录历史）
  function handlePropertyChangeForRealTimeSync(propertyName: string, newValue: any, oldValue: any) {
    if (!isMultiSelection || selectedObjects.length <= 1) return;

    const delta = typeof newValue === 'number' && typeof oldValue === 'number'
      ? newValue - oldValue
      : null;

    console.log(`🔄 记录实时同步历史${propertyName}: ${oldValue} → ${newValue}`, delta ? `(Δ${delta})` : '');

    // 开始历史记录组
    historyManager.startGroup(
      `批量修改${propertyName}`,
      `主对象${propertyName}变化，同步到${selectedObjects.length - 1}个对象`
    );

    // 🔑 首先记录主对象的历史
    console.log(`📝 记录主对象历史: ${primaryObject?.className}.${propertyName} ${oldValue} → ${newValue}`);
    if (primaryObject) {
      historyManager.recordChange(primaryObject, propertyName, oldValue, newValue);
    }

    // 为其他对象记录历史（使用存储的初始值）
    const otherObjects = selectedObjects.slice(1);
    const initialValues = dragInitialValues[propertyName];

    if (initialValues) {
      otherObjects.forEach((obj, index) => {
        const objInitialValue = initialValues[index];
        const objCurrentValue = (obj as any)[propertyName];

        console.log(`📝 记录其他对象历史: ${obj.className}.${propertyName} ${objInitialValue} → ${objCurrentValue}`);
        historyManager.recordChange(obj, propertyName, objInitialValue, objCurrentValue);
      });
    }

    historyManager.endGroup();
  }

  // 🔑 处理没有实时同步的组件（如 Slider、Checkbox）的多选同步
  function handlePropertyChangeWithPrimaryObject(propertyName: string, newValue: any, oldValue: any) {
    if (!isMultiSelection || selectedObjects.length <= 1) return;

    const delta = typeof newValue === 'number' && typeof oldValue === 'number'
      ? newValue - oldValue
      : null;

    console.log(`🔄 多选同步（含主对象）${propertyName}: ${oldValue} → ${newValue}`, delta ? `(Δ${delta})` : '');

    // 开始历史记录组
    historyManager.startGroup(
      `批量修改${propertyName}`,
      `主对象${propertyName}变化，同步到${selectedObjects.length - 1}个对象`
    );

    // 🔑 首先记录主对象的历史
    console.log(`📝 记录主对象历史: ${primaryObject?.className}.${propertyName} ${oldValue} → ${newValue}`);
    if (primaryObject) {
      historyManager.recordChange(primaryObject, propertyName, oldValue, newValue);
    }

    // 同步并记录其他对象
    const otherObjects = selectedObjects.slice(1);
    console.log(`🎯 要同步的其他对象:`, otherObjects.map(obj => obj.className));

    otherObjects.forEach((obj, index) => {
      const oldObjValue = (obj as any)[propertyName];
      let newObjValue;

      if (delta !== null) {
        // 数值属性：使用增量
        if (propertyName === 'scaleX' || propertyName === 'scaleY') {
          // 缩放：使用比例
          const ratio = newValue / oldValue;
          newObjValue = oldObjValue * ratio;
        } else if (propertyName === 'alpha' || propertyName === 'anchorX' || propertyName === 'anchorY') {
          // 范围限制属性
          newObjValue = Math.max(0, Math.min(1, oldObjValue + delta));
        } else {
          // 普通数值属性
          newObjValue = oldObjValue + delta;
        }
      } else {
        // 非数值属性：直接赋值
        newObjValue = newValue;
      }

      console.log(`📝 记录其他对象历史: ${obj.className}.${propertyName} ${oldObjValue} → ${newObjValue}`);

      // 🔑 手动记录每个其他对象的历史变化
      historyManager.recordChange(obj, propertyName, oldObjValue, newObjValue);

      // 🔑 暂停自动记录，避免重复记录
      historyManager.pauseRecording();
      (obj as any)[propertyName] = newObjValue;
      historyManager.resumeRecording();
    });

    historyManager.endGroup();
  }

  // 🔑 清理拖拽状态
  function clearDragState(propertyName: string) {
    delete dragInitialValues[propertyName];
  }

  // 整个面板的展开状态
  let isExpanded = $state(true);
</script>

{#if primaryObject}
  <AccordionPanel
    title="基础属性"
    icon="⚙️"
    badge={isMultiSelection ? `${selectedObjects.length}个对象` : (primaryObject.className || '未知类型')}
    badgeVariant={isMultiSelection ? "active" : "info"}
    bind:expanded={isExpanded}
  >
    <!-- 🔑 多选提示 -->
    {#if isMultiSelection}
      <div class="multi-selection-hint">
        <p>🔄 正在编辑 {selectedObjects.length} 个对象</p>
        <p>修改主对象的属性，其他对象会自动跟随变化</p>
      </div>
    {/if}

    <!-- 🔑 名称属性 - 多选时禁用 -->
    <PropertyContainer>
      <Label text="名称:" />
      <SafeInput
        bind:value={primaryObject.name}
        type="text"
        placeholder={isMultiSelection ? "多选时不可编辑" : "组件名称"}
        disabled={isMultiSelection}
        class="flex-1"
      />
    </PropertyContainer>

    <!-- 位置属性 -->
    <PropertyContainer>
      <Label text="X:" />
      <LabelInput
        bind:value={primaryObject.x}
        type="number"
        step={1}
        targetObject={isMultiSelection ? undefined : primaryObject}
        fieldName="x"
        name="X坐标"
        enableHistory={!isMultiSelection}
        onChange={(newValue: any, _event: any, initialValue?: any) => updateProperty('x', newValue, initialValue)}
        onMove={(newValue: any, initialValue?: any) => updatePropertyMove('x', newValue, initialValue)}
      />
      <Label text="Y:" />
      <LabelInput
        bind:value={primaryObject.y}
        type="number"
        step={1}
        targetObject={isMultiSelection ? undefined : primaryObject}
        fieldName="y"
        name="Y坐标"
        enableHistory={!isMultiSelection}
        onChange={(newValue: any, _event: any, initialValue?: any) => updateProperty('y', newValue, initialValue)}
        onMove={(newValue: any, initialValue?: any) => updatePropertyMove('y', newValue, initialValue)}
      />
    </PropertyContainer>

    <!-- 尺寸属性 -->
    <PropertyContainer>
      <Label text="宽度:" />
      <LabelInput
        bind:value={primaryObject.width}
        type="number"
        step={1}
        targetObject={isMultiSelection ? undefined : primaryObject}
        fieldName="width"
        name="宽度"
        enableHistory={!isMultiSelection}
        onChange={(newValue: any, _event: any, initialValue?: any) => updateProperty('width', newValue, initialValue)}
        onMove={(newValue: any, initialValue?: any) => updatePropertyMove('width', newValue, initialValue)}
      />
      <Label text="高度:" />
      <LabelInput
        bind:value={primaryObject.height}
        type="number"
        step={1}
        targetObject={isMultiSelection ? undefined : primaryObject}
        fieldName="height"
        name="高度"
        enableHistory={!isMultiSelection}
        onChange={(newValue: any, _event: any, initialValue?: any) => updateProperty('height', newValue, initialValue)}
        onMove={(newValue: any, initialValue?: any) => updatePropertyMove('height', newValue, initialValue)}
      />
    </PropertyContainer>


    <!-- 锚点属性 -->
    <PropertyContainer>
      <Label text="锚点X:" />
      <LabelInput
        bind:value={primaryObject.anchorX}
        type="number"
        step={0.01}
        min={0}
        max={1}
        precision={2}
        targetObject={isMultiSelection ? undefined : primaryObject}
        fieldName="anchorX"
        name="锚点X"
        enableHistory={!isMultiSelection}
        onChange={(newValue: any, _event: any, initialValue?: any) => updateProperty('anchorX', newValue, initialValue)}
        onMove={(newValue: any, initialValue?: any) => updatePropertyMove('anchorX', newValue, initialValue)}
      />
      <Label text="锚点Y:" />
      <LabelInput
        bind:value={primaryObject.anchorY}
        type="number"
        step={0.01}
        min={0}
        max={1}
        precision={2}
        targetObject={isMultiSelection ? undefined : primaryObject}
        fieldName="anchorY"
        name="锚点Y"
        enableHistory={!isMultiSelection}
        onChange={(newValue: any, _event: any, initialValue?: any) => updateProperty('anchorY', newValue, initialValue)}
        onMove={(newValue: any, initialValue?: any) => updatePropertyMove('anchorY', newValue, initialValue)}
      />
    </PropertyContainer>

    <!-- 缩放属性 -->
    <PropertyContainer>
      <Label text="缩放X:" />
      <LabelInput
        bind:value={primaryObject.scaleX}
        type="number"
        step={0.1}
        min={0.1}
        max={3}
        precision={1}
        targetObject={isMultiSelection ? undefined : primaryObject}
        fieldName="scaleX"
        name="X缩放"
        enableHistory={!isMultiSelection}
        onChange={(newValue: any, _event: any, initialValue?: any) => updateProperty('scaleX', newValue, initialValue)}
        onMove={(newValue: any, initialValue?: any) => updatePropertyMove('scaleX', newValue, initialValue)}
      />
      <Label text="缩放Y:" />
      <LabelInput
        bind:value={primaryObject.scaleY}
        type="number"
        step={0.1}
        min={0.1}
        max={3}
        precision={1}
        targetObject={isMultiSelection ? undefined : primaryObject}
        fieldName="scaleY"
        name="Y缩放"
        enableHistory={!isMultiSelection}
        onChange={(newValue: any, _event: any, initialValue?: any) => updateProperty('scaleY', newValue, initialValue)}
        onMove={(newValue: any, initialValue?: any) => updatePropertyMove('scaleY', newValue, initialValue)}
      />
    </PropertyContainer>

    <!-- 旋转属性 -->
    <PropertyContainer>
      <Label text="旋转:" />
      <LabelInput
        bind:value={primaryObject.rotation}
        type="number"
        step={0.1}
        precision={1}
        targetObject={isMultiSelection ? undefined : primaryObject}
        fieldName="rotation"
        name="旋转角度"
        enableHistory={!isMultiSelection}
        onChange={(newValue: any, _event: any, initialValue?: any) => updateProperty('rotation', newValue, initialValue)}
        onMove={(newValue: any, initialValue?: any) => updatePropertyMove('rotation', newValue, initialValue)}
      />
    </PropertyContainer>

    <!-- 透明度属性 -->
    <PropertyContainer>
      <Label text="透明度:" />
      <Slider
        bind:value={primaryObject.alpha}
        min={0}
        max={1}
        step={0.01}
        targetObject={isMultiSelection ? undefined : primaryObject}
        fieldName="alpha"
        name="透明度"
        enableHistory={!isMultiSelection}
        onChange={(newValue: any, _event: any, initialValue?: any) => updateProperty('alpha', newValue, initialValue)}
        onMove={(newValue: any, initialValue?: any) => updatePropertyMove('alpha', newValue, initialValue)}
      />
      <Label text={primaryObject.alpha.toFixed(2)} size="sm" variant="secondary" />
    </PropertyContainer>

    <!-- 可见性属性 -->
    <PropertyContainer>
      <Label text="可见性:" />
      <Checkbox
        bind:checked={primaryObject.visible}
        targetObject={isMultiSelection ? undefined : primaryObject}
        fieldName="visible"
        name="可见性"
        enableHistory={!isMultiSelection}
        onChange={(checked: boolean, _event: any, initialValue?: boolean) => {
          console.log("🔧 ReactiveBasePropertyPanel: 可见性变化", {
            from: initialValue,
            to: checked,
            selectedObjects: selectedObjects.map(obj => ({className: obj.className, visible: obj.visible}))
          });
          updateProperty('visible', checked, initialValue);
        }}
      />
      <Label text={primaryObject.visible ? '显示' : '隐藏'} size="sm" variant="secondary" />
    </PropertyContainer>
  </AccordionPanel>
{:else}
  <div class="no-selection">
    <p>请选择一个对象来查看其属性</p>
  </div>
{/if}

<style>
  .multi-selection-hint {
    background: var(--theme-warning-light, #fff3cd);
    border: 1px solid var(--theme-warning, #ffc107);
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 12px;
  }

  .multi-selection-hint p {
    margin: 0;
    font-size: 11px;
    color: var(--theme-warning-dark, #856404);
    line-height: 1.4;
  }

  .multi-selection-hint p:first-child {
    font-weight: 600;
    margin-bottom: 4px;
  }

  .no-selection {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 16px;
    text-align: center;
    color: var(--theme-text-secondary, #718096);
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 6px;
    border: 2px dashed var(--theme-border, #e2e8f0);
  }

  .no-selection p {
    margin: 0;
    font-size: 12px;
  }


</style>
