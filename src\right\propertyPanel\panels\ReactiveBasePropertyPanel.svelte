<script lang="ts">
  /**
   * 响应式基础属性面板
   * 直接使用选中对象的响应式字段
   */

  import {
    sceneModelState
  } from '../../../stores/sceneModelStore';
  import LabelInput from '../../../components/LabelInput.svelte';
  import SafeInput from '../../../components/SafeInput.svelte';
  import Slider from '../../../components/Slider.svelte';
  import Label from '../../../components/Label.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';

  // 监听选中的对象 - 使用第一个选中的对象
  let currentState = $derived($sceneModelState);
  let primaryObject = $derived(currentState.selectedObjects[0] || null);

  // 🔧 添加调试信息
  $effect(() => {
    console.log('🎯 选择变化:', primaryObject);
    if (primaryObject === undefined) {
      console.warn('❌ primaryObject 是 undefined，这不应该发生');
    }
  });

  // 整个面板的展开状态
  let isExpanded = $state(true);
</script>

{#if primaryObject}
  <AccordionPanel
    title="基础属性"
    icon="⚙️"
    badge={primaryObject.className || '未知类型'}
    badgeVariant="info"
    bind:expanded={isExpanded}
  >
    <!-- 🔑 名称属性 -->
    <PropertyContainer>
      <Label text="名称:" />
      <SafeInput
        bind:value={primaryObject.name}
        type="text"
        placeholder="组件名称"
        class="flex-1"
      />
    </PropertyContainer>

    <!-- 位置属性 -->
    <PropertyContainer>
      <Label text="X:" />
      <LabelInput
        bind:value={primaryObject.x}
        type="number"
        step={1}
        targetObject={primaryObject}
        fieldName="x"
        name="X坐标"
      />
            <Label text="Y:" />
      <LabelInput
        bind:value={primaryObject.y}
        type="number"
        step={1}
        targetObject={primaryObject}
        fieldName="y"
        name="Y坐标"
      />
    </PropertyContainer>

    <!-- 尺寸属性 -->
    <PropertyContainer>
      <Label text="宽度:" />
      <LabelInput
        bind:value={primaryObject.width}
        type="number"
        step={1}
        targetObject={primaryObject}
        fieldName="width"
        name="宽度"
      />
            <Label text="高度:" />
      <LabelInput
        bind:value={primaryObject.height}
        type="number"
        step={1}
        targetObject={primaryObject}
        fieldName="height"
        name="高度"
      />
    </PropertyContainer>


    <!-- 锚点属性 -->
    <PropertyContainer>
      <Label text="锚点X:" />
      <LabelInput
        bind:value={primaryObject.anchorX}
        type="number"
        step={0.01}
        min={0}
        max={1}
        precision={2}
        targetObject={primaryObject}
        fieldName="anchorX"
        name="锚点X"
      />
            <Label text="锚点Y:" />
      <LabelInput
        bind:value={primaryObject.anchorY}
        type="number"
        step={0.01}
        min={0}
        max={1}
        precision={2}
        targetObject={primaryObject}
        fieldName="anchorY"
        name="锚点Y"
      />
    </PropertyContainer>

    <!-- 缩放属性 -->
    <PropertyContainer>
      <Label text="缩放X:" />
      <LabelInput
        bind:value={primaryObject.scaleX}
        type="number"
        step={0.1}
        min={0.1}
        max={3}
        precision={1}
        targetObject={primaryObject}
        fieldName="scaleX"
        name="X缩放"
      />
            <Label text="缩放Y:" />
      <LabelInput
        bind:value={primaryObject.scaleY}
        type="number"
        step={0.1}
        min={0.1}
        max={3}
        precision={1}
        targetObject={primaryObject}
        fieldName="scaleY"
        name="Y缩放"
      />
    </PropertyContainer>


    <!-- 旋转属性 -->
    <PropertyContainer>
      <Label text="旋转:" />
      <LabelInput
        bind:value={primaryObject.rotation}
        type="number"
        step={0.1}
        precision={1}
        targetObject={primaryObject}
        fieldName="rotation"
        name="旋转角度"
      />
    </PropertyContainer>

    <!-- 透明度属性 -->
    <PropertyContainer>
      <Label text="透明度:" />
      <Slider
        bind:value={primaryObject.alpha}
        min={0}
        max={1}
        step={0.01}
        targetObject={primaryObject}
        fieldName="alpha"
        name="透明度"
      />
      <Label text={primaryObject.alpha.toFixed(2)} size="sm" variant="secondary" />
    </PropertyContainer>

    <!-- 可见性属性 -->
    <PropertyContainer>
      <Label text="可见性:" />
      <Checkbox
        checked={primaryObject.visible}
        targetObject={primaryObject}
        fieldName="visible"
        name="可见性"
        onChange={(checked: boolean) => {
          console.log("🔧 ReactiveBasePropertyPanel: 可见性变化", {from: primaryObject.visible, to: checked});
          primaryObject.visible = checked;
        }}
      />
      <Label text={primaryObject.visible ? '显示' : '隐藏'} size="sm" variant="secondary" />
    </PropertyContainer>
  </AccordionPanel>
{:else}
  <div class="no-selection">
    <p>请选择一个对象来查看其属性</p>
  </div>
{/if}

<style>


  .no-selection {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 16px;
    text-align: center;
    color: var(--theme-text-secondary, #718096);
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 6px;
    border: 2px dashed var(--theme-border, #e2e8f0);
  }

  .no-selection p {
    margin: 0;
    font-size: 12px;
  }


</style>
