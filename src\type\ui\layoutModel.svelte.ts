import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * 脚本接口定义
 */
interface Script {
    id: string;
    name: string;
    type: 'lifecycle' | 'interaction' | 'custom';
    enabled: boolean;
    code: string;
    description: string;
}

/**
 * LayoutModel - UILayout的模型对象
 * 继承自BaseObjectModel，管理UILayout的所有属性和状态
 */
export class LayoutModel extends BaseObjectModel {
    // 组件类型
    public readonly componentType = 'UILayout';

    // 🔑 布局类型
    layoutType = $state('vertical'); // 'vertical', 'horizontal', 'grid'

    // 🔑 间距设置
    spacing = $state(5);              // 统一间距
    horizontalSpacing = $state(5);    // 水平间距
    verticalSpacing = $state(5);      // 垂直间距
    padding = $state(0);              // 内边距

    // 🔑 网格布局参数
    columns = $state(2);              // 网格列数
    rows = $state(0);                 // 网格行数（0表示自动）

    // 🔑 对齐方式
    mainAxisAlignment = $state('start');     // 主轴对齐：start, center, end, space-between, space-around
    crossAxisAlignment = $state('start');    // 交叉轴对齐：start, center, end, stretch

    // 🔑 容器尺寸
    containerWidth = $state(0);       // 容器宽度（0表示自动）
    containerHeight = $state(0);      // 容器高度（0表示自动）

    // 🔑 自动更新设置
    autoUpdate = $state(true);        // 是否自动更新布局

    // 🔑 高级设置
    wrapContent = $state(false);      // 是否自动换行（未来功能）
    reverseOrder = $state(false);     // 是否反向排列

    // 🔑 滚动设置
    scrollEnabled = $state(true);     // 是否启用滚动
    scrollSpeed = $state(40);         // 滚动速度

    // 🔑 统一的脚本系统
    componentScripts = $state<Script[]>([]);   // 脚本数组

    // 🔑 模板绑定属性
    templateData = $state<any[]>([]);            // 模板数据数组
    isTemplateMode = $state<boolean>(false);     // 是否启用模板模式



    constructor(layout: any = {}) {
        super(layout);

        // 初始化布局属性
        this.layoutType = layout.layoutType || 'vertical';
        this.spacing = layout.spacing || 5;
        this.horizontalSpacing = layout.horizontalSpacing || layout.spacing || 5;
        this.verticalSpacing = layout.verticalSpacing || layout.spacing || 5;
        this.padding = layout.padding || 0;
        this.columns = layout.columns || 2;
        this.rows = layout.rows || 0;
        this.mainAxisAlignment = layout.mainAxisAlignment || 'start';
        this.crossAxisAlignment = layout.crossAxisAlignment || 'start';
        this.containerWidth = layout.containerWidth || 0;
        this.containerHeight = layout.containerHeight || 0;
        this.autoUpdate = layout.autoUpdate !== false;
        this.wrapContent = layout.wrapContent || false;
        this.reverseOrder = layout.reverseOrder || false;

        // 🔑 滚动设置
        this.scrollEnabled = layout.scrollEnabled !== false;
        this.scrollSpeed = layout.scrollSpeed || 40;

        // 🔑 统一的脚本系统 - 从原始对象获取脚本数组
        this.componentScripts = layout.componentScripts || [];

        // 🔑 脚本系统初始化 - 直接使用传入的脚本数组，不再重复初始化

        console.log('🔧 LayoutModel: 脚本系统初始化', {
            hasOriginalScripts: !!layout.componentScripts,
            scriptsCount: this.componentScripts.length,
            scriptNames: this.componentScripts.map(s => s.name)
        });

        // 🔑 模板绑定属性初始化
        this.templateData = layout.templateData || [];
        this.isTemplateMode = layout.isTemplateMode || false;

        console.log('📐 LayoutModel: 创建布局模型', this);
    }

    /**
     * 设置Layout特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Layout特有的属性
     */
    protected setupSpecificSync(): void {
        try {
            console.log('📐 LayoutModel: setupSpecificSync 被调用', {
                layoutType: this.layoutType,
                spacing: this.spacing,
                padding: this.padding,
                hasOriginalObject: !!this._originalObject
            });

            // 🔑 同步组件脚本数组
            if (this._originalObject.componentScripts !== this.componentScripts) {
                this._originalObject.componentScripts = this.componentScripts;
                console.log('🔧 LayoutModel: 同步组件脚本', this.componentScripts);
            }

            // 同步所有Layout特有属性
            this.syncLayoutProperties();

            // 🔑 同步滚动设置
            if (this._originalObject.scrollEnabled !== this.scrollEnabled) {
                this._originalObject.scrollEnabled = this.scrollEnabled;
            }
            if (this._originalObject.scrollSpeed !== this.scrollSpeed) {
                this._originalObject.scrollSpeed = this.scrollSpeed;
            }

            // 🔑 响应式监听原始对象的children变化
            // 通过访问 children.length 来建立响应式依赖
            const originalChildrenLength = this._originalObject?.children?.length || 0;

            // 这个比较会在每次 setupSpecificSync 被调用时执行
            // 由于 setupSpecificSync 在 $effect 中被调用，所以是响应式的
            if (originalChildrenLength !== this.children.length) {
                console.log('📐 LayoutModel: 响应式检测到子对象数量变化', {
                    originalCount: originalChildrenLength,
                    modelCount: this.children.length
                });
                this.syncChildrenFromOriginal();
            }

        } catch (error) {
            console.error('❌ LayoutModel: 特有属性同步失败', error);
        }
    }

    /**
     * 同步Layout属性到原始对象
     */
    private syncLayoutProperties(): void {
        if (!this._originalObject) return;

        // 同步布局类型
        if (this.layoutType !== undefined) {
            this._originalObject.layoutType = this.layoutType;
            console.log('📐 LayoutModel: 同步布局类型', this.layoutType);
        }

        // 同步间距 - 关键修复
        if (this.spacing !== undefined) {
            // 更新模型的间距属性，保持一致性
            this.horizontalSpacing = this.spacing;
            this.verticalSpacing = this.spacing;

            // 同步到原始对象
            this._originalObject.spacing = this.spacing;
            this._originalObject.horizontalSpacing = this.spacing;
            this._originalObject.verticalSpacing = this.spacing;

            console.log('📐 LayoutModel: 同步统一间距', {
                modelSpacing: this.spacing,
                objectSpacing: this._originalObject.spacing,
                objectHorizontalSpacing: this._originalObject.horizontalSpacing,
                objectVerticalSpacing: this._originalObject.verticalSpacing
            });
        } else {
            // 如果没有统一间距，则分别同步水平和垂直间距
            if (this.horizontalSpacing !== undefined) {
                this._originalObject.horizontalSpacing = this.horizontalSpacing;
                console.log('📐 LayoutModel: 同步水平间距', this.horizontalSpacing);
            }

            if (this.verticalSpacing !== undefined) {
                this._originalObject.verticalSpacing = this.verticalSpacing;
                console.log('📐 LayoutModel: 同步垂直间距', this.verticalSpacing);
            }
        }

        // 同步内边距
        if (this.padding !== undefined) {
            this._originalObject.padding = this.padding;
            console.log('📐 LayoutModel: 同步内边距', this.padding);
        }

        // 同步网格列数
        if (this.columns !== undefined) {
            this._originalObject.columns = this.columns;
            console.log('📐 LayoutModel: 同步网格列数', this.columns);
        }

        // 同步网格行数
        if (this.rows !== undefined) {
            this._originalObject.rows = this.rows;
            console.log('📐 LayoutModel: 同步网格行数', this.rows);
        }

        // 同步主轴对齐
        if (this.mainAxisAlignment !== undefined) {
            this._originalObject.mainAxisAlignment = this.mainAxisAlignment;
            console.log('📐 LayoutModel: 同步主轴对齐', this.mainAxisAlignment);
        }

        // 同步交叉轴对齐
        if (this.crossAxisAlignment !== undefined) {
            this._originalObject.crossAxisAlignment = this.crossAxisAlignment;
            console.log('📐 LayoutModel: 同步交叉轴对齐', this.crossAxisAlignment);
        }

        // 同步容器宽度
        if (this.containerWidth !== undefined) {
            // 🔧 确保容器宽度不为负数
            const safeWidth = Math.max(0, this.containerWidth);
            this._originalObject.containerWidth = safeWidth;
            if (safeWidth !== this.containerWidth) {
                console.warn('📐 LayoutModel: 容器宽度被修正', this.containerWidth, '→', safeWidth);
                this.containerWidth = safeWidth;
            }
            console.log('📐 LayoutModel: 同步容器宽度', safeWidth);
        }

        // 同步容器高度
        if (this.containerHeight !== undefined) {
            // 🔧 确保容器高度不为负数
            const safeHeight = Math.max(0, this.containerHeight);
            this._originalObject.containerHeight = safeHeight;
            if (safeHeight !== this.containerHeight) {
                console.warn('📐 LayoutModel: 容器高度被修正', this.containerHeight, '→', safeHeight);
                this.containerHeight = safeHeight;
            }
            console.log('📐 LayoutModel: 同步容器高度', safeHeight);
        }

        // 同步自动更新设置
        if (this.autoUpdate !== undefined) {
            this._originalObject.autoUpdate = this.autoUpdate;
            console.log('📐 LayoutModel: 同步自动更新设置', this.autoUpdate);
        }

        // 🔑 同步组件脚本
        if (this._originalObject.componentScript !== this.componentScript) {
            this._originalObject.componentScript = this.componentScript;
            console.log('🔧 LayoutModel: 同步组件脚本', this.componentScript);
        }

        // 🔑 同步模板绑定属性
        if (this._originalObject.templateData !== this.templateData) {
            this._originalObject.templateData = this.templateData;
            console.log('🔧 LayoutModel: 同步模板数据', this.templateData);
        }
        if (this._originalObject.isTemplateMode !== this.isTemplateMode) {
            this._originalObject.isTemplateMode = this.isTemplateMode;
            console.log('🔧 LayoutModel: 同步模板模式', this.isTemplateMode);
        }

        // 触发布局更新
        this.triggerLayoutUpdate();
    }

    /**
     * 触发布局更新
     */
    private triggerLayoutUpdate(): void {
        if (this._originalObject && typeof this._originalObject.updateLayout === 'function') {
            console.log('📐 LayoutModel: 触发布局更新', {
                layoutType: this._originalObject.layoutType,
                spacing: this._originalObject.spacing,
                padding: this._originalObject.padding
            });
            this._originalObject.updateLayout();
        } else {
            console.warn('📐 LayoutModel: 无法触发布局更新', {
                hasOriginalObject: !!this._originalObject,
                hasUpdateMethod: !!(this._originalObject && typeof this._originalObject.updateLayout === 'function')
            });
        }
    }

    /**
     * 🔑 从原始对象同步子对象到模型
     */
    public syncChildrenFromOriginal(): void {
        if (!this._originalObject) return;

        const originalChildren = this._originalObject.children || [];
        const currentModelChildren = this.children.length;

        // 只有当子对象数量发生变化时才同步
        if (originalChildren.length === currentModelChildren) {
            return;
        }

        console.log('📐 LayoutModel: 检测到子对象数量变化，开始同步', {
            originalCount: originalChildren.length,
            modelCount: currentModelChildren
        });

        // 🔑 保存模板对象（第一个子对象，通常是UIItem）
        const templateChild = this.children.find(child => {
            const childOriginal = child.getOriginalObject();
            return childOriginal && childOriginal._isTemplate;
        });

        // 重新创建子对象模型
        const newChildren = this.createChildrenModels(originalChildren);

        // 🔑 如果有模板对象，确保它被保留并正确设置父级关系
        if (templateChild) {
            const templateIndex = newChildren.findIndex(child => {
                const childOriginal = child.getOriginalObject();
                return childOriginal && childOriginal._isTemplate;
            });

            if (templateIndex >= 0) {
                console.log('📐 LayoutModel: 保留现有模板对象');
                newChildren[templateIndex] = templateChild;
                templateChild.parent = this;
            }
        }

        // 更新children数组
        this.children = newChildren;

        console.log('📐 LayoutModel: 子对象同步完成', {
            newCount: this.children.length,
            hasTemplate: !!templateChild
        });
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 创建 UILayout 对象，包含所有必要属性
        codes.push(`${indent}const ${varName} = new UILayout({`);

        // 🔑 基础布局属性
        codes.push(`${indent}    layoutType: '${this.layoutType}',`);
        codes.push(`${indent}    spacing: ${this.spacing},`);
        codes.push(`${indent}    horizontalSpacing: ${this.horizontalSpacing},`);
        codes.push(`${indent}    verticalSpacing: ${this.verticalSpacing},`);
        codes.push(`${indent}    padding: ${this.padding},`);
        codes.push(`${indent}    columns: ${this.columns},`);
        codes.push(`${indent}    rows: ${this.rows},`);
        codes.push(`${indent}    mainAxisAlignment: '${this.mainAxisAlignment}',`);
        codes.push(`${indent}    crossAxisAlignment: '${this.crossAxisAlignment}',`);
        codes.push(`${indent}    width: ${this.containerWidth},`);
        codes.push(`${indent}    height: ${this.containerHeight},`);
        codes.push(`${indent}    autoUpdate: ${this.autoUpdate},`);

        // 🔑 滚动设置
        codes.push(`${indent}    scrollEnabled: ${this.scrollEnabled},`);
        codes.push(`${indent}    scrollSpeed: ${this.scrollSpeed},`);

        // 🔑 生成组件脚本数组（如果有的话）
        if (this.componentScripts && this._hasNonEmptyScripts()) {
            codes.push(`${indent}    componentScripts: ${JSON.stringify(this.componentScripts, null, 8).replace(/\n/g, `\n${indent}`)},`);
        }

        // 🔑 高级设置（如果不是默认值）
        if (this.wrapContent) {
            codes.push(`${indent}    wrapContent: ${this.wrapContent},`);
        }
        if (this.reverseOrder) {
            codes.push(`${indent}    reverseOrder: ${this.reverseOrder},`);
        }

        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 🔑 检查是否有非空的脚本内容
     */
    private _hasNonEmptyScripts(): boolean {
        if (!this.componentScripts || this.componentScripts.length === 0) return false;

        // 检查是否有启用且有代码的脚本
        return this.componentScripts.some(script =>
            script.enabled && script.code && script.code.trim().length > 0
        );
    }

    /**
     * 克隆当前Layout对象 - 调用插件的 clone 方法
     */
    clone(): LayoutModel {
        console.log('🔄 LayoutModel: 开始克隆Layout对象（调用插件方法）');

        // 1. 调用原始 UILayout 对象的 clone 方法
        const originalUILayout = this.getOriginalObject();
        if (!originalUILayout || typeof originalUILayout.clone !== 'function') {
            console.error('❌ LayoutModel: 原始对象没有 clone 方法');
            throw new Error('UILayout 对象缺少 clone 方法');
        }

        console.log('🔄 LayoutModel: 原始对象子对象数量', originalUILayout.children?.length || 0);

        // 2. 使用插件的 clone 方法克隆原始对象（包括所有子对象）
        const clonedUILayout = originalUILayout.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        console.log('🔄 LayoutModel: 克隆后对象子对象数量', clonedUILayout.children?.length || 0);

        // 3. 🔑 创建新的 LayoutModel 包装克隆的对象
        // LayoutModel 构造函数会自动根据 clonedUILayout.children 创建子对象模型
        const clonedModel = new LayoutModel(clonedUILayout);

        console.log('✅ LayoutModel: 克隆完成，自动创建了', clonedModel.children.length, '个子对象模型');

        // 🔧 调试信息：输出子对象类型
        clonedModel.children.forEach((child, index) => {
            console.log(`  - 子对象 [${index}]: ${child.className}`);
        });

        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UILayout 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UILayout 特有属性
            layoutType: this.layoutType,
            spacing: this.spacing,
            horizontalSpacing: this.horizontalSpacing,
            verticalSpacing: this.verticalSpacing,
            padding: this.padding,
            columns: this.columns,
            rows: this.rows,
            mainAxisAlignment: this.mainAxisAlignment,
            crossAxisAlignment: this.crossAxisAlignment,
            containerWidth: this.containerWidth,
            containerHeight: this.containerHeight,
            autoUpdate: this.autoUpdate,
            wrapContent: this.wrapContent,
            reverseOrder: this.reverseOrder,
            // 🔑 克隆脚本数组
            componentScripts: this.componentScripts ?
                this.componentScripts.map(script => ({ ...script })) : []
        };
    }

    /**
     * 获取布局信息摘要
     */
    getLayoutInfo(): any {
        return {
            layoutType: this.layoutType,
            spacing: this.spacing,
            horizontalSpacing: this.horizontalSpacing,
            verticalSpacing: this.verticalSpacing,
            padding: this.padding,
            columns: this.columns,
            rows: this.rows,
            mainAxisAlignment: this.mainAxisAlignment,
            crossAxisAlignment: this.crossAxisAlignment,
            containerWidth: this.containerWidth,
            containerHeight: this.containerHeight,
            autoUpdate: this.autoUpdate,
            wrapContent: this.wrapContent,
            reverseOrder: this.reverseOrder,

        };
    }

    /**
     * 🔑 重写子对象创建代码生成，跳过动态添加的 UIItem 实例
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 子对象创建代码
     */
    protected generateChildrenCreation(varName: string, indent: string): string {
        if (this.children.length === 0) {
            return '';
        }

        // 🔑 过滤掉动态添加的 UIItem 实例
        const staticChildren = this.children.filter(child => {
            const childOriginal = child.getOriginalObject();

            // 检查是否是动态添加的 UIItem
            if (childOriginal.constructor.name === 'UIItem' || childOriginal.uiComponentType === 'UIItem') {
                // 如果这个 UIItem 是某个 UIList 的 itemTemplate，保留它
                // 否则认为是动态添加的实例，跳过
                const isTemplate = this.isItemTemplate(childOriginal);
                if (!isTemplate) {
                    console.log('🔍 LayoutModel: 跳过动态添加的 UIItem 实例:', child.className);
                    return false;
                }
            }

            return true;
        });

        if (staticChildren.length === 0) {
            return `${indent}// UILayout: 无静态子对象，动态子对象将在运行时添加`;
        }

        const codes: string[] = [];
        codes.push(`${indent}// 添加静态子对象`);

        staticChildren.forEach((child, index) => {
            const childVarName = `${varName}_child${index}`;

            // 生成子对象创建代码
            codes.push(child.generateCreationCode(childVarName, indent));

            // 添加到布局容器
            codes.push(`${indent}${varName}.addChild(${childVarName});`);
        });

        return codes.join('\n');
    }

    /**
     * 🔑 检查 UIItem 是否是某个 UIList 的模板
     * @param uiItem UIItem 对象
     * @returns 是否是模板
     */
    private isItemTemplate(uiItem: any): boolean {
        // 简单的启发式检查：如果 UIItem 的父对象是 UIList，且是 itemTemplate，则保留
        // 这里可以根据实际需求调整判断逻辑
        const parent = uiItem.parent;
        if (parent && (parent.constructor.name === 'UIList' || parent.uiComponentType === 'UIList')) {
            return parent.itemTemplate === uiItem;
        }
        return false;
    }
}

// 注册LayoutModel到基类容器
BaseObjectModel.registerModel('UILayout', LayoutModel);
BaseObjectModel.registerModel('Layout', LayoutModel);
