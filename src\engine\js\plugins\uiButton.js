/*:
 * @target MZ
 * @plugindesc UIButton v2.0.0
 * <AUTHOR>
 * @version 2.0.0
 * @description 可交互的按钮UI组件，继承自PIXI.Container
 *
 * @help uiButton.js
 *
 * 使用方法：
 * const button = new UIButton({
 *   x: 100,
 *   y: 100,
 *   width: 120,
 *   height: 40,
 *   frames: {
 *     default: { image: 'button_normal.png' },
 *     hover: { image: 'button_hover.png' },
 *     pressed: { image: 'button_pressed.png' }
 *   },
 *   onClick: 'SceneManager.goto(Scene_Map);'
 * });
 */

(() => {
    'use strict';

    /**
     * UIButton - 可交互的按钮UI组件，继承自PIXI.Container
     */
    class UIButton extends PIXI.Container {
        constructor(properties = {}) {
            super();

            console.log('🔘 UIButton: 创建按钮（继承自PIXI.Container）', properties);

            // 标识为UI组件
            this.isUIComponent = true;
            this.uiComponentType = 'UIButton';

            // 🔑 使用UIComponentUtils添加UIComponent功能
            if (window.UIComponentUtils) {
                window.UIComponentUtils.applyToSprite(this, properties);
            }

            // 🔑 使用UIScriptManager添加脚本功能
            if (window.UIScriptManager) {
                window.UIScriptManager.applyToObject(this, properties);
            }

            // 初始化按钮组件
            this.initializeButton(properties);

            // 🔑 标记为已创建，onStart会在添加到父容器时执行
            this._isCreated = true;
        }

        /**
         * 初始化按钮组件
         * @param {Object} properties 按钮属性
         */
        initializeButton(properties) {
            // 基础属性
            this.x = properties.x || 0;
            this.y = properties.y || 0;
            this.buttonWidth = properties.width || 120;
            this.buttonHeight = properties.height || 40;
            this.enabled = properties.enabled !== false;

            // 🔑 子组件绑定属性（通过属性面板设置）
            this.boundDefaultSprite = properties.boundDefaultSprite || null;   // 绑定的默认状态精灵 (UIImage)
            this.boundHoverSprite = properties.boundHoverSprite || null;       // 绑定的悬停状态精灵 (UIImage)
            this.boundPressedSprite = properties.boundPressedSprite || null;   // 绑定的按下状态精灵 (UIImage)

            // 🔑 事件执行控制（默认禁用，可在属性面板中启用用于调试）
            this.executeEventsInEditor = false;

            // 状态管理
            this.currentState = 'default';
            this._pressed = false;
            this._hovered = false;

            // 行为属性
            this.enabled = properties.enabled !== false;

            // 初始化容器
            this.initializeContainer();

            // 设置事件监听器（使用RPG Maker MZ的TouchInput系统）
            this.setupEventListeners();

            // 初始更新
            this.updateButton();

            console.log('✅ UIButton: 按钮创建完成');
        }



        /**
         * 初始化容器（Container 不需要默认精灵，所有状态由外部绑定）
         */
        initializeContainer() {
            // 设置容器的交互属性
            this.interactive = true;
            this.interactiveChildren = true;

            // 设置容器的基础尺寸（用于碰撞检测等）
            this.containerWidth = this.buttonWidth;
            this.containerHeight = this.buttonHeight;

            console.log('✅ UIButton 容器初始化完成:', this.containerWidth, 'x', this.containerHeight);
        }

        /**
         * 🔑 核心方法：更新按钮
         */
        updateButton() {
            this.updateButtonDisplay();
        }

        /**
         * 更新按钮显示
         */
        updateButtonDisplay() {
            // 隐藏所有状态精灵
            this.hideAllStateSprites();

            // 显示当前状态的精灵
            const currentSprite = this.getCurrentStateSprite();
            if (currentSprite) {
                currentSprite.visible = true;
                console.log('🎨 UIButton: 显示状态精灵', this.currentState);
            }
        }

        /**
         * 隐藏所有状态精灵
         */
        hideAllStateSprites() {
            if (this.boundDefaultSprite) this.boundDefaultSprite.visible = false;
            if (this.boundHoverSprite) this.boundHoverSprite.visible = false;
            if (this.boundPressedSprite) this.boundPressedSprite.visible = false;
        }

        /**
         * 获取当前状态的精灵
         */
        getCurrentStateSprite() {
            switch (this.currentState) {
                case 'hover':
                    return this.boundHoverSprite || this.boundDefaultSprite;
                case 'pressed':
                    return this.boundPressedSprite || this.boundDefaultSprite;
                default:
                    return this.boundDefaultSprite;
            }
        }



        /**
         * 切换状态
         */
        switchToState(newState) {
            if (this.currentState === newState) return;

            console.log('🔄 UIButton: 状态切换', this.currentState, '->', newState);
            this.currentState = newState;
            this.updateButton();
        }

        /**
         * 设置事件监听器 - 使用 RPG Maker MZ 的 TouchInput 系统
         */
        setupEventListeners() {
            // 初始化按钮状态
            this._wasPressed = false;
            this._wasHovered = false;

            // 双击检测相关
            this._doubleClickCount = 0;
            this._doubleClickTimer = null;

            // 保存原始的 update 方法
            // if (!this._originalUpdate) {
            //     this._originalUpdate = this.update;
            // }

            // 🔑 使用 RPG Maker MZ 的循环事件系统（参考 UIItem）
            this.setupRPGMakerEventLoop();

            // 自动注册到全局注册表
            if (typeof window.registerUIButton === 'function') {
                window.registerUIButton(this);
            }

            console.log('✅ UIButton: 事件监听器设置完成');
        }

        /**
         * 🔑 设置 RPG Maker MZ 的循环事件系统（参考 UIItem）
         */
        setupRPGMakerEventLoop() {
            // 🔑 注册到 Graphics.app.ticker（PIXI 渲染循环）
            if (typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                this._tickerCallback = () => this.processButtonTouch();
                Graphics.app.ticker.add(this._tickerCallback);
                console.log('✅ UIButton: 已注册到 PIXI ticker 循环');
                return;
            }

            console.warn('⚠️ UIButton: 无法注册到 RPG Maker MZ 循环');
        }



        /**
         * 处理按钮触摸事件 - 使用 RPG Maker MZ 的 TouchInput 系统（通过循环事件调用）
         */
        processButtonTouch() {
            if (!this.enabled || !this.visible) {
                // console.log('🚫 UIButton: 按钮被禁用或不可见', {
                //     enabled: this.enabled,
                //     visible: this.visible,
                //     alpha: this.alpha,
                //     interactive: this.interactive
                // });
                return;
            }

            // 检查TouchInput是否可用 - 通过window访问
            if (typeof window.TouchInput === 'undefined') {
                console.log('🚫 UIButton: TouchInput未定义');
                return;
            }

            const isBeingTouched = this.isBeingTouched();
            const isTriggered = window.TouchInput.isTriggered();
            const isReleased = window.TouchInput.isReleased();

            // 添加调试日志
            // if (isBeingTouched || isTriggered || isReleased) {
            //     console.log('🎯 UIButton: 触摸状态', {
            //         isBeingTouched,
            //         isTriggered,
            //         isReleased,
            //         _wasHovered: this._wasHovered,
            //         _wasPressed: this._wasPressed
            //     });
            // }

            // 参考UISlider的逻辑进行状态管理
            if (isBeingTouched) {
                // 鼠标在按钮上

                // 处理悬停状态
                if (!this._wasHovered) {
                    this.onButtonHover();
                    this._wasHovered = true;
                }

                // 处理按下状态
                if (isTriggered) {
                    this._pressed = true;
                    this._wasPressed = true;
                    this.onButtonPress();
                }

                // 保持按下状态 - 只要TouchInput.isPressed()且鼠标在按钮上
                if (window.TouchInput.isPressed()) {
                    this._pressed = true;
                }
            } else {
                // 鼠标不在按钮上
                if (this._wasHovered) {
                    this.onButtonHoverOut();
                    this._wasHovered = false;
                }

                // � 关键修复：只有在没有按下时才清除_pressed状态
                // 如果正在按下，保持状态直到释放
                if (!window.TouchInput.isPressed()) {
                    this._pressed = false;
                } else {
                    console.log('🔄 UIButton: 鼠标离开按钮，但仍在按下，保持_pressed状态');
                }
            }

            // 处理释放状态 - 无论鼠标在哪里，只要释放就触发
            if (this._wasPressed && isReleased) {
                console.log('🎯 UIButton: 检测到释放事件，_wasPressed =', this._wasPressed);
                // 🔑 先调用 onButtonRelease()，再清除状态
                this.onButtonRelease();
                this._pressed = false;
                this._wasPressed = false;
            }
        }

        /**
         * 检查是否被触摸
         */
        isBeingTouched() {
            if (typeof window.TouchInput === 'undefined') return false;

            // 获取按钮在屏幕上的位置
            const bounds = this.getBounds();
            const touchX = window.TouchInput.x;
            const touchY = window.TouchInput.y;

            const isHit = touchX >= bounds.x && touchX <= bounds.x + bounds.width &&
                         touchY >= bounds.y && touchY <= bounds.y + bounds.height;

            // 🔍 调试：检查是否在容器内且容器可能阻挡事件
            if (this.parent && this.parent.uiComponentType === 'UIList') {
                const parentBounds = this.parent.getBounds();
                const insideParent = touchX >= parentBounds.x && touchX <= parentBounds.x + parentBounds.width &&
                                   touchY >= parentBounds.y && touchY <= parentBounds.y + parentBounds.height;

                console.log('🔍 UIButton在UIList内的碰撞检测:', {
                    buttonName: this.name || 'unnamed',
                    buttonBounds: bounds,
                    parentBounds: parentBounds,
                    touchPoint: { x: touchX, y: touchY },
                    hitButton: isHit,
                    insideParent: insideParent,
                    parentInteractive: this.parent.interactive,
                    buttonVisible: this.visible,
                    buttonWorldVisible: this.worldVisible
                });
            }

            return isHit;
        }

        /**
         * 检查是否有启用的指定生命周期方法
         */
        hasEnabledLifecycleMethod(methodName) {
            if (!this.componentScripts || !Array.isArray(this.componentScripts)) {
                return false;
            }

            return this.componentScripts.some(script => {
                if (!script.enabled || !script.code) return false;
                // 检查脚本代码中是否包含未注释的指定函数定义
                const lines = script.code.split('\n');
                return lines.some(line => {
                    const trimmed = line.trim();
                    // 排除注释行
                    if (trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*')) {
                        return false;
                    }
                    // 检查是否有指定函数定义
                    const regex = new RegExp(`function\\s+${methodName}\\s*\\(`);
                    return regex.test(trimmed);
                });
            });
        }

        /**
         * 按钮悬停事件
         */
        onButtonHover() {
            if (!this.enabled) return;
            // console.log('🔄 UIButton: 鼠标进入');
            this._hovered = true;
            if (!this._pressed) {
                this.switchToState('hover');
            }
            // 🔑 只有启用了 onHover 方法时才执行脚本
            if (this.executeScript && this.hasEnabledLifecycleMethod('onHover')) {
                this.executeScript('onHover');
            }
        }

        /**
         * 按钮离开事件
         */
        onButtonHoverOut() {
            // console.log('🔄 UIButton: 鼠标离开');
            this._hovered = false;
            if (!this._pressed) {
                this.switchToState('default');
            }
            // 🔑 只有启用了 onHoverOut 方法时才执行脚本
            if (this.executeScript && this.hasEnabledLifecycleMethod('onHoverOut')) {
                this.executeScript('onHoverOut');
            }
        }

        /**
         * 按钮按下事件
         */
        onButtonPress() {
            if (!this.enabled) return;
            console.log('👇 UIButton: 按钮按下');
            this._pressed = true;
            this.switchToState('pressed');
            // 🔑 只有启用了 onPress 方法时才执行脚本
            if (this.executeScript && this.hasEnabledLifecycleMethod('onPress')) {
                this.executeScript('onPress');
            }
        }

        /**
         * 按钮释放事件
         */
        onButtonRelease() {
            if (!this.enabled) return;
            console.log('👆 UIButton: 按钮释放', { _pressed: this._pressed, _wasPressed: this._wasPressed });

            // 保存按下状态，因为后面会清除
            const wasPressed = this._pressed || this._wasPressed;

            if (wasPressed) {
                console.log('🎯 UIButton: 检测到按钮被按下过，触发点击事件');
                this.handleClick(); // 处理点击和双击
            } else {
                console.log('⚠️ UIButton: 按钮未被按下，不触发点击事件');
            }

            this._pressed = false;
            this.switchToState(this._hovered ? 'hover' : 'default');
            // 🔑 只有启用了 onRelease 方法时才执行脚本
            if (this.executeScript && this.hasEnabledLifecycleMethod('onRelease')) {
                this.executeScript('onRelease');
            }
        }

        /**
         * 处理点击事件（包括双击检测）
         */
        handleClick() {
            console.log('🎯 UIButton: handleClick 被调用');

            // 🔑 检查是否有启用的双击脚本
            const hasDoubleClickScript = this.hasEnabledLifecycleMethod('onDoubleClick');

            console.log('🔍 UIButton: 双击检测', {
                hasDoubleClickScript,
                _doubleClickCount: this._doubleClickCount
            });

            if (hasDoubleClickScript) {
                // 有双击脚本，需要检测双击
                this._doubleClickCount = (this._doubleClickCount || 0) + 1;

                if (this._doubleClickCount === 1) {
                    // 第一次点击，设置定时器
                    console.log('⏰ UIButton: 设置单击延迟定时器');
                    this._doubleClickTimer = setTimeout(() => {
                        this._doubleClickCount = 0;
                        // 单击事件
                        console.log('⚡ UIButton: 延迟执行单击脚本');
                        if (this.executeScript && this.hasEnabledLifecycleMethod('onClick')) {
                            this.executeScript('onClick');
                        }
                    }, 300); // 300ms 内的第二次点击算作双击
                } else if (this._doubleClickCount === 2) {
                    // 第二次点击，清除定时器并执行双击事件
                    console.log('⚡ UIButton: 执行双击脚本');
                    if (this._doubleClickTimer) {
                        clearTimeout(this._doubleClickTimer);
                    }
                    this._doubleClickCount = 0;
                    if (this.executeScript && this.hasEnabledLifecycleMethod('onDoubleClick')) {
                        this.executeScript('onDoubleClick');
                    }
                    return; // 不执行单击事件
                }
            } else {
                // 没有双击脚本，直接执行单击
                console.log('⚡ UIButton: 直接执行单击脚本');
                if (this.executeScript && this.hasEnabledLifecycleMethod('onClick')) {
                    this.executeScript('onClick');
                }
            }
        }

        /**
         * 检查按钮是否被按下（用于阻止地图点击穿透）
         */
        isPressed() {
            const pressed = this._pressed;
            if (pressed) {
                console.log('🔴 UIButton.isPressed() = true, 按钮被按下');
            }
            return pressed;
        }





        /**
         * 设置启用状态
         */
        setEnabled(enabled) {
            this.enabled = enabled;
            this.interactive = enabled;
            this.alpha = enabled ? 1.0 : 0.5;

            if (enabled) {
                this.switchToState('default');
            } else {
                this.switchToState('disabled');
            }
        }

        /**
         * 设置尺寸
         */
        setSize(width, height) {
            this.buttonWidth = width;
            this.buttonHeight = height;
            this.updateButton();
            console.log('📏 UIButton: 尺寸更新', width, height);
        }

        /**
         * 获取宽度
         */
        get width() {
            return this.buttonWidth;
        }

        /**
         * 设置宽度
         */
        set width(value) {
            this.buttonWidth = value;
            this.updateButton();
        }

        /**
         * 获取高度
         */
        get height() {
            return this.buttonHeight;
        }

        /**
         * 设置高度
         */
        set height(value) {
            this.buttonHeight = value;
            this.updateButton();
        }

        /**
         * 检查对象是否为 UIImage 类型
         */
        isUIImageType(obj) {
            if (!obj) return false;

            // 方法1: 检查 UI 组件标识
            if (obj.isUIComponent === true && obj.uiComponentType === 'UIImage') {
                return true;
            }

            // 方法2: 检查构造函数名称
            if (obj.constructor && obj.constructor.name === 'UIImage') {
                return true;
            }

            // 方法3: 检查特征属性（UIImage 特有的属性）
            if (obj.imagePath !== undefined || obj.regions !== undefined ||
                obj.scaleMode !== undefined || obj.preserveAspectRatio !== undefined) {
                return true;
            }

            return false;
        }



        /**
         * 绑定默认状态精灵
         */
        bindDefaultSprite(sprite) {
            if (!this.isUIImageType(sprite)) {
                console.error('🚨 UIButton: boundDefaultSprite 必须是 UIImage 类型', {
                    provided: sprite.constructor.name,
                    expected: 'UIImage'
                });
                return;
            }

            this.boundDefaultSprite = sprite;
            this.updateButton();
            console.log('✅ UIButton: 成功绑定 DefaultSprite (UIImage)');
        }

        /**
         * 绑定悬停状态精灵
         */
        bindHoverSprite(sprite) {
            if (!this.isUIImageType(sprite)) {
                console.error('🚨 UIButton: boundHoverSprite 必须是 UIImage 类型', {
                    provided: sprite.constructor.name,
                    expected: 'UIImage'
                });
                return;
            }

            this.boundHoverSprite = sprite;
            this.updateButton();
            console.log('✅ UIButton: 成功绑定 HoverSprite (UIImage)');
        }

        /**
         * 绑定按下状态精灵
         */
        bindPressedSprite(sprite) {
            if (!this.isUIImageType(sprite)) {
                console.error('🚨 UIButton: boundPressedSprite 必须是 UIImage 类型', {
                    provided: sprite.constructor.name,
                    expected: 'UIImage'
                });
                return;
            }

            this.boundPressedSprite = sprite;
            this.updateButton();
            console.log('✅ UIButton: 成功绑定 PressedSprite (UIImage)');
        }

        /**
         * 克隆当前 UIButton 对象
         * @param {Object} options 克隆选项
         * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
         * @param {number} options.offsetX 水平偏移量 (默认: 20)
         * @param {number} options.offsetY 垂直偏移量 (默认: 20)
         * @returns {UIButton} 克隆的 UIButton 对象
         */
        clone(options = {}) {
            console.log('🔄 UIButton: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = {
                // 基础属性
                width: this.width,
                height: this.height,
                enabled: this.enabled,
                visible: this.visible,

                // 🔑 UIComponent 属性（安全访问）
                name: this.name || '',
                dataBinding: this.dataBinding || '',

                // 🔑 深度克隆组件脚本数组
                componentScripts: this.componentScripts ?
                    this.componentScripts.map(script => ({
                        id: script.id,
                        name: script.name,
                        type: script.type,
                        enabled: script.enabled,
                        code: script.code,
                        description: script.description
                    })) : [],

                // 🔑 按钮事件系统
                buttonEvents: this.buttonEvents ? { ...this.buttonEvents } : {}
            };

            // 2. 创建克隆对象
            const clonedButton = new UIButton(cloneProperties);

            // 3. 设置位置和变换属性
            clonedButton.x = this.x + (offsetPosition ? offsetX : 0);
            clonedButton.y = this.y + (offsetPosition ? offsetY : 0);
            clonedButton.scale.x = this.scale.x;
            clonedButton.scale.y = this.scale.y;
            clonedButton.rotation = this.rotation;
            clonedButton.alpha = this.alpha;
            clonedButton.zIndex = this.zIndex;

            // 4. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                    clonedButton.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                }
            }

            // 5. 重建绑定关系
            this.rebuildBindingsForClone(clonedButton, clonedChildren);

            console.log('✅ UIButton: 克隆完成，包含', clonedChildren.length, '个子对象');
            return clonedButton;
        }

        /**
         * 为克隆对象重建绑定关系
         * @param {UIButton} clonedButton 克隆的按钮对象
         * @param {Array} clonedChildren 克隆的子对象数组
         */
        rebuildBindingsForClone(clonedButton, clonedChildren) {
            console.log('🔗 UIButton: 重建克隆对象的绑定关系');

            // 找到原始绑定对象在子对象数组中的索引
            const findChildIndex = (boundObject) => {
                if (!boundObject) return -1;
                for (let i = 0; i < this.children.length; i++) {
                    if (this.children[i] === boundObject) {
                        return i;
                    }
                }
                return -1;
            };

            // 重新绑定默认状态精灵
            if (this.boundDefaultSprite) {
                const index = findChildIndex(this.boundDefaultSprite);
                if (index >= 0 && index < clonedChildren.length) {
                    clonedButton.bindDefaultSprite(clonedChildren[index]);
                    console.log('🔗 重新绑定默认状态精灵');
                }
            }

            // 重新绑定悬停状态精灵
            if (this.boundHoverSprite) {
                const index = findChildIndex(this.boundHoverSprite);
                if (index >= 0 && index < clonedChildren.length) {
                    clonedButton.bindHoverSprite(clonedChildren[index]);
                    console.log('🔗 重新绑定悬停状态精灵');
                }
            }

            // 重新绑定按下状态精灵
            if (this.boundPressedSprite) {
                const index = findChildIndex(this.boundPressedSprite);
                if (index >= 0 && index < clonedChildren.length) {
                    clonedButton.bindPressedSprite(clonedChildren[index]);
                    console.log('🔗 重新绑定按下状态精灵');
                }
            }
        }

        /**
         * 销毁UIButton并从注册表中清理
         */
        destroy() {
            console.log('🗑️ UIButton.destroy() 被调用！', this.name || 'unnamed');

            // 🔑 安全执行销毁脚本
            try {
                if (this.executeScript && !this._isDestroying) {
                    this._isDestroying = true; // 防止重复销毁
                    this.executeScript('onDestroy');
                }
            } catch (error) {
                console.warn('⚠️ UIButton: 销毁脚本执行失败', error);
            }

            // 🔑 立即禁用按钮
            this.enabled = false;
            this.visible = false;

            // 🔑 禁用交互
            this.interactive = false;
            this.interactiveChildren = false;

            // 🔑 清理 PIXI ticker 注册
            if (this._tickerCallback && typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                Graphics.app.ticker.remove(this._tickerCallback);
                this._tickerCallback = null;
            }

            // 从全局注册表中注销
            if (typeof window.unregisterUIButton === 'function') {
                window.unregisterUIButton(this);
            }

            // 清理按钮事件
            this.buttonEvents = {};

            // 清理绑定的精灵引用
            this.boundDefaultSprite = null;
            this.boundHoverSprite = null;
            this.boundPressedSprite = null;

            // 清理定时器
            if (this._doubleClickTimer) {
                clearTimeout(this._doubleClickTimer);
                this._doubleClickTimer = null;
            }

            // 调用父类的destroy方法（如果存在）
            if (super.destroy) {
                super.destroy();
            }

            console.log('🗑️ UIButton: 已销毁并清理注册表');
        }

    }

    // 导出到全局
    window.UIButton = UIButton;

    // 全局UIButton实例注册表
    window._uiButtonRegistry = window._uiButtonRegistry || [];

    // 注册UIButton实例
    window.registerUIButton = function(button) {
        if (window._uiButtonRegistry.indexOf(button) === -1) {
            window._uiButtonRegistry.push(button);
            console.log('📝 UIButton已注册，当前总数:', window._uiButtonRegistry.length);
        }
    };

    // 注销UIButton实例
    window.unregisterUIButton = function(button) {
        const index = window._uiButtonRegistry.indexOf(button);
        if (index > -1) {
            window._uiButtonRegistry.splice(index, 1);
            console.log('🗑️ UIButton已注销，当前总数:', window._uiButtonRegistry.length);
        }
    };

    // 检查是否有任何UIButton被按下
    window.isAnyUIButtonPressed = function() {
        // console.log('🔍 检查全局UIButton注册表:', {
        //     总数: window._uiButtonRegistry.length,
        //     按钮列表: window._uiButtonRegistry.map(btn => ({
        //         name: btn.name || 'unnamed',
        //         enabled: btn.enabled,
        //         visible: btn.visible,
        //         isPressed: btn.isPressed ? btn.isPressed() : 'no method'
        //     }))
        // });

        const result = window._uiButtonRegistry.some(button =>
            button.isPressed && button.isPressed()
        );

        // console.log('🔍 全局注册表检查结果:', result);
        return result;
    };

    // 扩展Scene_Map以支持UIButton的点击穿透阻止
    // 使用延迟加载确保Scene_Map已经定义
    setTimeout(() => {
        if (typeof window.Scene_Map !== 'undefined') {
            // console.log('🔧 开始扩展Scene_Map以支持UIButton点击穿透阻止');

            // 保存原始的isAnyButtonPressed方法
            const _Scene_Map_isAnyButtonPressed = window.Scene_Map.prototype.isAnyButtonPressed;

            window.Scene_Map.prototype.isAnyButtonPressed = function() {
                // 调用原始方法检查菜单按钮等
                const originalResult = _Scene_Map_isAnyButtonPressed.call(this);
                if (originalResult) {
                    // console.log('🚫 原始按钮被按下，阻止地图点击');
                    return true;
                }

                // 检查是否有UIButton被按下
                const uiButtonPressed = this.isAnyUIButtonPressed();

                // 添加详细调试日志
                // console.log('� Scene_Map.isAnyButtonPressed检查', {
                //     originalResult,
                //     uiButtonPressed,
                //     TouchInputPressed: window.TouchInput ? window.TouchInput.isPressed() : 'undefined',
                //     TouchInputTriggered: window.TouchInput ? window.TouchInput.isTriggered() : 'undefined'
                // });

                if (uiButtonPressed) {
                    // console.log('� UIButton被按下，阻止地图点击穿透');
                }
                return uiButtonPressed;
            };

            window.Scene_Map.prototype.isAnyUIButtonPressed = function() {
                // 使用全局注册表检查UIButton，而不是搜索场景树
                if (typeof window.isAnyUIButtonPressed === 'function') {
                    const result = window.isAnyUIButtonPressed();
                    // console.log('🔍 全局UIButton注册表检查结果:', result);
                    return result;
                }
                return false;
            };



            // console.log('✅ Scene_Map扩展完成，支持UIButton点击穿透阻止');
        } else {
            console.warn('⚠️ Scene_Map未找到，无法扩展点击穿透阻止功能');
        }
    }, 100); // 延迟100ms确保Scene_Map已加载

    console.log('✅ UIButton插件加载完成');

})();
