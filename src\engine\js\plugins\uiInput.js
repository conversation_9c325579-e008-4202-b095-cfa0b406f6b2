/**
 * UIInput 插件 - 输入框组件
 *
 * 功能：
 * - 文本输入和编辑
 * - 光标管理和文本选择
 * - 输入验证和类型限制
 * - 占位符支持
 * - 完整的脚本系统支持
 *
 * 使用方法：
 * const input = new UIInput({
 *     width: 200,
 *     height: 40,
 *     placeholder: '请输入文本...',
 *     inputType: 'text'
 * });
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

(function() {
    'use strict';

    console.log('📝 开始加载 UIInput 插件 v1.0.0');

    /**
     * UIInput - 输入框组件
     * 继承自 Sprite，参考UILabel的实现架构
     */
    class UIInput extends Sprite {
        constructor(properties = {}) {
            super();

            console.log('📝 UIInput: 创建输入框组件', properties);

            // 🔑 组件标识 (参考UILabel)
            this.isUIComponent = true;
            this.uiComponentType = 'UIInput';

            // 🔑 使用UIComponentUtils添加UIComponent功能
            if (window.UIComponentUtils) {
                window.UIComponentUtils.applyToSprite(this, properties);
            }

            // 🔑 初始化脚本管理器（参考UISlider实现）
            this.initializeScriptManager(properties);

            // 🔑 初始化输入框
            this.initializeInput(properties);

            // 🔑 标记为已创建，onStart会在添加到父容器时执行
            this._isCreated = true;

            console.log('✅ UIInput: 输入框组件创建完成', {
                size: `${this.width}x${this.height}`,
                inputType: this._inputType,
                placeholder: this._placeholder
            });
        }

        /**
         * 🔑 初始化脚本管理器（参考UISlider实现）
         */
        initializeScriptManager(properties) {
            if (typeof window.UIScriptManager !== 'undefined') {
                // 🔑 修复：让UIScriptManager自动创建默认脚本（如果没有提供componentScripts）
                window.UIScriptManager.applyToObject(this, properties);
                console.log('✅ UIInput: 脚本管理器初始化完成', {
                    hasComponentScripts: !!this.componentScripts,
                    scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                    scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                });
            } else {
                console.warn('⚠️ UIInput: UIScriptManager 不可用');
                // 🔑 如果UIScriptManager不可用，至少初始化一个空数组
                this.componentScripts = properties.componentScripts || [];
            }
        }

        /**
         * 初始化输入框 - 参考UILabel的initializeLabel结构
         */
        initializeInput(properties) {
            // 基础属性
            this._width = properties.width || 200;
            this._height = properties.height || 40;

            // 输入框特有属性
            this._value = properties.value || '';
            this._placeholder = properties.placeholder || '';
            this._inputType = properties.inputType || 'text'; // text, password, number, email
            this._maxLength = properties.maxLength || 9;
            this._readonly = properties.readonly || false;

            // 样式属性
            this._fontSize = properties.fontSize || 16;
            this._textColor = properties.textColor || '#000000';
            this._placeholderColor = properties.placeholderColor || '#999999';
            this._backgroundColor = properties.backgroundColor || '#ffffff';
            this._borderColor = properties.borderColor || '#cccccc';
            this._focusBorderColor = properties.focusBorderColor || '#007bff';
            this._borderWidth = properties.borderWidth || 1;
            this._borderRadius = properties.borderRadius || 4;
            this._letterSpacing = properties.letterSpacing || 0; // 🔑 新增字符间距
            this._defaultPadding = 4; // 🔑 默认内边距，防止文字被裁切

            // 光标和选择状态
            this._cursorPosition = 0;
            this._selectionStart = 0;
            this._selectionEnd = 0;
            this._focused = false;
            this._cursorVisible = true;
            this._cursorBlinkTimer = 0;

            // 输入验证
            this._validationPattern = properties.validationPattern || null;
            this._validationMessage = properties.validationMessage || '输入格式不正确';

            // 创建视觉元素
            this.createInputElements();
            this.setupEventListeners();

            console.log('🔧 UIInput: 输入框初始化完成', {
                value: this._value,
                type: this._inputType,
                maxLength: this._maxLength
            });
        }

        /**
         * 创建输入框的视觉元素
         */
        createInputElements() {
            // 🔑 统一绘制：直接在UIInput对象上创建一个bitmap
            this.inputBitmap = new Bitmap(this._width, this._height);
            this.bitmap = this.inputBitmap;

            // 🔑 添加调试信息
            console.log('📝 UIInput: 创建视觉元素完成', {
                width: this._width,
                height: this._height,
                letterSpacing: this._letterSpacing,
                inputBitmap: !!this.inputBitmap
            });

            // 绘制初始状态
            this.redrawAll();
        }

        /**
         * 设置事件监听器
         */
        setupEventListeners() {
            // 🔑 参考UIButton：使用RPG Maker MZ的TouchInput系统
            // 初始化状态
            this._wasClicked = false;
            this._wasHovered = false;

            // 键盘事件需要在获得焦点时绑定
            this._keydownHandler = this.onKeyDown.bind(this);
            this._keyupHandler = this.onKeyUp.bind(this);

            // 🔑 使用RPG Maker MZ的循环事件系统（参考UIButton）
            this.setupRPGMakerEventLoop();

            console.log('✅ UIInput: 事件监听器设置完成（使用TouchInput系统）');
        }

        /**
         * 🔑 设置 RPG Maker MZ 的循环事件系统（参考 UIButton）
         */
        setupRPGMakerEventLoop() {
            // 🔑 注册到 Graphics.app.ticker（PIXI 渲染循环）
            if (typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                this._tickerCallback = () => {
                    this.processInputTouch(); // 处理TouchInput事件
                    this.update(); // 处理光标闪烁等
                };
                Graphics.app.ticker.add(this._tickerCallback);
                console.log('✅ UIInput: 已注册到 PIXI ticker 循环');
                return;
            }

            console.warn('⚠️ UIInput: 无法注册到 RPG Maker MZ 循环');
        }

        /**
         * 🔑 处理输入框触摸事件 - 使用 RPG Maker MZ 的 TouchInput 系统（参考UIButton）
         */
        processInputTouch() {
            if (!this.enabled || !this.visible) {
                return;
            }

            // 检查TouchInput是否可用
            if (typeof window.TouchInput === 'undefined') {
                return;
            }

            const isBeingTouched = this.isBeingTouched();
            const isTriggered = window.TouchInput.isTriggered();

            // 🔑 处理失去焦点 - 如果点击了其他地方且当前有焦点
            if (isTriggered && this._focused && !isBeingTouched) {
                console.log('📝 UIInput: 点击其他地方，失去焦点');
                this.blur();
                return;
            }

            // 处理点击事件
            if (isBeingTouched && isTriggered) {
                console.log('📝 UIInput: TouchInput点击事件触发');
                this.focus();

                // 计算相对位置设置光标
                const touchX = window.TouchInput.x;
                const bounds = this.getBounds();
                const localX = touchX - bounds.x;
                this.setCursorFromPosition(localX);

                // 执行点击脚本
                if (this.executeScript) {
                    this.executeScript('onClick');
                }
            }
        }

        /**
         * 🔑 检查是否被触摸（参考UIButton的实现）
         */
        isBeingTouched() {
            if (typeof window.TouchInput === 'undefined') return false;

            // 获取输入框在屏幕上的位置
            const bounds = this.getBounds();
            const touchX = window.TouchInput.x;
            const touchY = window.TouchInput.y;

            const isHit = touchX >= bounds.x && touchX <= bounds.x + bounds.width &&
                         touchY >= bounds.y && touchY <= bounds.y + bounds.height;

            return isHit;
        }

        /**
         * 获取焦点
         */
        focus() {
            if (this._focused) return;

            this._focused = true;
            // 🔑 修复：重置光标状态
            this._cursorVisible = true;
            this._cursorBlinkTimer = 0;

            // 🔑 统一重绘
            this.redrawAll();

            // 绑定键盘事件
            document.addEventListener('keydown', this._keydownHandler);
            document.addEventListener('keyup', this._keyupHandler);

            // 执行焦点脚本
            if (this.executeScript) {
                this.executeScript('onFocus');
            }

            console.log('📝 UIInput: 获得焦点');
        }

        /**
         * 失去焦点
         */
        blur() {
            if (!this._focused) return;

            this._focused = false;
            this._cursorVisible = false;
            // 🔑 统一重绘
            this.redrawAll();

            // 解绑键盘事件
            document.removeEventListener('keydown', this._keydownHandler);
            document.removeEventListener('keyup', this._keyupHandler);

            // 执行失焦脚本
            if (this.executeScript) {
                this.executeScript('onBlur');
            }

            console.log('📝 UIInput: 失去焦点');
        }

        /**
         * 键盘按下事件处理
         */
        onKeyDown(event) {
            if (!this._focused || this._readonly) return;

            const key = event.key;
            const oldValue = this._value;

            console.log('🔧 UIInput: 键盘按下', {
                key,
                code: event.code,
                isComposing: this._isComposing
            });

            // 🔑 修复：在输入法组合状态下，只处理控制键，不处理字符输入
            if (this._isComposing) {
                // 在输入法组合状态下，只允许某些控制键
                switch (key) {
                    case 'Escape':
                        // 取消输入法组合
                        this._isComposing = false;
                        event.preventDefault();
                        break;
                    case 'ArrowLeft':
                    case 'ArrowRight':
                    case 'ArrowUp':
                    case 'ArrowDown':
                        // 允许方向键（输入法可能需要）
                        break;
                    default:
                        // 其他键在组合状态下不处理，让输入法处理
                        console.log('🔧 UIInput: 输入法组合状态，跳过键盘处理', { key });
                        return;
                }
                return;
            }

            switch (key) {
                case 'Backspace':
                    this.handleBackspace();
                    event.preventDefault();
                    break;
                case 'Delete':
                    this.handleDelete();
                    event.preventDefault();
                    break;
                case 'ArrowLeft':
                    this.moveCursor(-1);
                    event.preventDefault();
                    break;
                case 'ArrowRight':
                    this.moveCursor(1);
                    event.preventDefault();
                    break;
                case 'Home':
                    this.setCursorPosition(0);
                    event.preventDefault();
                    break;
                case 'End':
                    this.setCursorPosition(this._value.length);
                    event.preventDefault();
                    break;
                case 'Enter':
                    if (this.executeScript) {
                        this.executeScript('onEnterPressed');
                    }
                    event.preventDefault();
                    break;
                default:
                    // 🔑 修复：只在非输入法组合状态下处理可打印字符
                    if (!this._isComposing && this.isValidInput(key)) {
                        this.insertText(key);
                        event.preventDefault();
                    }
                    break;
            }

            // 如果值发生变化，触发变化事件
            if (this._value !== oldValue) {
                this.onValueChanged(oldValue);
            }
        }

        /**
         * 键盘释放事件处理
         */
        onKeyUp(event) {
            // 可以在这里处理一些特殊的键盘释放事件
        }



        /**
         * 处理退格键
         */
        handleBackspace() {
            if (this._cursorPosition > 0) {
                this._value = this._value.slice(0, this._cursorPosition - 1) + this._value.slice(this._cursorPosition);
                this._cursorPosition--;

                // 🔑 修复：删除后重新计算文本偏移，确保内容可见
                this.adjustTextOffsetAfterDeletion();

                // 🔑 统一重绘
                this.redrawAll();
            }
        }

        /**
         * 处理删除键
         */
        handleDelete() {
            if (this._cursorPosition < this._value.length) {
                this._value = this._value.slice(0, this._cursorPosition) + this._value.slice(this._cursorPosition + 1);

                // 🔑 修复：删除后重新计算文本偏移，确保内容可见
                this.adjustTextOffsetAfterDeletion();

                // 🔑 统一重绘
                this.redrawAll();
            }
        }

        /**
         * 🔑 删除后调整文本偏移，确保内容始终可见
         */
        adjustTextOffsetAfterDeletion() {
            const maxTextWidth = this._width - this._defaultPadding * 2;
            // 🔑 修复：使用显示文本宽度（考虑密码模式）
            const totalTextWidth = this.measureDisplayTextWidth(this._value);

            // 如果文本完全可以显示，重置偏移
            if (totalTextWidth <= maxTextWidth) {
                this._textOffset = 0;
                console.log('🔧 UIInput: 文本可完全显示，重置偏移');
                return;
            }

            // 如果当前偏移导致右侧有空白，调整偏移
            if (this._textOffset > 0 && totalTextWidth - this._textOffset < maxTextWidth) {
                this._textOffset = Math.max(0, totalTextWidth - maxTextWidth);
                console.log('🔧 UIInput: 调整偏移以消除右侧空白', {
                    newOffset: this._textOffset,
                    totalTextWidth,
                    maxTextWidth,
                    inputType: this._inputType
                });
            }
        }

        /**
         * 移动光标
         */
        moveCursor(direction) {
            const newPosition = Math.max(0, Math.min(this._value.length, this._cursorPosition + direction));
            this.setCursorPosition(newPosition);
        }

        /**
         * 设置光标位置
         */
        setCursorPosition(position) {
            this._cursorPosition = Math.max(0, Math.min(this._value.length, position));
            this.updateCursor();
        }

        /**
         * 根据鼠标位置设置光标
         */
        setCursorFromPosition(x) {
            // 🔑 修复：考虑默认内边距和文本偏移
            const relativeX = x - this._defaultPadding + (this._textOffset || 0);
            let position = 0;

            console.log('🔧 UIInput: 计算光标位置', {
                clickX: x,
                letterSpacing: this._letterSpacing,
                textOffset: this._textOffset || 0,
                relativeX,
                valueLength: this._value.length
            });

            // 🔑 修复：使用带字符间距的文本宽度测量
            if (this._value && this.inputBitmap) {
                // 逐个字符测量，找到最接近的位置
                for (let i = 0; i <= this._value.length; i++) {
                    const textWidth = this.measureDisplayTextWidthWithSpacing(this._value.substring(0, i));

                    if (i === this._value.length) {
                        // 如果是最后一个位置，检查点击是否在文本末尾之后
                        if (relativeX >= textWidth) {
                            position = i;
                        }
                        break;
                    } else {
                        // 计算当前字符的中点
                        const nextTextWidth = this.measureDisplayTextWidthWithSpacing(this._value.substring(0, i + 1));
                        const charMidPoint = (textWidth + nextTextWidth) / 2;

                        if (relativeX <= charMidPoint) {
                            position = i;
                            break;
                        }
                        position = i + 1;
                    }
                }
            }

            console.log('🔧 UIInput: 设置光标位置', {
                calculatedPosition: position,
                finalPosition: Math.max(0, Math.min(this._value.length, position))
            });

            this.setCursorPosition(position);
        }

        /**
         * 🔑 测量文本宽度 - 使用RPG Maker MZ的Bitmap测量
         */
        measureTextWidth(text) {
            if (!text || !this.inputBitmap) return 0;

            // 创建临时bitmap来测量文本宽度
            const tempBitmap = new Bitmap(1, 1);
            tempBitmap.fontSize = this._fontSize;
            tempBitmap.fontFace = this.inputBitmap.fontFace || 'GameFont';

            return tempBitmap.measureTextWidth(text);
        }

        /**
         * 🔑 测量显示文本宽度（考虑密码模式）
         */
        measureDisplayTextWidth(text) {
            if (!text) return 0;

            // 🔑 修复：密码模式下测量星号的宽度
            if (this._inputType === 'password') {
                const asteriskText = '*'.repeat(text.length);
                return this.measureTextWidth(asteriskText);
            }

            return this.measureTextWidth(text);
        }

        /**
         * 🔑 测量显示文本宽度（考虑字符间距）
         */
        measureDisplayTextWidthWithSpacing(text) {
            if (!text) return 0;

            const baseWidth = this.measureDisplayTextWidth(text);
            const spacingWidth = (text.length - 1) * this._letterSpacing;
            return baseWidth + spacingWidth;
        }

        /**
         * 🔑 绘制带字符间距的文本
         */
        drawTextWithSpacing(displayText, textOffset) {
            if (!displayText) return;

            const chars = displayText.split('');
            let currentX = this._defaultPadding - textOffset;

            // 🔑 修复：密码模式和普通模式都要上下居中
            const textY = (this._height - this._fontSize) / 2;

            for (let i = 0; i < chars.length; i++) {
                const char = chars[i];
                const charWidth = this.measureTextWidth(char);

                // 绘制单个字符
                this.inputBitmap.drawText(char, currentX, textY, charWidth + 10, this._height, 'left');

                // 移动到下一个字符位置
                currentX += charWidth + this._letterSpacing;
            }

            console.log('🔧 UIInput: 绘制字符间距文本', {
                charCount: chars.length,
                letterSpacing: this._letterSpacing,
                startX: 0 - textOffset,
                textY,
                inputType: this._inputType,
                isPassword: this._inputType === 'password'
            });
        }



        /**
         * 插入文本
         */
        insertText(text) {
            if (this._value.length >= this._maxLength) return;

            // 🔑 修复：正确处理多字符文本（如中文）
            this._value = this._value.slice(0, this._cursorPosition) + text + this._value.slice(this._cursorPosition);
            this._cursorPosition += text.length; // 使用text.length而不是固定的1
            this.updateCursor();
            this.redrawAll();

            // 🔑 触发值改变事件
            this.onValueChanged(this._value.slice(0, this._cursorPosition - text.length) + this._value.slice(this._cursorPosition));
        }

        /**
         * 验证输入是否有效
         */
        isValidInput(key) {
            // 忽略控制字符
            if (key.length > 1) return false;

            // 根据输入类型验证
            switch (this._inputType) {
                case 'number':
                    return /[0-9.-]/.test(key);
                case 'email':
                    return /[a-zA-Z0-9@._-]/.test(key);
                case 'password':
                case 'text':
                default:
                    return true;
            }
        }

        /**
         * 值改变时的处理
         */
        onValueChanged(oldValue) {
            // 🔑 同步值到模型对象（反向同步）
            this.syncValueToModel();

            // 执行值改变脚本
            if (this.executeScript) {
                this.executeScript('onChange', this._value, oldValue);
            }

            console.log('📝 UIInput: 值改变', { oldValue, newValue: this._value });
        }

        /**
         * 🔑 同步值到模型对象（反向同步）
         * 当用户在输入框中输入时，需要将值同步回模型
         */
        syncValueToModel() {
            // 查找对应的模型对象
            if (this.parent && this.parent.children) {
                // 遍历父容器的所有子对象，找到对应的模型
                for (const child of this.parent.children) {
                    if (child._originalObject === this) {
                        // 找到了对应的模型，同步值
                        if (child.value !== this._value) {
                            child.value = this._value;
                            console.log('🔄 UIInput: 同步值到模型', {
                                inputValue: this._value,
                                modelValue: child.value
                            });
                        }
                        return;
                    }
                }
            }

            // 如果没有找到模型，尝试通过全局查找
            if (typeof window !== 'undefined' && window.currentScene) {
                this.findAndSyncToModel(window.currentScene);
            }
        }

        /**
         * 🔑 递归查找并同步到模型对象
         */
        findAndSyncToModel(container) {
            if (!container || !container.children) return;

            for (const child of container.children) {
                // 检查是否是对应的模型对象
                if (child._originalObject === this) {
                    if (child.value !== this._value) {
                        child.value = this._value;
                        console.log('🔄 UIInput: 递归找到并同步值到模型', {
                            inputValue: this._value,
                            modelValue: child.value
                        });
                    }
                    return;
                }

                // 递归查找子容器
                this.findAndSyncToModel(child);
            }
        }

        /**
         * 🔑 统一绘制方法 - 在一个bitmap上绘制所有内容
         */
        redrawAll() {
            if (!this.inputBitmap) return;

            // 清除整个bitmap
            this.inputBitmap.clear();

            // 1. 绘制背景和边框
            this.drawBackground();

            // 2. 绘制文本
            this.drawText();

            // 3. 绘制光标
            this.drawCursor();

            console.log('🔧 UIInput: 统一绘制完成');
        }

        /**
         * 绘制背景和边框
         */
        drawBackground() {
            const borderColor = this._focused ? this._focusBorderColor : this._borderColor;

            // 绘制背景
            this.inputBitmap.fillRect(0, 0, this._width, this._height, this._backgroundColor);

            // 绘制边框
            if (this._borderWidth > 0) {
                // 上边框
                this.inputBitmap.fillRect(0, 0, this._width, this._borderWidth, borderColor);
                // 下边框
                this.inputBitmap.fillRect(0, this._height - this._borderWidth, this._width, this._borderWidth, borderColor);
                // 左边框
                this.inputBitmap.fillRect(0, 0, this._borderWidth, this._height, borderColor);
                // 右边框
                this.inputBitmap.fillRect(this._width - this._borderWidth, 0, this._borderWidth, this._height, borderColor);
            }
        }

        /**
         * 绘制文本内容
         */
        drawText() {
            const displayText = this.getDisplayText();
            const textColor = this._value ? this._textColor : this._placeholderColor;

            this.inputBitmap.fontSize = this._fontSize;
            this.inputBitmap.textColor = textColor;

            // 🔑 修复：正确应用文本偏移，考虑默认内边距
            const maxTextWidth = this._width - this._defaultPadding * 2;
            const totalTextWidth = this.measureDisplayTextWidthWithSpacing(displayText);

            // 计算文本偏移
            let textOffset = 0;
            if (totalTextWidth > maxTextWidth) {
                // 🔑 关键修复：让文本右边缘始终紧贴输入框右边缘
                textOffset = totalTextWidth - maxTextWidth;

                // 但要确保光标可见
                const cursorX = this.measureDisplayTextWidth(this._value.substring(0, this._cursorPosition));
                const visibleCursorX = cursorX - textOffset;
                const minMargin = 10;

                if (visibleCursorX < minMargin) {
                    textOffset = cursorX - minMargin;
                    textOffset = Math.max(0, textOffset);
                }
            }

            // 更新文本偏移
            this._textOffset = textOffset;

            // 🔑 支持字符间距的文本绘制
            if (this._letterSpacing > 0 && displayText) {
                this.drawTextWithSpacing(displayText, textOffset);
            } else {
                // 普通绘制（无字符间距）
                const textX = this._defaultPadding - textOffset;
                // 🔑 修复：所有文本都要上下居中
                const textY = (this._height - this._fontSize) / 2;

                // 🔑 关键修复：让文本以自然宽度绘制，设置合适的textHeight
                const textWidth = Math.max(totalTextWidth, maxTextWidth);
                const textHeight = this._fontSize; // 使用字体大小作为高度，避免RPG Maker MZ重新对齐

                this.inputBitmap.drawText(displayText, textX, textY, textWidth, textHeight, 'left');
            }

            console.log('🔧 UIInput: 绘制文本', {
                displayText: displayText.substring(0, 10) + (displayText.length > 10 ? '...' : ''),
                totalTextWidth,
                maxTextWidth,
                textOffset,
                letterSpacing: this._letterSpacing,
                strategy: totalTextWidth <= maxTextWidth ? 'no-scroll' : 'right-aligned'
            });
        }

        /**
         * 绘制光标
         */
        drawCursor() {
            if (!this._focused || !this._cursorVisible) {
                return;
            }

            // 🔑 计算光标位置 - 使用带字符间距的测量
            const textBeforeCursor = this._value.substring(0, this._cursorPosition);
            let cursorX = this.measureDisplayTextWidthWithSpacing(textBeforeCursor);

            // 🔑 使用与drawText相同的文本偏移逻辑
            const textOffset = this._textOffset || 0;

            // 调整光标位置
            cursorX = cursorX - textOffset;

            // 🔑 确保光标在输入框边界内，考虑默认内边距
            const maxTextWidth = this._width - this._defaultPadding * 2;
            const minX = 0;
            const maxX = maxTextWidth - 2;
            cursorX = Math.max(minX, Math.min(maxX, cursorX));

            // 绘制光标线条 - 确保完整显示
            const finalCursorX = this._defaultPadding + cursorX;

            // 🔑 修复：计算合适的光标高度和位置，确保完整显示
            const availableHeight = this._height;
            const cursorHeight = Math.min(this._fontSize, availableHeight - 4); // 确保不超出边界
            const finalCursorY = (availableHeight - cursorHeight) / 2; // 垂直居中

            this.inputBitmap.fillRect(finalCursorX, finalCursorY, 1, cursorHeight, this._textColor);

            console.log('🔧 UIInput: 绘制光标', {
                cursorPosition: this._cursorPosition,
                textBeforeCursor,
                inputType: this._inputType,
                originalCursorX: this.measureDisplayTextWidth(textBeforeCursor),
                textOffset,
                adjustedCursorX: cursorX,
                finalX: finalCursorX,
                maxTextWidth,
                focused: this._focused,
                cursorVisible: this._cursorVisible
            });
        }

        /**
         * 🔑 更新光标 - 兼容旧的API调用
         */
        updateCursor() {
            // 现在光标是在统一绘制中处理的，所以直接重绘全部
            this.redrawAll();
        }

        /**
         * 获取要显示的文本
         */
        getDisplayText() {
            if (this._value) {
                // 如果是密码类型，显示星号
                if (this._inputType === 'password') {
                    return '*'.repeat(this._value.length);
                }
                return this._value;
            } else {
                // 显示占位符
                return this._placeholder;
            }
        }



        /**
         * 🔑 计算文本偏移量（用于文本滚动）
         */
        calculateTextOffset(cursorX, maxTextWidth) {
            if (!this._textOffset) this._textOffset = 0;

            const totalTextWidth = this.measureDisplayTextWidth(this._value);

            // 🔑 修复：如果文本完全可以显示，不需要偏移
            if (totalTextWidth <= maxTextWidth) {
                this._textOffset = 0;
                console.log('🔧 UIInput: 文本完全可见，重置偏移', {
                    totalTextWidth,
                    maxTextWidth,
                    textOffset: this._textOffset
                });
                return this._textOffset;
            }

            // 🔑 关键修复：当文本超出宽度时，始终让文本右边缘紧贴输入框右边缘
            // 这样最后一个字符就会始终在最右边
            this._textOffset = totalTextWidth - maxTextWidth;

            // 🔑 但是要确保光标可见 - 如果光标在左边界外，调整偏移
            const visibleCursorX = cursorX - this._textOffset;
            const minMargin = 10; // 光标左边的最小边距

            if (visibleCursorX < minMargin) {
                // 如果光标太靠左，调整偏移让光标可见
                this._textOffset = cursorX - minMargin;
                // 但不能小于0
                this._textOffset = Math.max(0, this._textOffset);
            }

            console.log('🔧 UIInput: 计算文本偏移', {
                cursorX,
                totalTextWidth,
                maxTextWidth,
                visibleCursorX,
                textOffset: this._textOffset,
                strategy: visibleCursorX < minMargin ? 'cursor-visible' : 'text-right-aligned'
            });

            return this._textOffset;
        }

        /**
         * 获取/设置宽度
         */
        get width() {
            return this._width;
        }

        set width(value) {
            if (this._width !== value) {
                this._width = value;
                // 🔑 重新创建bitmap并统一重绘
                if (this.inputBitmap) {
                    this.inputBitmap.destroy();
                    this.inputBitmap = new Bitmap(this._width, this._height);
                    this.bitmap = this.inputBitmap;
                }
                this.redrawAll();
            }
        }

        /**
         * 获取/设置高度
         */
        get height() {
            return this._height;
        }

        set height(value) {
            if (this._height !== value) {
                this._height = value;
                // 🔑 重新创建bitmap并统一重绘
                if (this.inputBitmap) {
                    this.inputBitmap.destroy();
                    this.inputBitmap = new Bitmap(this._width, this._height);
                    this.bitmap = this.inputBitmap;
                }
                this.redrawAll();
            }
        }

        /**
         * 获取/设置值
         */
        get value() {
            return this._value;
        }

        set value(newValue) {
            const oldValue = this._value;
            this._value = String(newValue || '');
            this._cursorPosition = Math.min(this._cursorPosition, this._value.length);
            this.redrawAll();

            if (oldValue !== this._value) {
                this.onValueChanged(oldValue);
            }
        }

        /**
         * 获取/设置占位符
         */
        get placeholder() {
            return this._placeholder;
        }

        set placeholder(value) {
            this._placeholder = String(value || '');
            this.redrawAll();
        }

        /**
         * 获取/设置只读状态
         */
        get readonly() {
            return this._readonly;
        }

        set readonly(value) {
            this._readonly = Boolean(value);
        }

        /**
         * 获取/设置输入类型
         */
        get inputType() {
            return this._inputType;
        }

        set inputType(value) {
            this._inputType = value || 'text';
        }

        /**
         * 清空输入框
         */
        clear() {
            this.value = '';
        }

        /**
         * 🔑 属性面板同步方法 - 支持InputModel的属性同步
         */
        setValue(newValue) {
            this.value = newValue;
        }

        setPlaceholder(newPlaceholder) {
            this.placeholder = newPlaceholder;
        }

        setInputType(newType) {
            this.inputType = newType;
            this.redrawAll(); // 重新绘制以应用新的输入类型
        }

        setMaxLength(newMaxLength) {
            this._maxLength = newMaxLength;
        }

        setReadonly(newReadonly) {
            this.readonly = newReadonly;
        }

        setFontSize(newFontSize) {
            this._fontSize = newFontSize;
            this.redrawAll();
        }

        setTextColor(newColor) {
            this._textColor = newColor;
            this.redrawAll();
        }

        setPlaceholderColor(newColor) {
            this._placeholderColor = newColor;
            this.redrawAll();
        }

        setBackgroundColor(newColor) {
            this._backgroundColor = newColor;
            this.redrawAll();
        }

        setBorderColor(newColor) {
            this._borderColor = newColor;
            this.redrawAll();
        }

        setFocusBorderColor(newColor) {
            this._focusBorderColor = newColor;
            if (this._focused) {
                this.redrawAll();
            }
        }

        setBorderWidth(newWidth) {
            this._borderWidth = newWidth;
            this.redrawAll();
        }

        setBorderRadius(newRadius) {
            this._borderRadius = newRadius;
            this.redrawAll();
        }

        setLetterSpacing(newSpacing) {
            this._letterSpacing = newSpacing;
            this.redrawAll();
        }

        setValidationPattern(newPattern) {
            this._validationPattern = newPattern;
        }

        setValidationMessage(newMessage) {
            this._validationMessage = newMessage;
        }

        /**
         * 选择所有文本
         */
        selectAll() {
            this._selectionStart = 0;
            this._selectionEnd = this._value.length;
            this.setCursorPosition(this._value.length);
        }

        /**
         * 验证当前值
         */
        validate() {
            if (!this._validationPattern) return true;

            const isValid = this._validationPattern.test(this._value);
            if (!isValid && this.executeScript) {
                this.executeScript('onValidationFailed', this._validationMessage);
            }
            return isValid;
        }

        /**
         * 🔑 更新方法 - 支持脚本执行 (参考UILabel)
         */
        update() {
            // 调用父类更新方法（如果存在）
            if (super.update) {
                super.update();
            }

            // 光标闪烁逻辑
            if (this._focused) {
                this._cursorBlinkTimer++;
                if (this._cursorBlinkTimer >= 30) { // 约0.5秒闪烁一次
                    this._cursorVisible = !this._cursorVisible;
                    this._cursorBlinkTimer = 0;
                    this.updateCursor();
                }
            }

            // 🔑 执行更新脚本
            if (this.executeScript) {
                this.executeScript('onUpdate');
            }
        }

        /**
         * 🔑 克隆UIInput对象 (参考UILabel的clone实现)
         * @param {Object} options 克隆选项
         * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
         * @param {number} options.offsetX 水平偏移量 (默认: 20)
         * @param {number} options.offsetY 垂直偏移量 (默认: 20)
         * @returns {UIInput} 克隆的 UIInput 对象
         */
        clone(options = {}) {
            console.log('🔄 UIInput: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 创建新的UIInput对象
            const clonedInput = new UIInput({
                // 基础属性
                width: this.width,
                height: this.height,
                visible: this.visible,

                // 🔑 UIComponent 属性（安全访问）
                name: this.name || '',
                enabled: this.enabled !== false,

                // 🔑 深度克隆组件脚本数组
                componentScripts: this.componentScripts ?
                    this.componentScripts.map(script => ({
                        id: script.id,
                        name: script.name,
                        type: script.type,
                        enabled: script.enabled,
                        code: script.code,
                        description: script.description
                    })) : [],

                // UIInput 特有属性
                value: this._value,
                placeholder: this._placeholder,
                inputType: this._inputType,
                maxLength: this._maxLength,
                readonly: this._readonly,
                fontSize: this._fontSize,
                textColor: this._textColor,
                placeholderColor: this._placeholderColor,
                backgroundColor: this._backgroundColor,
                borderColor: this._borderColor,
                focusBorderColor: this._focusBorderColor,
                borderWidth: this._borderWidth,
                borderRadius: this._borderRadius,
                letterSpacing: this._letterSpacing,
                validationPattern: this._validationPattern,
                validationMessage: this._validationMessage
            });

            // 2. 设置位置和变换属性
            clonedInput.x = this.x + (offsetPosition ? offsetX : 0);
            clonedInput.y = this.y + (offsetPosition ? offsetY : 0);
            clonedInput.scale.x = this.scale.x;
            clonedInput.scale.y = this.scale.y;
            clonedInput.rotation = this.rotation;
            clonedInput.alpha = this.alpha;
            clonedInput.anchor.x = this.anchor.x;
            clonedInput.anchor.y = this.anchor.y;
            clonedInput.pivot.x = this.pivot.x;
            clonedInput.pivot.y = this.pivot.y;
            clonedInput.skew.x = this.skew.x;
            clonedInput.skew.y = this.skew.y;
            clonedInput.zIndex = this.zIndex;

            console.log('✅ UIInput: 克隆完成', {
                originalValue: this._value,
                clonedValue: clonedInput._value,
                position: `(${clonedInput.x}, ${clonedInput.y})`
            });

            return clonedInput;
        }

        /**
         * 🔑 销毁时清理资源 (参考UILabel的destroy实现)
         */
        destroy(options) {
            console.log('🗑️ UIInput: 开始销毁输入框组件');

            // 🔑 安全执行销毁脚本
            try {
                if (this.executeScript && !this._isDestroying) {
                    this._isDestroying = true; // 防止重复销毁
                    this.executeScript('onDestroy');
                }
            } catch (error) {
                console.warn('⚠️ UIInput: 销毁脚本执行失败', error);
            }

            try {
                // 失去焦点并清理事件监听器
                this.blur();

                // 🔑 清理RPG Maker MZ循环事件
                if (this._tickerCallback && typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                    Graphics.app.ticker.remove(this._tickerCallback);
                    this._tickerCallback = null;
                    console.log('🗑️ UIInput: 已从 PIXI ticker 循环中移除');
                }

                // 🔑 修复：清理Bitmap和Sprite对象
                if (this.backgroundBitmap) {
                    this.backgroundBitmap.destroy();
                    this.backgroundBitmap = null;
                }

                if (this.backgroundSprite) {
                    this.backgroundSprite.destroy();
                    this.backgroundSprite = null;
                }

                if (this.cursorBitmap) {
                    this.cursorBitmap.destroy();
                    this.cursorBitmap = null;
                }

                if (this.cursorSprite) {
                    this.cursorSprite.destroy();
                    this.cursorSprite = null;
                }

                if (this.textBitmap) {
                    this.textBitmap.destroy();
                    this.textBitmap = null;
                }

                if (this.textSprite) {
                    this.textSprite.destroy();
                    this.textSprite = null;
                }

                // 调用父类销毁方法
                super.destroy(options);

                console.log('✅ UIInput: 输入框组件销毁完成');
            } catch (error) {
                console.error('❌ UIInput: 销毁过程中出错', error);
            }
        }

        /**
         * 获取组件属性信息 (用于调试和序列化)
         */
        getProperties() {
            return {
                // 基础属性
                x: this.x,
                y: this.y,
                width: this.width,
                height: this.height,
                visible: this.visible,

                // UIInput 特有属性
                value: this._value,
                placeholder: this._placeholder,
                inputType: this._inputType,
                maxLength: this._maxLength,
                readonly: this._readonly,
                fontSize: this._fontSize,
                textColor: this._textColor,
                placeholderColor: this._placeholderColor,
                backgroundColor: this._backgroundColor,
                borderColor: this._borderColor,
                focusBorderColor: this._focusBorderColor,
                borderWidth: this._borderWidth,
                borderRadius: this._borderRadius,
                letterSpacing: this._letterSpacing,
                focused: this._focused,
                cursorPosition: this._cursorPosition
            };
        }
    }

    // 导出到全局
    window.UIInput = UIInput;
    console.log('✅ UIInput 插件 v1.0.0 加载完成');

})();
