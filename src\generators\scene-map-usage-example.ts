/**
 * Scene_Map 特殊处理使用示例
 * 展示如何在代码生成时应用Scene_Map的特殊处理
 */

import { generatePluginCode } from './pluginGenerator';
import type { SceneModel } from '../type/senceModel.svelte';

/**
 * 使用示例：生成包含Scene_Map特殊处理的插件代码
 */
export async function generateSceneMapPlugin() {
  // 创建一个模拟的Scene_Map场景模型
  const sceneModels = new Map<string, SceneModel>();
  
  // 假设我们有一个Scene_Map的场景模型
  // 在实际使用中，这会从编辑器的状态中获取
  const sceneMapModel = {
    className: 'Scene_Map',
    children: [
      // 这里会包含编辑器中创建的UI组件
      // 例如：自定义按钮、标签、图片等
    ]
  } as SceneModel;
  
  sceneModels.set('Scene_Map', sceneMapModel);
  
  // 生成插件代码
  const pluginCode = await generatePluginCode(sceneModels, {
    pluginName: 'CustomSceneMapPlugin',
    pluginDescription: 'Scene_Map with custom UI components',
    author: 'RPG Editor',
    version: '1.0.0'
  });
  
  console.log('生成的插件代码包含Scene_Map特殊处理：');
  console.log('- 重写了 createDisplayObjects 方法');
  console.log('- 跳过了 this.createAllWindows() 调用');
  console.log('- 保留了其他必要的初始化步骤');
  
  return pluginCode;
}

/**
 * 关键特性说明：
 * 
 * 1. createDisplayObjects 方法重写
 *    - 保留：this.createSpriteset()
 *    - 保留：this.createWindowLayer()
 *    - 跳过：this.createAllWindows()  // 这是关键！
 *    - 保留：this.createButtons()
 * 
 * 2. 为什么要跳过 createAllWindows()？
 *    - 避免创建原生的消息窗口、菜单窗口等
 *    - 使用编辑器中自定义的UI组件替代
 *    - 防止原生窗口与自定义UI的冲突
 * 
 * 3. 其他重写的方法：
 *    - createSpriteset(): 跳过原生精灵集创建
 *    - createAllWindows(): 跳过原生窗口创建
 *    - update(): 安全检查精灵集
 *    - updateTransferPlayer(): 安全检查传送
 * 
 * 4. 使用场景：
 *    - 完全自定义的地图界面
 *    - 替换原生UI系统
 *    - 创建独特的游戏体验
 */

/**
 * 生成的代码结构示例：
 * 
 * Scene_Map.prototype.createDisplayObjects = function() {
 *   console.log('RPG Editor: 使用自定义的 createDisplayObjects 方法');
 *   this.createSpriteset();      // 保留
 *   this.createWindowLayer();    // 保留
 *   // 注意：不调用 this.createAllWindows(); - 跳过原生窗口创建
 *   this.createButtons();        // 保留
 *   console.log('RPG Editor: createDisplayObjects 完成，已跳过原生窗口创建');
 * };
 */
