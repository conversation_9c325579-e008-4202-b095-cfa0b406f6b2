<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIMask 简化版本测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
        }
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .controls button:hover {
            background: #45a049;
        }
        .canvas-container {
            border: 2px solid #ccc;
            display: inline-block;
            background: #fff;
        }
        #testCanvas {
            display: block;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🎭 UIMask 简化版本测试</h1>
    
    <div class="test-container">
        <h2>测试说明</h2>
        <p>这个测试验证简化后的UIMask功能：</p>
        <ul>
            <li>✅ 移除了 maskWidth, maskHeight, offsetX, offsetY 参数</li>
            <li>✅ UIMask对象的 width 和 height 直接就是遮罩的大小</li>
            <li>✅ 遮罩从 (0,0) 开始绘制，覆盖整个对象区域</li>
            <li>✅ 子对象自动应用遮罩效果</li>
        </ul>
    </div>

    <div class="test-container">
        <h2>控制面板</h2>
        <div class="controls">
            <button onclick="createMask()">创建遮罩</button>
            <button onclick="addChildToMask()">添加子对象</button>
            <button onclick="resizeMask()">调整遮罩大小</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="canvas-container">
            <canvas id="testCanvas" width="600" height="400"></canvas>
        </div>
        
        <div id="log" class="log">
            <div>📋 测试日志:</div>
        </div>
    </div>

    <script src="Project6/js/libs/pixi.js"></script>
    <script src="src/engine/js/plugins/uiMask.js"></script>
    <script>
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // 全局变量
        let app = null;
        let testMask = null;
        let childCount = 0;
        
        // 初始化PIXI应用
        window.onload = function() {
            const canvas = document.getElementById('testCanvas');
            
            app = new PIXI.Application({
                view: canvas,
                width: 600,
                height: 400,
                backgroundColor: 0xf0f0f0
            });
            
            log('🚀 PIXI应用初始化完成');
            log('📦 UIMask插件已加载');
        };
        
        function createMask() {
            if (testMask) {
                app.stage.removeChild(testMask);
                testMask.destroy();
            }
            
            log('🎭 创建简化版UIMask...');
            
            try {
                // 🔑 使用简化的参数创建遮罩
                testMask = new UIMask({
                    width: 200,      // 直接设置遮罩大小
                    height: 150,
                    maskType: 'rectangle',
                    maskImage: ''
                });
                
                testMask.x = 100;
                testMask.y = 50;
                testMask.name = 'TestMask';
                
                app.stage.addChild(testMask);
                
                log('✅ UIMask创建成功');
                log(`📏 遮罩尺寸: ${testMask.width}x${testMask.height}`);
                log(`📍 遮罩位置: (${testMask.x}, ${testMask.y})`);
                
                // 显示遮罩信息
                const maskInfo = testMask.getMaskInfo();
                log(`🔍 遮罩信息: ${JSON.stringify(maskInfo)}`);
                
                childCount = 0;
                
            } catch (error) {
                log(`❌ 创建UIMask失败: ${error.message}`);
            }
        }
        
        function addChildToMask() {
            if (!testMask) {
                log('❌ 请先创建遮罩');
                return;
            }
            
            childCount++;
            log(`➕ 添加子对象 #${childCount}...`);
            
            try {
                // 创建一个简单的图形作为子对象
                const child = new PIXI.Graphics();
                child.beginFill(0x4CAF50);
                child.drawRect(0, 0, 80, 60);
                child.endFill();
                
                // 设置子对象位置（相对于遮罩）
                child.x = (childCount - 1) * 30;
                child.y = (childCount - 1) * 20;
                child.name = `Child${childCount}`;
                
                // 🔑 添加到遮罩中（会自动应用遮罩效果）
                testMask.addChild(child);
                
                log(`✅ 子对象 #${childCount} 添加成功`);
                log(`📍 子对象位置: (${child.x}, ${child.y})`);
                log(`🎭 遮罩已自动应用到子对象`);
                
            } catch (error) {
                log(`❌ 添加子对象失败: ${error.message}`);
            }
        }
        
        function resizeMask() {
            if (!testMask) {
                log('❌ 请先创建遮罩');
                return;
            }
            
            log('🔧 调整遮罩大小...');
            
            try {
                // 🔑 直接修改width和height来调整遮罩大小
                const newWidth = 300;
                const newHeight = 200;
                
                log(`📏 调整前: ${testMask.width}x${testMask.height}`);
                
                testMask.updateMaskSize(newWidth, newHeight);
                
                log(`📏 调整后: ${testMask.width}x${testMask.height}`);
                log('✅ 遮罩大小调整完成');
                
            } catch (error) {
                log(`❌ 调整遮罩大小失败: ${error.message}`);
            }
        }
        
        function clearLog() {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML = '<div>📋 测试日志:</div>';
        }
    </script>
</body>
</html>
