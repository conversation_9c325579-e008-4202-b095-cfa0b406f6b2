use image::{<PERSON><PERSON><PERSON><PERSON>, ImageB<PERSON><PERSON>, <PERSON><PERSON><PERSON>, RgbaImage};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

// 删除了不再使用的 PixelSegment 和 UIObject 结构体
// 现在使用洪水填充算法，不需要这些中间数据结构

/// UI元素类型
#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub enum UIElementType {
    Button,
    Icon,
    Text,
    Panel,
    Unknown,
}

/// 边界框
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BoundingBox {
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
}

/// 元素属性
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ElementProperties {
    pub has_border: bool,
    pub has_shadow: bool,
    pub dominant_colors: Vec<String>,
    pub pixel_count: u32,
}

/// 检测到的UI元素
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DetectedUIElement {
    pub id: String,
    pub bounds: BoundingBox,
    pub confidence: f32,
    pub element_type: UIElementType,
    pub properties: ElementProperties,
}

/// 裁切选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CropOptions {
    pub min_width: u32,
    pub min_height: u32,
    pub alpha_tolerance: u8,
    pub color_tolerance: u8,
    pub merge_distance: u32,
}

impl Default for CropOptions {
    fn default() -> Self {
        Self {
            min_width: 8,  // 降低最小宽度，检测更小的UI元素
            min_height: 8, // 降低最小高度
            alpha_tolerance: 50, // 提高Alpha容差，检测半透明元素
            color_tolerance: 60, // 提高颜色容差，检测渐变色元素
            merge_distance: 1,   // 进一步减少合并距离，只合并真正相邻的元素
        }
    }
}

/// 智能裁切分析结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SmartCropResponse {
    pub success: bool,
    pub elements: Vec<DetectedUIElement>,
    pub processing_time: u64,
    pub error: Option<String>,
}

/// 图像分析器
pub struct ImageAnalyzer {
    options: CropOptions,
}

impl ImageAnalyzer {
    pub fn new(options: CropOptions) -> Self {
        Self { options }
    }

    /// 分析图像并检测UI元素
    pub fn analyze_image(&self, image: &DynamicImage) -> SmartCropResponse {
        let start_time = std::time::Instant::now();
        
        match self.detect_ui_elements(image) {
            Ok(elements) => SmartCropResponse {
                success: true,
                elements,
                processing_time: start_time.elapsed().as_millis() as u64,
                error: None,
            },
            Err(e) => SmartCropResponse {
                success: false,
                elements: vec![],
                processing_time: start_time.elapsed().as_millis() as u64,
                error: Some(e.to_string()),
            },
        }
    }

    /// 检测UI元素的核心算法 - 使用改进的多阶段分析
    fn detect_ui_elements(&self, image: &DynamicImage) -> Result<Vec<DetectedUIElement>, Box<dyn std::error::Error>> {
        let rgba_image = image.to_rgba8();
        let (width, height) = rgba_image.dimensions();

        println!("🔍 开始多阶段UI元素检测，图像尺寸: {}x{}", width, height);

        // 1. 改进的二值化处理
        let binary_image = self.advanced_binarization(&rgba_image);
        println!("🔧 改进二值化完成");

        // 2. 多尺度连通组件分析
        let elements = self.multi_scale_connected_components(&binary_image, width, height);
        println!("✅ 多尺度分析完成，检测到 {} 个UI元素", elements.len());

        // 移除调试可视化图片保存，避免触发热重载
        // self.save_debug_visualization(image, &elements);

        Ok(elements)
    }

    /// 将RGBA图像转换为严格的二值图像
    fn convert_to_binary_strict(&self, image: &RgbaImage) -> ImageBuffer<image::Luma<u8>, Vec<u8>> {
        let (width, height) = image.dimensions();
        let mut binary = ImageBuffer::new(width, height);

        println!("🎨 使用改进的二值化算法，同时考虑颜色和透明度");

        for (x, y, pixel) in image.enumerate_pixels() {
            let [r, g, b, alpha] = pixel.0;

            // 改进的二值化：平衡检测精度和边界完整性
            let color_intensity = (r as f32 + g as f32 + b as f32) / 3.0;

            // 平衡的前景检测：保持边界完整但减少噪点
            let is_foreground = (alpha > 100) || // 中等透明度阈值
                               (alpha > 30 && color_intensity > 30.0) || // 保留边缘抗锯齿
                               (color_intensity > 80.0) || // 中等强颜色阈值
                               (alpha > 20 && (r > 100 || g > 100 || b > 100)); // 保留有色边缘

            let value = if is_foreground { 255 } else { 0 };
            binary.put_pixel(x, y, image::Luma([value]));
        }

        binary
    }

    /// 后处理：分离可能合并的元素
    fn post_process_and_split_elements(&self, elements: Vec<DetectedUIElement>) -> Vec<DetectedUIElement> {
        let mut processed_elements = Vec::new();

        for element in elements {
            // 检查是否需要分割
            if self.should_split_element_by_aspect_ratio(&element) {
                let split_elements = self.split_element_by_aspect_ratio(&element);
                processed_elements.extend(split_elements);
            } else {
                processed_elements.push(element);
            }
        }

        processed_elements
    }

    /// 根据长宽比判断是否需要分割元素
    fn should_split_element_by_aspect_ratio(&self, element: &DetectedUIElement) -> bool {
        let bounds = &element.bounds;
        let aspect_ratio = if bounds.height > 0 {
            bounds.width as f32 / bounds.height as f32
        } else {
            1.0
        };

        // 如果长宽比过于极端，可能是多个元素合并
        aspect_ratio > 4.0 || aspect_ratio < 0.25
    }

    /// 根据长宽比分割元素
    fn split_element_by_aspect_ratio(&self, element: &DetectedUIElement) -> Vec<DetectedUIElement> {
        let bounds = &element.bounds;
        let mut split_elements = Vec::new();

        let aspect_ratio = if bounds.height > 0 {
            bounds.width as f32 / bounds.height as f32
        } else {
            1.0
        };

        if aspect_ratio > 4.0 {
            // 水平分割
            let split_count = (aspect_ratio / 2.0).ceil() as u32;
            let sub_width = bounds.width / split_count;

            for i in 0..split_count {
                let sub_element = DetectedUIElement {
                    id: format!("{}_h{}", element.id, i),
                    bounds: BoundingBox {
                        x: bounds.x + i * sub_width,
                        y: bounds.y,
                        width: if i == split_count - 1 { bounds.width - i * sub_width } else { sub_width },
                        height: bounds.height,
                    },
                    confidence: element.confidence * 0.8, // 降低置信度
                    element_type: element.element_type.clone(),
                    properties: element.properties.clone(),
                };
                split_elements.push(sub_element);
            }
        } else if aspect_ratio < 0.25 {
            // 垂直分割
            let split_count = ((1.0 / aspect_ratio) / 2.0).ceil() as u32;
            let sub_height = bounds.height / split_count;

            for i in 0..split_count {
                let sub_element = DetectedUIElement {
                    id: format!("{}_v{}", element.id, i),
                    bounds: BoundingBox {
                        x: bounds.x,
                        y: bounds.y + i * sub_height,
                        width: bounds.width,
                        height: if i == split_count - 1 { bounds.height - i * sub_height } else { sub_height },
                    },
                    confidence: element.confidence * 0.8, // 降低置信度
                    element_type: element.element_type.clone(),
                    properties: element.properties.clone(),
                };
                split_elements.push(sub_element);
            }
        } else {
            split_elements.push(element.clone());
        }

        split_elements
    }

    /// 智能分离算法：检测并分离粘连的UI元素
    fn smart_separation(&self, image: &ImageBuffer<image::Luma<u8>, Vec<u8>>) -> ImageBuffer<image::Luma<u8>, Vec<u8>> {
        let (width, height) = image.dimensions();
        let mut result = image.clone();

        // 检测窄连接点（连接两个较大区域的细线）
        for y in 1..(height - 1) {
            for x in 1..(width - 1) {
                if image.get_pixel(x, y)[0] > 128 {
                    if self.is_narrow_connection(image, x, y) {
                        // 断开窄连接
                        result.put_pixel(x, y, image::Luma([0]));
                    }
                }
            }
        }

        result
    }

    /// 检测是否为窄连接点
    fn is_narrow_connection(&self, image: &ImageBuffer<image::Luma<u8>, Vec<u8>>, x: u32, y: u32) -> bool {
        // 检查3x3邻域
        let mut foreground_count = 0;
        let mut pattern_score = 0;

        for dy in -1i32..=1 {
            for dx in -1i32..=1 {
                let nx = (x as i32 + dx) as u32;
                let ny = (y as i32 + dy) as u32;

                if nx < image.width() && ny < image.height() {
                    if image.get_pixel(nx, ny)[0] > 128 {
                        foreground_count += 1;

                        // 检查特定模式：水平或垂直连接
                        if (dx == 0 && dy != 0) || (dx != 0 && dy == 0) {
                            pattern_score += 2;
                        } else if dx == 0 && dy == 0 {
                            pattern_score += 1;
                        }
                    }
                }
            }
        }

        // 如果前景像素较少且主要是线性连接，则认为是窄连接
        foreground_count <= 5 && pattern_score >= 3 && self.has_separated_regions_around(image, x, y)
    }

    /// 检查周围是否有分离的区域
    fn has_separated_regions_around(&self, image: &ImageBuffer<image::Luma<u8>, Vec<u8>>, x: u32, y: u32) -> bool {
        // 检查5x5邻域，看是否有分离的前景区域
        let mut regions = Vec::new();

        for dy in -2i32..=2 {
            for dx in -2i32..=2 {
                if dx.abs() <= 1 && dy.abs() <= 1 {
                    continue; // 跳过中心3x3区域
                }

                let nx = x as i32 + dx;
                let ny = y as i32 + dy;

                if nx >= 0 && ny >= 0 && (nx as u32) < image.width() && (ny as u32) < image.height() {
                    if image.get_pixel(nx as u32, ny as u32)[0] > 128 {
                        regions.push((nx, ny));
                    }
                }
            }
        }

        // 如果外围有前景像素，可能是连接了分离的区域
        regions.len() >= 2
    }

    /// 边缘检测算法
    fn detect_edges(&self, image: &RgbaImage) -> ImageBuffer<image::Luma<u8>, Vec<u8>> {
        let (width, height) = image.dimensions();
        let mut edge_image = ImageBuffer::new(width, height);

        // 使用Sobel算子进行边缘检测
        for y in 1..(height - 1) {
            for x in 1..(width - 1) {
                let intensity = self.calculate_edge_intensity(image, x, y);
                let value = if intensity > 30.0 { 255 } else { 0 };
                edge_image.put_pixel(x, y, image::Luma([value]));
            }
        }

        edge_image
    }

    /// 计算边缘强度
    fn calculate_edge_intensity(&self, image: &RgbaImage, x: u32, y: u32) -> f32 {
        // Sobel算子
        let sobel_x = [
            [-1, 0, 1],
            [-2, 0, 2],
            [-1, 0, 1],
        ];
        let sobel_y = [
            [-1, -2, -1],
            [0, 0, 0],
            [1, 2, 1],
        ];

        let mut gx = 0.0;
        let mut gy = 0.0;

        for dy in -1i32..=1 {
            for dx in -1i32..=1 {
                let nx = (x as i32 + dx) as u32;
                let ny = (y as i32 + dy) as u32;

                let pixel = image.get_pixel(nx, ny);
                let intensity = (pixel[0] as f32 + pixel[1] as f32 + pixel[2] as f32) / 3.0;

                let kernel_x = sobel_x[(dy + 1) as usize][(dx + 1) as usize] as f32;
                let kernel_y = sobel_y[(dy + 1) as usize][(dx + 1) as usize] as f32;

                gx += intensity * kernel_x;
                gy += intensity * kernel_y;
            }
        }

        (gx * gx + gy * gy).sqrt()
    }

    /// 轮廓提取
    fn extract_contours(&self, edge_image: &ImageBuffer<image::Luma<u8>, Vec<u8>>) -> Vec<Vec<(u32, u32)>> {
        let (width, height) = edge_image.dimensions();
        let mut visited = vec![vec![false; width as usize]; height as usize];
        let mut contours = Vec::new();

        for y in 0..height {
            for x in 0..width {
                if !visited[y as usize][x as usize] && edge_image.get_pixel(x, y)[0] > 128 {
                    let contour = self.trace_contour(edge_image, &mut visited, x, y, width, height);
                    if contour.len() > 10 { // 过滤太小的轮廓
                        contours.push(contour);
                    }
                }
            }
        }

        contours
    }

    /// 轮廓跟踪
    fn trace_contour(&self, edge_image: &ImageBuffer<image::Luma<u8>, Vec<u8>>, visited: &mut Vec<Vec<bool>>, start_x: u32, start_y: u32, width: u32, height: u32) -> Vec<(u32, u32)> {
        let mut contour = Vec::new();
        let mut stack = vec![(start_x, start_y)];

        while let Some((x, y)) = stack.pop() {
            if x >= width || y >= height || visited[y as usize][x as usize] {
                continue;
            }

            if edge_image.get_pixel(x, y)[0] <= 128 {
                continue;
            }

            visited[y as usize][x as usize] = true;
            contour.push((x, y));

            // 8连通邻域
            for dy in -1i32..=1 {
                for dx in -1i32..=1 {
                    if dx == 0 && dy == 0 { continue; }

                    let nx = x as i32 + dx;
                    let ny = y as i32 + dy;

                    if nx >= 0 && ny >= 0 && (nx as u32) < width && (ny as u32) < height {
                        stack.push((nx as u32, ny as u32));
                    }
                }
            }
        }

        contour
    }

    /// 轮廓分析和转换为UI元素
    fn analyze_contours(&self, contours: Vec<Vec<(u32, u32)>>, width: u32, height: u32) -> Vec<DetectedUIElement> {
        let mut elements = Vec::new();

        for (index, contour) in contours.iter().enumerate() {
            if contour.len() < 20 { // 过滤太小的轮廓
                continue;
            }

            // 计算边界框
            let min_x = contour.iter().map(|(x, _)| *x).min().unwrap_or(0);
            let max_x = contour.iter().map(|(x, _)| *x).max().unwrap_or(0);
            let min_y = contour.iter().map(|(_, y)| *y).min().unwrap_or(0);
            let max_y = contour.iter().map(|(_, y)| *y).max().unwrap_or(0);

            let bounds_width = max_x - min_x + 1;
            let bounds_height = max_y - min_y + 1;

            // 过滤太小或太大的区域
            if bounds_width < 8 || bounds_height < 8 || bounds_width > width / 2 || bounds_height > height / 2 {
                continue;
            }

            // 计算轮廓特征
            let area = self.calculate_contour_area(&contour);
            let perimeter = contour.len() as f32;
            let compactness = if perimeter > 0.0 {
                4.0 * std::f32::consts::PI * area / (perimeter * perimeter)
            } else {
                0.0
            };

            // 分类UI元素类型
            let element_type = self.classify_by_contour_features(bounds_width, bounds_height, area, compactness);

            // 计算置信度
            let confidence = self.calculate_contour_confidence(bounds_width, bounds_height, area, compactness);

            let element = DetectedUIElement {
                id: uuid::Uuid::new_v4().to_string(),
                bounds: BoundingBox {
                    x: min_x,
                    y: min_y,
                    width: bounds_width,
                    height: bounds_height,
                },
                confidence,
                element_type: element_type.clone(),
                properties: ElementProperties {
                    has_border: true, // 基于边缘检测，默认有边框
                    has_shadow: false,
                    dominant_colors: vec![],
                    pixel_count: area as u32,
                },
            };

            println!("🔍 轮廓 {}: {}x{} at ({}, {}), 面积: {:.1}, 紧凑度: {:.2}, 类型: {:?}",
                    index, bounds_width, bounds_height, min_x, min_y, area, compactness, element_type);

            elements.push(element);
        }

        elements
    }

    /// 计算轮廓面积（使用简化的边界框面积）
    fn calculate_contour_area(&self, contour: &[(u32, u32)]) -> f32 {
        if contour.is_empty() {
            return 0.0;
        }

        let min_x = contour.iter().map(|(x, _)| *x).min().unwrap_or(0);
        let max_x = contour.iter().map(|(x, _)| *x).max().unwrap_or(0);
        let min_y = contour.iter().map(|(_, y)| *y).min().unwrap_or(0);
        let max_y = contour.iter().map(|(_, y)| *y).max().unwrap_or(0);

        ((max_x - min_x + 1) * (max_y - min_y + 1)) as f32
    }

    /// 基于轮廓特征分类UI元素
    fn classify_by_contour_features(&self, width: u32, height: u32, area: f32, compactness: f32) -> UIElementType {
        let aspect_ratio = if height > 0 {
            width as f32 / height as f32
        } else {
            1.0
        };

        // 基于尺寸和形状特征分类
        if area < 400.0 { // 小元素
            if compactness > 0.7 {
                UIElementType::Icon // 紧凑的小元素是图标
            } else {
                UIElementType::Text // 不紧凑的小元素可能是文本
            }
        } else if area < 2500.0 { // 中等元素
            if aspect_ratio > 2.0 || aspect_ratio < 0.5 {
                UIElementType::Text // 长条形是文本
            } else {
                UIElementType::Button // 方形是按钮
            }
        } else { // 大元素
            if aspect_ratio > 3.0 || aspect_ratio < 0.33 {
                UIElementType::Panel // 极端长宽比是面板
            } else {
                UIElementType::Panel // 大的方形区域是面板
            }
        }
    }

    /// 计算轮廓置信度
    fn calculate_contour_confidence(&self, width: u32, height: u32, area: f32, compactness: f32) -> f32 {
        let size_score = (area / 1000.0).min(1.0);
        let shape_score = compactness;
        let aspect_ratio = if height > 0 {
            width as f32 / height as f32
        } else {
            1.0
        };
        let ratio_score = if aspect_ratio > 0.2 && aspect_ratio < 5.0 { 1.0 } else { 0.5 };

        (size_score * 0.4 + shape_score * 0.4 + ratio_score * 0.2).max(0.1).min(1.0)
    }

    /// 改进的二值化处理
    fn advanced_binarization(&self, image: &RgbaImage) -> ImageBuffer<image::Luma<u8>, Vec<u8>> {
        let (width, height) = image.dimensions();
        let mut binary = ImageBuffer::new(width, height);

        // 计算全局阈值（Otsu方法的简化版本）
        let threshold = self.calculate_otsu_threshold(image);
        println!("🔧 计算得到的自适应阈值: {}", threshold);

        for y in 0..height {
            for x in 0..width {
                let pixel = image.get_pixel(x, y);
                let [r, g, b, a] = pixel.0;

                // 综合考虑颜色强度和透明度，更好地保留边缘
                let intensity = (r as f32 + g as f32 + b as f32) / 3.0;
                let alpha_factor = a as f32 / 255.0;
                let weighted_intensity = intensity * alpha_factor;

                // 使用更宽松的阈值以保留边缘像素
                let is_foreground = weighted_intensity > (threshold * 0.8) || // 降低阈值
                                   a > 150 || // 降低透明度阈值
                                   (a > 50 && intensity > 30.0); // 保留半透明边缘
                let value = if is_foreground { 255 } else { 0 };
                binary.put_pixel(x, y, image::Luma([value]));
            }
        }

        binary
    }

    /// 多尺度连通组件分析
    fn multi_scale_connected_components(&self, binary_image: &ImageBuffer<image::Luma<u8>, Vec<u8>>, width: u32, height: u32) -> Vec<DetectedUIElement> {
        let mut elements = Vec::new();
        let mut visited = vec![vec![false; width as usize]; height as usize];

        println!("🔧 开始多尺度连通组件分析...");

        // 使用改进的洪水填充算法
        for y in 0..height {
            for x in 0..width {
                if !visited[y as usize][x as usize] && binary_image.get_pixel(x, y)[0] > 128 {
                    if let Some(element) = self.improved_flood_fill(binary_image, &mut visited, x, y, width, height) {
                        elements.push(element);
                    }
                }
            }
        }

        // 后处理：合并相邻的相似元素
        let merged_elements = self.merge_similar_elements(elements);
        println!("🔧 合并后剩余 {} 个元素", merged_elements.len());

        merged_elements
    }

    /// 基于OpenCV特征分类UI元素
    fn classify_opencv_element(&self, area: i32, aspect_ratio: f32, fill_ratio: f32) -> UIElementType {
        // 基于面积、长宽比和填充率进行分类
        if area < 400 { // 小元素
            if fill_ratio > 0.8 && aspect_ratio > 0.8 && aspect_ratio < 1.25 {
                UIElementType::Icon // 方形且填充率高的小元素是图标
            } else {
                UIElementType::Text // 其他小元素可能是文本
            }
        } else if area < 2500 { // 中等元素
            if aspect_ratio > 2.5 || aspect_ratio < 0.4 {
                UIElementType::Text // 极端长宽比是文本
            } else if fill_ratio > 0.7 {
                UIElementType::Button // 填充率高的中等元素是按钮
            } else {
                UIElementType::Icon // 填充率低的可能是有边框的图标
            }
        } else { // 大元素
            if aspect_ratio > 3.0 || aspect_ratio < 0.33 {
                UIElementType::Panel // 极端长宽比的大元素是面板
            } else {
                UIElementType::Panel // 其他大元素也是面板
            }
        }
    }

    /// 计算OpenCV元素的置信度
    fn calculate_opencv_confidence(&self, area: i32, aspect_ratio: f32, fill_ratio: f32) -> f32 {
        // 面积得分：中等大小的元素得分更高
        let size_score = if area < 100 {
            0.3 // 太小
        } else if area < 1000 {
            1.0 // 理想大小
        } else if area < 5000 {
            0.8 // 较大
        } else {
            0.4 // 太大
        };

        // 长宽比得分：接近正常比例的得分更高
        let ratio_score = if aspect_ratio > 0.2 && aspect_ratio < 5.0 {
            1.0 // 正常比例
        } else {
            0.5 // 极端比例
        };

        // 填充率得分：适中的填充率得分更高
        let fill_score = if fill_ratio > 0.3 && fill_ratio < 0.95 {
            1.0 // 正常填充
        } else {
            0.6 // 极端填充
        };

        {
            let result = size_score * 0.4f32 + ratio_score * 0.3f32 + fill_score * 0.3f32;
            result.max(0.1f32).min(1.0f32)
        }
    }

    /// 计算Otsu阈值（简化版本）
    fn calculate_otsu_threshold(&self, image: &RgbaImage) -> f32 {
        let (width, height) = image.dimensions();
        let mut histogram = vec![0; 256];

        // 构建直方图
        for y in 0..height {
            for x in 0..width {
                let pixel = image.get_pixel(x, y);
                let [r, g, b, a] = pixel.0;
                let intensity = ((r as f32 + g as f32 + b as f32) / 3.0 * a as f32 / 255.0) as usize;
                if intensity < 256 {
                    histogram[intensity] += 1;
                }
            }
        }

        // 简化的Otsu算法
        let total_pixels = (width * height) as f32;
        let mut best_threshold = 128.0;
        let mut max_variance = 0.0;

        for t in 1..255 {
            let mut w0 = 0.0;
            let mut w1 = 0.0;
            let mut sum0 = 0.0;
            let mut sum1 = 0.0;

            for i in 0..t {
                w0 += histogram[i] as f32;
                sum0 += (i * histogram[i]) as f32;
            }

            for i in t..256 {
                w1 += histogram[i] as f32;
                sum1 += (i * histogram[i]) as f32;
            }

            if w0 > 0.0 && w1 > 0.0 {
                let mean0 = sum0 / w0;
                let mean1 = sum1 / w1;
                let variance = (w0 / total_pixels) * (w1 / total_pixels) * (mean0 - mean1) * (mean0 - mean1);

                if variance > max_variance {
                    max_variance = variance;
                    best_threshold = t as f32;
                }
            }
        }

        best_threshold
    }

    /// 改进的洪水填充算法
    fn improved_flood_fill(&self, image: &ImageBuffer<image::Luma<u8>, Vec<u8>>, visited: &mut Vec<Vec<bool>>, start_x: u32, start_y: u32, width: u32, height: u32) -> Option<DetectedUIElement> {
        let mut pixels = Vec::new();
        let mut stack = vec![(start_x, start_y)];
        let mut min_x = start_x;
        let mut max_x = start_x;
        let mut min_y = start_y;
        let mut max_y = start_y;

        while let Some((x, y)) = stack.pop() {
            if x >= width || y >= height || visited[y as usize][x as usize] {
                continue;
            }

            if image.get_pixel(x, y)[0] <= 128 {
                continue;
            }

            visited[y as usize][x as usize] = true;
            pixels.push((x, y));

            // 更新边界
            min_x = min_x.min(x);
            max_x = max_x.max(x);
            min_y = min_y.min(y);
            max_y = max_y.max(y);

            // 4连通邻域（避免对角连接）
            let neighbors = [
                (x.wrapping_sub(1), y),
                (x + 1, y),
                (x, y.wrapping_sub(1)),
                (x, y + 1),
            ];

            for (nx, ny) in neighbors {
                if nx < width && ny < height {
                    stack.push((nx, ny));
                }
            }
        }

        // 过滤太小的组件
        if pixels.len() < 20 {
            return None;
        }

        let bounds_width = max_x - min_x + 1;
        let bounds_height = max_y - min_y + 1;

        // 过滤不合理的尺寸
        if bounds_width < 5 || bounds_height < 5 || bounds_width > width / 2 || bounds_height > height / 2 {
            return None;
        }

        // 扩展边界框以包含完整边缘
        let expand_pixels = 1; // 向外扩展1像素，精确控制边界
        let expanded_min_x = min_x.saturating_sub(expand_pixels);
        let expanded_max_x = (max_x + expand_pixels).min(width - 1);
        let expanded_min_y = min_y.saturating_sub(expand_pixels);
        let expanded_max_y = (max_y + expand_pixels).min(height - 1);

        let final_width = expanded_max_x - expanded_min_x + 1;
        let final_height = expanded_max_y - expanded_min_y + 1;

        // 计算特征（基于原始检测到的像素）
        let area = pixels.len() as f32;
        let aspect_ratio = bounds_width as f32 / bounds_height as f32;
        let fill_ratio = area / (bounds_width * bounds_height) as f32;

        // 分类和置信度
        let element_type = self.classify_improved_element(area as i32, aspect_ratio, fill_ratio);
        let confidence = self.calculate_improved_confidence(area as i32, aspect_ratio, fill_ratio);

        Some(DetectedUIElement {
            id: uuid::Uuid::new_v4().to_string(),
            bounds: BoundingBox {
                x: expanded_min_x,
                y: expanded_min_y,
                width: final_width,
                height: final_height,
            },
            confidence,
            element_type,
            properties: ElementProperties {
                has_border: fill_ratio < 0.85,
                has_shadow: false,
                dominant_colors: vec![],
                pixel_count: pixels.len() as u32,
            },
        })
    }

    /// 改进的元素分类
    fn classify_improved_element(&self, area: i32, aspect_ratio: f32, fill_ratio: f32) -> UIElementType {
        if area < 300 { // 小元素
            if fill_ratio > 0.8 && aspect_ratio > 0.8 && aspect_ratio < 1.25 {
                UIElementType::Icon // 方形且填充的小元素
            } else {
                UIElementType::Text // 其他小元素
            }
        } else if area < 1500 { // 中等元素
            if aspect_ratio > 2.5 || aspect_ratio < 0.4 {
                UIElementType::Text // 极端长宽比
            } else if fill_ratio > 0.6 {
                UIElementType::Button // 填充较好的中等元素
            } else {
                UIElementType::Icon // 有边框的图标
            }
        } else { // 大元素
            UIElementType::Panel
        }
    }

    /// 改进的置信度计算
    fn calculate_improved_confidence(&self, area: i32, aspect_ratio: f32, fill_ratio: f32) -> f32 {
        let size_score = if area < 50 {
            0.2
        } else if area < 500 {
            1.0
        } else if area < 2000 {
            0.8
        } else {
            0.5
        };

        let ratio_score = if aspect_ratio > 0.2 && aspect_ratio < 5.0 {
            1.0
        } else {
            0.4
        };

        let fill_score = if fill_ratio > 0.3 && fill_ratio < 0.95 {
            1.0
        } else {
            0.6
        };

        {
            let result = size_score * 0.4f32 + ratio_score * 0.3f32 + fill_score * 0.3f32;
            result.max(0.1f32).min(1.0f32)
        }
    }

    /// 合并相似的相邻元素
    fn merge_similar_elements(&self, elements: Vec<DetectedUIElement>) -> Vec<DetectedUIElement> {
        // 简化版本：直接返回原始元素，避免过度合并
        elements
    }

    /// 形态学开运算：先腐蚀再膨胀，用于分离粘连的区域
    fn morphology_open(&self, image: &ImageBuffer<image::Luma<u8>, Vec<u8>>) -> ImageBuffer<image::Luma<u8>, Vec<u8>> {
        // 先腐蚀，分离粘连的区域
        let eroded = self.morphology_erode(image);
        // 再膨胀，恢复原有尺寸
        self.morphology_dilate(&eroded)
    }

    /// 形态学腐蚀操作（使用温和的十字形结构元素）
    fn morphology_erode(&self, image: &ImageBuffer<image::Luma<u8>, Vec<u8>>) -> ImageBuffer<image::Luma<u8>, Vec<u8>> {
        let (width, height) = image.dimensions();
        let mut result = ImageBuffer::new(width, height);

        // 使用增强的十字形结构元素，更有效分离相邻元素
        for y in 1..(height - 1) {
            for x in 1..(width - 1) {
                let current = image.get_pixel(x, y)[0];
                let left = image.get_pixel(x - 1, y)[0];
                let right = image.get_pixel(x + 1, y)[0];
                let up = image.get_pixel(x, y - 1)[0];
                let down = image.get_pixel(x, y + 1)[0];

                // 更严格的腐蚀：要求所有邻居都是前景才保留
                let is_foreground = current > 128 && left > 128 && right > 128 && up > 128 && down > 128;
                let value = if is_foreground { 255 } else { 0 };
                result.put_pixel(x, y, image::Luma([value]));
            }
        }

        // 复制边界
        self.copy_borders(image, &mut result);
        result
    }

    /// 形态学膨胀操作（使用温和的十字形结构元素）
    fn morphology_dilate(&self, image: &ImageBuffer<image::Luma<u8>, Vec<u8>>) -> ImageBuffer<image::Luma<u8>, Vec<u8>> {
        let (width, height) = image.dimensions();
        let mut result = ImageBuffer::new(width, height);

        // 使用十字形结构元素（只包括上下左右，保护边界）
        for y in 1..(height - 1) {
            for x in 1..(width - 1) {
                let current = image.get_pixel(x, y)[0];
                let left = image.get_pixel(x - 1, y)[0];
                let right = image.get_pixel(x + 1, y)[0];
                let up = image.get_pixel(x, y - 1)[0];
                let down = image.get_pixel(x, y + 1)[0];

                // 十字形膨胀：如果当前像素或四个直接邻居中任何一个是前景，则设为前景
                let max_value = current.max(left).max(right).max(up).max(down);
                result.put_pixel(x, y, image::Luma([max_value]));
            }
        }

        // 复制边界
        self.copy_borders(image, &mut result);
        result
    }

    /// 复制图像边界
    fn copy_borders(&self, source: &ImageBuffer<image::Luma<u8>, Vec<u8>>, target: &mut ImageBuffer<image::Luma<u8>, Vec<u8>>) {
        let (width, height) = source.dimensions();

        // 复制上下边界
        for x in 0..width {
            target.put_pixel(x, 0, *source.get_pixel(x, 0));
            if height > 1 {
                target.put_pixel(x, height - 1, *source.get_pixel(x, height - 1));
            }
        }

        // 复制左右边界
        for y in 0..height {
            target.put_pixel(0, y, *source.get_pixel(0, y));
            if width > 1 {
                target.put_pixel(width - 1, y, *source.get_pixel(width - 1, y));
            }
        }
    }

    /// 检查像素是否为前景像素
    fn is_foreground_pixel(&self, image: &ImageBuffer<image::Luma<u8>, Vec<u8>>, x: u32, y: u32) -> bool {
        image.get_pixel(x, y)[0] > 0
    }

    /// 检测图像的背景色
    fn detect_background_color(&self, image: &RgbaImage) -> Rgba<u8> {
        let (width, height) = image.dimensions();
        let mut color_counts: std::collections::HashMap<[u8; 4], u32> = std::collections::HashMap::new();

        // 采样边缘像素来检测背景色
        let sample_points = [
            // 四个角落
            (0, 0), (width - 1, 0), (0, height - 1), (width - 1, height - 1),
            // 边缘中点
            (width / 2, 0), (width / 2, height - 1),
            (0, height / 2), (width - 1, height / 2),
        ];

        for &(x, y) in &sample_points {
            if x < width && y < height {
                let pixel = *image.get_pixel(x, y);
                let key = [pixel[0], pixel[1], pixel[2], pixel[3]];
                *color_counts.entry(key).or_insert(0) += 1;
            }
        }

        // 找到最常见的颜色作为背景色
        let most_common = color_counts.iter()
            .max_by_key(|(_, &count)| count)
            .map(|(color, _)| Rgba([color[0], color[1], color[2], color[3]]))
            .unwrap_or(Rgba([0, 0, 0, 0])); // 默认透明背景

        most_common
    }

    /// 洪水填充算法检测连通组件
    fn flood_fill_component(
        &self,
        image: &ImageBuffer<image::Luma<u8>, Vec<u8>>,
        visited: &mut Vec<Vec<bool>>,
        start_x: u32,
        start_y: u32,
        width: u32,
        height: u32,
    ) -> Option<DetectedUIElement> {
        let mut stack = vec![(start_x, start_y)];
        let mut pixels = Vec::new();
        let mut min_x = start_x;
        let mut max_x = start_x;
        let mut min_y = start_y;
        let mut max_y = start_y;

        while let Some((x, y)) = stack.pop() {
            // 检查边界
            if x >= width || y >= height {
                continue;
            }

            // 检查是否已访问
            if visited[y as usize][x as usize] {
                continue;
            }

            // 检查是否为前景像素
            if !self.is_foreground_pixel(image, x, y) {
                continue;
            }

            // 标记为已访问
            visited[y as usize][x as usize] = true;
            pixels.push((x, y));

            // 更新边界框
            min_x = min_x.min(x);
            max_x = max_x.max(x);
            min_y = min_y.min(y);
            max_y = max_y.max(y);

            // 使用4连通邻居，避免对角线连接导致的误合并
            let neighbors = [
                (x as i32 - 1, y as i32),     // 左
                (x as i32 + 1, y as i32),     // 右
                (x as i32, y as i32 - 1),     // 上
                (x as i32, y as i32 + 1),     // 下
            ];

            for (nx, ny) in neighbors {
                // 检查边界：确保坐标在有效范围内
                if nx >= 0 && ny >= 0 && nx < width as i32 && ny < height as i32 {
                    let nx = nx as u32;
                    let ny = ny as u32;
                    if !visited[ny as usize][nx as usize] {
                        stack.push((nx, ny));
                    }
                }
            }
        }

        // 改进的最小尺寸过滤：提高阈值减少噪点
        let min_pixels = 16; // 提高到16像素，减少噪点干扰
        if pixels.len() < min_pixels {
            println!("🚫 组件太小被过滤: 像素数={}, 最小要求={}", pixels.len(), min_pixels);
            return None;
        }

        // 创建边界框（使用实际像素坐标确保准确性）
        let actual_min_x = pixels.iter().map(|(x, _)| *x).min().unwrap_or(min_x);
        let actual_max_x = pixels.iter().map(|(x, _)| *x).max().unwrap_or(max_x);
        let actual_min_y = pixels.iter().map(|(_, y)| *y).min().unwrap_or(min_y);
        let actual_max_y = pixels.iter().map(|(_, y)| *y).max().unwrap_or(max_y);

        // 适度边界框扩展以包含完整的边缘像素
        let expand_pixels = 2; // 向外扩展2像素，平衡边缘完整性和精确度
        let expanded_min_x = actual_min_x.saturating_sub(expand_pixels);
        let expanded_max_x = (actual_max_x + expand_pixels).min(width - 1);
        let expanded_min_y = actual_min_y.saturating_sub(expand_pixels);
        let expanded_max_y = (actual_max_y + expand_pixels).min(height - 1);

        let bounds = BoundingBox {
            x: expanded_min_x,
            y: expanded_min_y,
            width: expanded_max_x - expanded_min_x + 1,
            height: expanded_max_y - expanded_min_y + 1,
        };

        // 改进的形状分类：考虑实际像素分布
        let element_type = self.classify_element_by_advanced_shape(&bounds, &pixels);
        let confidence = self.calculate_advanced_confidence(&bounds, &pixels);

        // 调试：显示实际像素坐标范围
        let actual_min_x = pixels.iter().map(|(x, _)| *x).min().unwrap_or(0);
        let actual_max_x = pixels.iter().map(|(x, _)| *x).max().unwrap_or(0);
        let actual_min_y = pixels.iter().map(|(_, y)| *y).min().unwrap_or(0);
        let actual_max_y = pixels.iter().map(|(_, y)| *y).max().unwrap_or(0);

        println!("🌊 洪水填充发现组件: {}x{} at ({}, {}), 像素数: {}, 类型: {:?}",
                bounds.width, bounds.height, bounds.x, bounds.y, pixels.len(), element_type);
        println!("🔍 边界框调试: 计算边界=({},{},{},{}), 实际像素范围=({},{},{},{})",
                bounds.x, bounds.y, bounds.x + bounds.width - 1, bounds.y + bounds.height - 1,
                actual_min_x, actual_min_y, actual_max_x, actual_max_y);

        // 检查边界框是否与实际像素匹配
        if bounds.x != actual_min_x || bounds.y != actual_min_y {
            println!("⚠️  边界框不匹配！计算的起点=({},{}), 实际最小坐标=({},{})",
                    bounds.x, bounds.y, actual_min_x, actual_min_y);
        }

        Some(DetectedUIElement {
            id: uuid::Uuid::new_v4().to_string(),
            bounds,
            confidence,
            element_type,
            properties: ElementProperties {
                has_border: false,
                has_shadow: false,
                dominant_colors: vec![],
                pixel_count: pixels.len() as u32,
            },
        })
    }

    /// 改进的形状分类算法：基于实际像素分布和形状特征
    fn classify_element_by_advanced_shape(&self, bounds: &BoundingBox, pixels: &[(u32, u32)]) -> UIElementType {
        let pixel_count = pixels.len();
        let rect_area = (bounds.width * bounds.height) as usize;

        // 防止除零错误
        let fill_ratio = if rect_area > 0 {
            pixel_count as f32 / rect_area as f32
        } else {
            0.0
        };

        let aspect_ratio = if bounds.height > 0 {
            bounds.width as f32 / bounds.height as f32
        } else {
            1.0
        };

        // 计算形状特征
        let compactness = self.calculate_compactness(pixels, bounds);
        let is_rectangular = self.is_rectangular_shape(pixels, bounds);
        let is_circular = self.is_circular_shape(pixels, bounds);

        println!("🔍 形状分析: 填充率={:.2}, 长宽比={:.2}, 紧凑度={:.2}, 矩形={}, 圆形={}",
                fill_ratio, aspect_ratio, compactness, is_rectangular, is_circular);

        // 基于多个特征进行分类
        match (fill_ratio, aspect_ratio, is_rectangular, is_circular) {
            // 高填充率 + 极端长宽比 = 按钮
            (f, a, true, _) if f > 0.8 && (a > 2.5 || a < 0.4) => UIElementType::Button,

            // 高填充率 + 接近正方形 + 矩形 = 图标
            (f, a, true, _) if f > 0.7 && a >= 0.5 && a <= 2.0 => UIElementType::Icon,

            // 高紧凑度 + 圆形特征 = 圆形图标
            (_, _, _, true) if compactness > 0.7 => UIElementType::Icon,

            // 低填充率但有一定尺寸 = 复杂形状
            (f, _, _, _) if f < 0.6 && pixel_count > 100 => UIElementType::Icon, // 暂时归类为图标

            // 线性元素：极端长宽比 + 低填充率
            (_, a, _, _) if a > 5.0 || a < 0.2 => UIElementType::Button, // 可能是分割线等

            // 默认分类
            _ => UIElementType::Icon,
        }
    }

    /// 计算形状紧凑度（接近圆形的程度）
    fn calculate_compactness(&self, pixels: &[(u32, u32)], bounds: &BoundingBox) -> f32 {
        let area = pixels.len() as f32;
        let perimeter = self.estimate_perimeter(pixels, bounds);

        if perimeter <= 0.0 {
            return 0.0;
        }

        // 圆形的紧凑度为1.0，其他形状小于1.0
        4.0 * std::f32::consts::PI * area / (perimeter * perimeter)
    }

    /// 估算周长
    fn estimate_perimeter(&self, _pixels: &[(u32, u32)], bounds: &BoundingBox) -> f32 {
        // 简化算法：边界框周长作为近似
        2.0 * (bounds.width + bounds.height) as f32
    }

    /// 判断是否为矩形形状
    fn is_rectangular_shape(&self, pixels: &[(u32, u32)], bounds: &BoundingBox) -> bool {
        let pixel_count = pixels.len();
        let rect_area = (bounds.width * bounds.height) as usize;

        // 防止除零错误
        if rect_area == 0 {
            return false;
        }

        let fill_ratio = pixel_count as f32 / rect_area as f32;

        // 填充率高于85%认为是矩形
        fill_ratio > 0.85
    }

    /// 判断是否为圆形形状
    fn is_circular_shape(&self, pixels: &[(u32, u32)], bounds: &BoundingBox) -> bool {
        // 防止除零错误：检查像素数量和边界尺寸
        if pixels.is_empty() || bounds.width == 0 || bounds.height == 0 {
            return false;
        }

        let center_x = bounds.x as f32 + bounds.width as f32 / 2.0;
        let center_y = bounds.y as f32 + bounds.height as f32 / 2.0;
        let expected_radius = (bounds.width.min(bounds.height) as f32) / 2.0;

        // 防止除零错误：检查半径
        if expected_radius <= 0.0 {
            return false;
        }

        // 计算像素到中心的平均距离
        let mut total_distance = 0.0;
        for &(x, y) in pixels {
            let dx = x as f32 - center_x;
            let dy = y as f32 - center_y;
            let distance = (dx * dx + dy * dy).sqrt();
            total_distance += distance;
        }

        let avg_distance = total_distance / pixels.len() as f32;
        let distance_ratio = avg_distance / expected_radius;

        // 平均距离接近预期半径，且长宽比接近1:1
        let aspect_ratio = bounds.width as f32 / bounds.height as f32;
        distance_ratio > 0.7 && distance_ratio < 1.3 && aspect_ratio > 0.8 && aspect_ratio < 1.25
    }

    /// 改进的置信度计算
    fn calculate_advanced_confidence(&self, bounds: &BoundingBox, pixels: &[(u32, u32)]) -> f32 {
        let pixel_count = pixels.len();
        let rect_area = (bounds.width * bounds.height) as usize;

        // 防止除零错误
        let fill_ratio = if rect_area > 0 {
            pixel_count as f32 / rect_area as f32
        } else {
            0.0
        };

        // 基础置信度：基于尺寸和填充率
        let size_score = ((bounds.width * bounds.height) as f32 / 400.0).min(1.0);
        let fill_score = fill_ratio.min(1.0);
        let pixel_score = (pixel_count as f32 / 100.0).min(1.0);

        // 综合评分
        (size_score * 0.3 + fill_score * 0.4 + pixel_score * 0.3).max(0.1).min(1.0)
    }

    /// 保存调试可视化图片
    fn save_debug_visualization(&self, image: &DynamicImage, elements: &[DetectedUIElement]) {
        println!("🎨 开始生成调试可视化图片...");

        // 创建可视化图片
        let mut visualization = image.to_rgba8();

        // 定义颜色数组
        let colors = [
            image::Rgba([255, 0, 0, 255]),   // 红色
            image::Rgba([0, 255, 0, 255]),   // 绿色
            image::Rgba([0, 0, 255, 255]),   // 蓝色
            image::Rgba([255, 255, 0, 255]), // 黄色
            image::Rgba([255, 0, 255, 255]), // 紫色
            image::Rgba([0, 255, 255, 255]), // 青色
        ];

        // 为每个检测到的元素绘制边框
        for (index, element) in elements.iter().enumerate() {
            let bounds = &element.bounds;
            let color = colors[index % colors.len()];

            // 绘制2像素宽的边框
            self.draw_rectangle_border(&mut visualization, bounds.x, bounds.y, bounds.width, bounds.height, color, 2);

            println!("🎨 绘制元素 {}: {}x{} at ({}, {}), 类型: {:?}",
                    index, bounds.width, bounds.height, bounds.x, bounds.y, element.element_type);
        }

        // 调试可视化已禁用，避免触发热重载
        println!("✅ 调试可视化完成，已禁用图片保存功能");
    }

    /// 绘制矩形边框
    fn draw_rectangle_border(
        &self,
        image: &mut image::RgbaImage,
        x: u32,
        y: u32,
        width: u32,
        height: u32,
        color: image::Rgba<u8>,
        border_width: u32
    ) {
        let img_width = image.width();
        let img_height = image.height();

        // 绘制上边框
        for bw in 0..border_width {
            if y + bw < img_height {
                for px in x..std::cmp::min(x + width, img_width) {
                    image.put_pixel(px, y + bw, color);
                }
            }
        }

        // 绘制下边框
        for bw in 0..border_width {
            if y + height > bw && y + height - 1 - bw < img_height {
                for px in x..std::cmp::min(x + width, img_width) {
                    image.put_pixel(px, y + height - 1 - bw, color);
                }
            }
        }

        // 绘制左边框
        for bw in 0..border_width {
            if x + bw < img_width {
                for py in y..std::cmp::min(y + height, img_height) {
                    image.put_pixel(x + bw, py, color);
                }
            }
        }

        // 绘制右边框
        for bw in 0..border_width {
            if x + width > bw && x + width - 1 - bw < img_width {
                for py in y..std::cmp::min(y + height, img_height) {
                    image.put_pixel(x + width - 1 - bw, py, color);
                }
            }
        }
    }

    /// 检查像素是否与背景色相似
    fn is_similar_to_background(&self, pixel: &Rgba<u8>, background: &Rgba<u8>) -> bool {
        // 如果背景是透明的，只检查当前像素的透明度
        if background[3] <= self.options.alpha_tolerance {
            return pixel[3] <= self.options.alpha_tolerance;
        }

        // 计算颜色距离
        let r_diff = (pixel[0] as i32 - background[0] as i32).abs();
        let g_diff = (pixel[1] as i32 - background[1] as i32).abs();
        let b_diff = (pixel[2] as i32 - background[2] as i32).abs();
        let a_diff = (pixel[3] as i32 - background[3] as i32).abs();

        let color_distance = ((r_diff * r_diff + g_diff * g_diff + b_diff * b_diff) as f32).sqrt();

        // 如果颜色距离小于阈值，认为是背景
        color_distance <= (self.options.color_tolerance as f32) && a_diff <= (self.options.alpha_tolerance as i32)
    }

    // 删除了 extract_row_segments 函数，不再需要逐行扫描

    // 删除了 connect_segments_to_objects 函数，改用洪水填充

    // 删除了所有与旧逐行扫描算法相关的函数

    // 删除了 create_ui_element_from_object 函数，现在在洪水填充中直接创建

    /// 创建Alpha通道掩码
    fn create_alpha_mask(&self, image: &RgbaImage) -> ImageBuffer<image::Luma<u8>, Vec<u8>> {
        let (width, height) = image.dimensions();
        let mut mask = ImageBuffer::new(width, height);
        
        for (x, y, pixel) in image.enumerate_pixels() {
            let alpha = pixel[3];
            // 如果Alpha值大于容差，认为是非透明像素
            let mask_value = if alpha > self.options.alpha_tolerance { 255 } else { 0 };
            mask.put_pixel(x, y, image::Luma([mask_value]));
        }
        
        mask
    }

    /// 预处理掩码以改善分离效果
    fn preprocess_mask(&self, mask: &ImageBuffer<image::Luma<u8>, Vec<u8>>) -> ImageBuffer<image::Luma<u8>, Vec<u8>> {
        // 简单的噪点去除和边缘平滑
        let (width, height) = mask.dimensions();
        let mut processed = ImageBuffer::new(width, height);

        for y in 1..(height - 1) {
            for x in 1..(width - 1) {
                let center = mask.get_pixel(x, y)[0];

                // 计算3x3邻域的平均值
                let mut sum = 0u32;
                let mut count = 0u32;

                for dy in -1i32..=1 {
                    for dx in -1i32..=1 {
                        let nx = (x as i32 + dx) as u32;
                        let ny = (y as i32 + dy) as u32;
                        sum += mask.get_pixel(nx, ny)[0] as u32;
                        count += 1;
                    }
                }

                let avg = sum / count;

                // 如果中心像素与邻域平均值差异很大，可能是噪点
                let processed_value = if (center as i32 - avg as i32).abs() > 100 {
                    avg as u8
                } else {
                    center
                };

                processed.put_pixel(x, y, image::Luma([processed_value]));
            }
        }

        // 复制边界像素
        for x in 0..width {
            processed.put_pixel(x, 0, *mask.get_pixel(x, 0));
            processed.put_pixel(x, height - 1, *mask.get_pixel(x, height - 1));
        }
        for y in 0..height {
            processed.put_pixel(0, y, *mask.get_pixel(0, y));
            processed.put_pixel(width - 1, y, *mask.get_pixel(width - 1, y));
        }

        processed
    }

    /// 判断像素是否是UI元素的起始点（左上角边界）
    fn is_ui_start_pixel(&self, image: &RgbaImage, x: u32, y: u32) -> bool {
        let pixel = image.get_pixel(x, y);

        // 当前像素必须是非透明的
        if pixel[3] <= self.options.alpha_tolerance {
            return false;
        }

        // 检查左边和上边是否是透明的（边界条件）
        let left_transparent = x == 0 || image.get_pixel(x - 1, y)[3] <= self.options.alpha_tolerance;
        let top_transparent = y == 0 || image.get_pixel(x, y - 1)[3] <= self.options.alpha_tolerance;

        // 如果左边或上边是透明的，这可能是UI元素的起始点
        left_transparent || top_transparent
    }

    /// 从起始点提取完整的UI矩形
    fn extract_ui_rectangle(
        &self,
        image: &RgbaImage,
        visited: &mut Vec<Vec<bool>>,
        start_x: u32,
        start_y: u32,
        img_width: u32,
        img_height: u32,
    ) -> Option<DetectedUIElement> {
        // 向右扫描，找到UI元素的右边界
        let mut right_x = start_x;
        while right_x < img_width && self.is_ui_content_pixel(image, right_x, start_y) {
            right_x += 1;
        }
        right_x = right_x.saturating_sub(1);

        // 向下扫描，找到UI元素的下边界
        let mut bottom_y = start_y;
        'outer: while bottom_y < img_height {
            // 检查这一行是否完全属于UI元素
            for x in start_x..=right_x {
                if !self.is_ui_content_pixel(image, x, bottom_y) {
                    break 'outer;
                }
            }
            bottom_y += 1;
        }
        bottom_y = bottom_y.saturating_sub(1);

        let width = right_x - start_x + 1;
        let height = bottom_y - start_y + 1;

        // 标记这个矩形区域为已访问
        for y in start_y..=bottom_y {
            for x in start_x..=right_x {
                if y < img_height && x < img_width {
                    visited[y as usize][x as usize] = true;
                }
            }
        }

        // 过滤太小的矩形
        if width < self.options.min_width || height < self.options.min_height {
            return None;
        }

        // 计算像素数量（用于置信度计算）
        let mut pixel_count = 0;
        for y in start_y..=bottom_y {
            for x in start_x..=right_x {
                if self.is_ui_content_pixel(image, x, y) {
                    pixel_count += 1;
                }
            }
        }

        let bounds = BoundingBox {
            x: start_x,
            y: start_y,
            width,
            height,
        };

        let element_type = self.classify_element_by_shape(&bounds, pixel_count);
        let confidence = self.calculate_element_confidence(&bounds, pixel_count);
        let has_border = self.detect_border(image, &bounds);

        Some(DetectedUIElement {
            id: uuid::Uuid::new_v4().to_string(),
            bounds,
            confidence,
            element_type,
            properties: ElementProperties {
                has_border,
                has_shadow: false,
                dominant_colors: vec![],
                pixel_count: pixel_count as u32,
            },
        })
    }

    /// 判断像素是否属于UI内容（包括内部纹理）
    fn is_ui_content_pixel(&self, image: &RgbaImage, x: u32, y: u32) -> bool {
        let (width, height) = image.dimensions();
        if x >= width || y >= height {
            return false;
        }

        let pixel = image.get_pixel(x, y);
        pixel[3] > self.options.alpha_tolerance
    }

    /// 判断是否是背景像素（颜色单一的大面积区域）
    fn is_background_pixel(&self, image: &RgbaImage, x: u32, y: u32) -> bool {
        let (width, height) = image.dimensions();
        let center_pixel = image.get_pixel(x, y);

        // 检查5x5邻域的颜色一致性
        let mut similar_count = 0;
        let mut total_count = 0;

        for dy in -2i32..=2 {
            for dx in -2i32..=2 {
                let nx = x as i32 + dx;
                let ny = y as i32 + dy;

                if nx >= 0 && ny >= 0 && nx < width as i32 && ny < height as i32 {
                    let neighbor = image.get_pixel(nx as u32, ny as u32);
                    total_count += 1;

                    // 检查颜色相似性
                    if self.colors_similar(center_pixel, neighbor) {
                        similar_count += 1;
                    }
                }
            }
        }

        // 如果80%以上的邻域像素颜色相似，认为是背景
        if total_count > 0 {
            similar_count as f32 / total_count as f32 > 0.8
        } else {
            false
        }
    }

    /// 判断两个颜色是否相似
    fn colors_similar(&self, color1: &Rgba<u8>, color2: &Rgba<u8>) -> bool {
        let r_diff = (color1[0] as i32 - color2[0] as i32).abs();
        let g_diff = (color1[1] as i32 - color2[1] as i32).abs();
        let b_diff = (color1[2] as i32 - color2[2] as i32).abs();
        let a_diff = (color1[3] as i32 - color2[3] as i32).abs();

        r_diff <= self.options.color_tolerance as i32 &&
        g_diff <= self.options.color_tolerance as i32 &&
        b_diff <= self.options.color_tolerance as i32 &&
        a_diff <= self.options.alpha_tolerance as i32
    }

    /// 使用洪水填充算法提取UI元素
    fn extract_ui_element(
        &self,
        image: &RgbaImage,
        visited: &mut Vec<Vec<bool>>,
        start_x: u32,
        start_y: u32,
        width: u32,
        height: u32,
    ) -> Option<DetectedUIElement> {
        let mut stack = vec![(start_x, start_y)];
        let mut pixels = Vec::new();
        let mut min_x = start_x;
        let mut max_x = start_x;
        let mut min_y = start_y;
        let mut max_y = start_y;

        let start_color = *image.get_pixel(start_x, start_y);

        while let Some((x, y)) = stack.pop() {
            if visited[y as usize][x as usize] {
                continue;
            }

            let current_pixel = *image.get_pixel(x, y);

            // 检查像素是否属于同一UI元素
            if !self.is_same_ui_element(&start_color, &current_pixel) {
                continue;
            }

            visited[y as usize][x as usize] = true;
            pixels.push((x, y));

            // 更新边界框
            min_x = min_x.min(x);
            max_x = max_x.max(x);
            min_y = min_y.min(y);
            max_y = max_y.max(y);

            // 添加4连通邻居到栈中
            for (dx, dy) in [(-1, 0), (1, 0), (0, -1), (0, 1)] {
                let nx = x as i32 + dx;
                let ny = y as i32 + dy;

                if nx >= 0 && ny >= 0 && nx < width as i32 && ny < height as i32 {
                    let nx = nx as u32;
                    let ny = ny as u32;

                    if !visited[ny as usize][nx as usize] && self.is_ui_content_pixel(image, nx, ny) {
                        stack.push((nx, ny));
                    }
                }
            }
        }

        // 如果像素数量太少，忽略这个元素 - 使用更宽松的条件
        if pixels.len() < 16 { // 至少4x4像素
            return None;
        }

        // 创建UI元素
        let bounds = BoundingBox {
            x: min_x,
            y: min_y,
            width: max_x - min_x + 1,
            height: max_y - min_y + 1,
        };

        let element_type = self.classify_element_by_shape(&bounds, pixels.len());
        let confidence = self.calculate_element_confidence(&bounds, pixels.len());
        let has_border = self.detect_border(image, &bounds);

        Some(DetectedUIElement {
            id: uuid::Uuid::new_v4().to_string(),
            bounds,
            confidence,
            element_type,
            properties: ElementProperties {
                has_border,
                has_shadow: false, // TODO: 实现阴影检测
                dominant_colors: vec![], // TODO: 实现主色调提取
                pixel_count: pixels.len() as u32,
            },
        })
    }

    /// 判断两个像素是否属于同一UI元素
    fn is_same_ui_element(&self, color1: &Rgba<u8>, color2: &Rgba<u8>) -> bool {
        // 使用更宽松的颜色匹配，适应渐变和抗锯齿
        let tolerance = self.options.color_tolerance;

        let r_diff = (color1[0] as i32 - color2[0] as i32).abs();
        let g_diff = (color1[1] as i32 - color2[1] as i32).abs();
        let b_diff = (color1[2] as i32 - color2[2] as i32).abs();
        let a_diff = (color1[3] as i32 - color2[3] as i32).abs();

        // 使用欧几里得距离进行颜色匹配
        let color_distance = ((r_diff * r_diff + g_diff * g_diff + b_diff * b_diff) as f32).sqrt();

        color_distance <= tolerance as f32 && a_diff <= self.options.alpha_tolerance as i32
    }

    /// 根据形状分类UI元素
    fn classify_element_by_shape(&self, bounds: &BoundingBox, pixel_count: usize) -> UIElementType {
        let area = bounds.width * bounds.height;
        let aspect_ratio = if bounds.height > 0 {
            bounds.width as f32 / bounds.height as f32
        } else {
            1.0
        };
        let _fill_ratio = if area > 0 {
            pixel_count as f32 / area as f32
        } else {
            0.0
        };

        // 更细致的分类，适应小UI元素
        if area < 400 { // 20x20以下，小图标
            UIElementType::Icon
        } else if area < 2500 { // 50x50以下，中等图标或小按钮
            if aspect_ratio > 2.0 || aspect_ratio < 0.5 {
                UIElementType::Text // 长条形是文本
            } else {
                UIElementType::Icon // 方形是图标
            }
        } else if area < 10000 { // 100x100以下，按钮
            UIElementType::Button
        } else if aspect_ratio > 4.0 || aspect_ratio < 0.25 {
            UIElementType::Text // 极长条形是文本
        } else {
            UIElementType::Panel // 大面积是面板
        }
    }

    /// 计算元素置信度
    fn calculate_element_confidence(&self, bounds: &BoundingBox, pixel_count: usize) -> f32 {
        let area = bounds.width * bounds.height;

        // 防止除零错误
        let fill_ratio = if area > 0 {
            pixel_count as f32 / area as f32
        } else {
            0.0
        };

        let aspect_ratio = if bounds.height > 0 {
            bounds.width as f32 / bounds.height as f32
        } else {
            1.0
        };

        let mut confidence = fill_ratio; // 基础置信度基于填充率

        // 调整置信度基于长宽比
        if aspect_ratio > 0.5 && aspect_ratio < 2.0 {
            confidence += 0.2; // 接近正方形的元素置信度更高
        }

        // 调整置信度基于大小
        if area > 400 && area < 100000 {
            confidence += 0.1; // 合理大小的元素置信度更高
        }

        confidence.min(1.0)
    }

    /// 检测边框
    fn detect_border(&self, image: &RgbaImage, bounds: &BoundingBox) -> bool {
        let mut border_pixels = 0;
        let mut total_border_pixels = 0;

        // 检查边界像素
        for x in bounds.x..(bounds.x + bounds.width) {
            for y in [bounds.y, bounds.y + bounds.height - 1] {
                if y < image.height() {
                    total_border_pixels += 1;
                    if self.is_border_pixel(image, x, y) {
                        border_pixels += 1;
                    }
                }
            }
        }

        for y in bounds.y..(bounds.y + bounds.height) {
            for x in [bounds.x, bounds.x + bounds.width - 1] {
                if x < image.width() {
                    total_border_pixels += 1;
                    if self.is_border_pixel(image, x, y) {
                        border_pixels += 1;
                    }
                }
            }
        }

        // 如果50%以上的边界像素是边框像素，认为有边框
        if total_border_pixels > 0 {
            border_pixels as f32 / total_border_pixels as f32 > 0.5
        } else {
            false
        }
    }

    /// 判断是否是边框像素
    fn is_border_pixel(&self, image: &RgbaImage, x: u32, y: u32) -> bool {
        let pixel = image.get_pixel(x, y);

        // 检查是否是深色像素（通常边框是深色的）
        let brightness = (pixel[0] as u32 + pixel[1] as u32 + pixel[2] as u32) / 3;
        brightness < 100
    }

    /// 后处理元素列表
    fn post_process_elements(&self, elements: Vec<DetectedUIElement>) -> Vec<DetectedUIElement> {
        let mut processed = Vec::new();

        for element in elements {
            // 检查是否需要分割大元素
            if self.should_split_element(&element) {
                let split_elements = self.split_large_element(&element);
                processed.extend(split_elements);
            } else {
                processed.push(element);
            }
        }

        // 移除重叠度过高的元素
        self.remove_overlapping_elements(processed)
    }

    /// 判断是否需要分割元素
    fn should_split_element(&self, element: &DetectedUIElement) -> bool {
        let area = element.bounds.width * element.bounds.height;
        let aspect_ratio = element.bounds.width as f32 / element.bounds.height as f32;

        // 分割条件：面积过大或长宽比极端
        area > 100000 || aspect_ratio > 5.0 || aspect_ratio < 0.2
    }

    /// 分割大元素
    fn split_large_element(&self, element: &DetectedUIElement) -> Vec<DetectedUIElement> {
        let bounds = &element.bounds;
        let mut split_elements = Vec::new();

        let aspect_ratio = if bounds.height > 0 {
            bounds.width as f32 / bounds.height as f32
        } else {
            1.0
        };

        if aspect_ratio > 3.0 {
            // 水平分割
            let split_count = ((aspect_ratio / 1.5) as u32).max(1);
            let sub_width = bounds.width / split_count;

            for i in 0..split_count {
                let sub_element = DetectedUIElement {
                    id: uuid::Uuid::new_v4().to_string(),
                    bounds: BoundingBox {
                        x: bounds.x + i * sub_width,
                        y: bounds.y,
                        width: if i == split_count - 1 { bounds.width - i * sub_width } else { sub_width },
                        height: bounds.height,
                    },
                    confidence: element.confidence * 0.8, // 降低置信度
                    element_type: element.element_type.clone(),
                    properties: element.properties.clone(),
                };
                split_elements.push(sub_element);
            }
        } else if aspect_ratio < 0.33 {
            // 垂直分割
            let split_count = ((1.0 / aspect_ratio / 1.5) as u32).max(1);
            let sub_height = bounds.height / split_count;

            for i in 0..split_count {
                let sub_element = DetectedUIElement {
                    id: uuid::Uuid::new_v4().to_string(),
                    bounds: BoundingBox {
                        x: bounds.x,
                        y: bounds.y + i * sub_height,
                        width: bounds.width,
                        height: if i == split_count - 1 { bounds.height - i * sub_height } else { sub_height },
                    },
                    confidence: element.confidence * 0.8,
                    element_type: element.element_type.clone(),
                    properties: element.properties.clone(),
                };
                split_elements.push(sub_element);
            }
        } else {
            // 网格分割
            let grid_size = 2; // 2x2分割
            let sub_width = bounds.width / grid_size;
            let sub_height = bounds.height / grid_size;

            for row in 0..grid_size {
                for col in 0..grid_size {
                    let sub_element = DetectedUIElement {
                        id: uuid::Uuid::new_v4().to_string(),
                        bounds: BoundingBox {
                            x: bounds.x + col * sub_width,
                            y: bounds.y + row * sub_height,
                            width: if col == grid_size - 1 { bounds.width - col * sub_width } else { sub_width },
                            height: if row == grid_size - 1 { bounds.height - row * sub_height } else { sub_height },
                        },
                        confidence: element.confidence * 0.7,
                        element_type: element.element_type.clone(),
                        properties: element.properties.clone(),
                    };
                    split_elements.push(sub_element);
                }
            }
        }

        split_elements
    }

    /// 移除重叠的元素
    fn remove_overlapping_elements(&self, elements: Vec<DetectedUIElement>) -> Vec<DetectedUIElement> {
        let mut filtered: Vec<DetectedUIElement> = Vec::new();

        for element in elements {
            let mut should_keep = true;

            // 检查与已有元素的重叠
            for existing in &filtered {
                if self.calculate_overlap_ratio(&element.bounds, &existing.bounds) > 0.7 {
                    // 如果重叠度超过70%，保留置信度更高的
                    if element.confidence <= existing.confidence {
                        should_keep = false;
                        break;
                    } else {
                        // 移除置信度较低的现有元素
                        // 这里简化处理，实际应该从filtered中移除
                    }
                }
            }

            if should_keep {
                filtered.push(element);
            }
        }

        filtered
    }

    /// 计算两个边界框的重叠比例
    fn calculate_overlap_ratio(&self, bounds1: &BoundingBox, bounds2: &BoundingBox) -> f32 {
        let x1 = bounds1.x.max(bounds2.x);
        let y1 = bounds1.y.max(bounds2.y);
        let x2 = (bounds1.x + bounds1.width).min(bounds2.x + bounds2.width);
        let y2 = (bounds1.y + bounds1.height).min(bounds2.y + bounds2.height);

        if x2 <= x1 || y2 <= y1 {
            return 0.0; // 没有重叠
        }

        let overlap_area = (x2 - x1) * (y2 - y1);
        let area1 = bounds1.width * bounds1.height;
        let area2 = bounds2.width * bounds2.height;
        let union_area = area1 + area2 - overlap_area;

        if union_area > 0 {
            overlap_area as f32 / union_area as f32
        } else {
            0.0
        }
    }

    /// 分离粘连的组件
    fn separate_connected_components(&self, components: Vec<ComponentInfo>, image: &RgbaImage) -> Vec<ComponentInfo> {
        let mut separated = Vec::new();

        for component in components {
            // 检查组件是否可能是多个UI元素粘连
            if self.is_likely_merged_component(&component) {
                // 尝试分离
                let sub_components = self.split_component(&component, image);
                separated.extend(sub_components);
            } else {
                separated.push(component);
            }
        }

        separated
    }

    /// 判断组件是否可能是多个UI元素粘连
    fn is_likely_merged_component(&self, component: &ComponentInfo) -> bool {
        let area = component.bounds.width * component.bounds.height;
        let aspect_ratio = component.bounds.width as f32 / component.bounds.height as f32;

        // 如果组件很大且长宽比极端，可能是多个元素粘连
        area > 50000 || aspect_ratio > 5.0 || aspect_ratio < 0.2
    }

    /// 分割组件
    fn split_component(&self, component: &ComponentInfo, _image: &RgbaImage) -> Vec<ComponentInfo> {
        let bounds = &component.bounds;
        let mut sub_components = Vec::new();

        // 简单的分割策略：基于长宽比决定分割方向
        let aspect_ratio = bounds.width as f32 / bounds.height as f32;

        if aspect_ratio > 3.0 {
            // 水平分割
            let split_count = ((aspect_ratio / 1.5) as u32).max(1);
            let sub_width = bounds.width / split_count;

            for i in 0..split_count {
                let sub_component = ComponentInfo {
                    label: component.label + i * 1000, // 避免标签冲突
                    bounds: BoundingBox {
                        x: bounds.x + i * sub_width,
                        y: bounds.y,
                        width: if i == split_count - 1 { bounds.width - i * sub_width } else { sub_width },
                        height: bounds.height,
                    },
                    pixel_count: component.pixel_count / split_count,
                    colors: component.colors.clone(),
                };
                sub_components.push(sub_component);
            }
        } else if aspect_ratio < 0.33 {
            // 垂直分割
            let split_count = ((1.0 / aspect_ratio / 1.5) as u32).max(1);
            let sub_height = bounds.height / split_count;

            for i in 0..split_count {
                let sub_component = ComponentInfo {
                    label: component.label + i * 1000,
                    bounds: BoundingBox {
                        x: bounds.x,
                        y: bounds.y + i * sub_height,
                        width: bounds.width,
                        height: if i == split_count - 1 { bounds.height - i * sub_height } else { sub_height },
                    },
                    pixel_count: component.pixel_count / split_count,
                    colors: component.colors.clone(),
                };
                sub_components.push(sub_component);
            }
        } else {
            // 网格分割（对于大的方形区域）
            let area = bounds.width * bounds.height;
            if area > 100000 {
                let grid_size = (((area as f32).sqrt() / 100.0) as u32).max(1);
                let sub_width = bounds.width / grid_size;
                let sub_height = bounds.height / grid_size;

                for row in 0..grid_size {
                    for col in 0..grid_size {
                        let sub_component = ComponentInfo {
                            label: component.label + row * 1000 + col * 100,
                            bounds: BoundingBox {
                                x: bounds.x + col * sub_width,
                                y: bounds.y + row * sub_height,
                                width: if col == grid_size - 1 { bounds.width - col * sub_width } else { sub_width },
                                height: if row == grid_size - 1 { bounds.height - row * sub_height } else { sub_height },
                            },
                            pixel_count: component.pixel_count / (grid_size * grid_size),
                            colors: component.colors.clone(),
                        };
                        sub_components.push(sub_component);
                    }
                }
            } else {
                // 不分割
                sub_components.push(component.clone());
            }
        }

        sub_components
    }

    /// 提取连通组件信息
    fn extract_components(&self, labeled_image: &ImageBuffer<image::Luma<u32>, Vec<u32>>, original: &RgbaImage) -> Vec<ComponentInfo> {
        let mut components: HashMap<u32, ComponentInfo> = HashMap::new();
        
        for (x, y, pixel) in labeled_image.enumerate_pixels() {
            let label = pixel[0];
            if label == 0 { continue; } // 跳过背景
            
            let component = components.entry(label).or_insert_with(|| ComponentInfo::new(label));
            component.add_pixel(x, y, original.get_pixel(x, y));
        }
        
        components.into_values().collect()
    }

    /// 过滤组件（移除太小的组件）
    fn filter_components(&self, components: Vec<ComponentInfo>) -> Vec<ComponentInfo> {
        components.into_iter()
            .filter(|comp| {
                comp.bounds.width >= self.options.min_width && 
                comp.bounds.height >= self.options.min_height
            })
            .collect()
    }

    /// 合并相近的组件
    fn merge_nearby_components(&self, components: Vec<ComponentInfo>) -> Vec<ComponentInfo> {
        let mut merged = Vec::new();
        let mut used = vec![false; components.len()];
        
        for i in 0..components.len() {
            if used[i] { continue; }
            
            let mut current = components[i].clone();
            used[i] = true;
            
            // 查找可以合并的组件
            for j in (i + 1)..components.len() {
                if used[j] { continue; }
                
                if self.should_merge(&current.bounds, &components[j].bounds) {
                    current = self.merge_components(current, components[j].clone());
                    used[j] = true;
                }
            }
            
            merged.push(current);
        }
        
        merged
    }

    /// 判断两个组件是否应该合并
    fn should_merge(&self, bounds1: &BoundingBox, bounds2: &BoundingBox) -> bool {
        let distance = self.calculate_distance(bounds1, bounds2);

        // 只有距离为0（真正重叠）或距离为1（紧邻）才合并
        distance <= self.options.merge_distance && distance <= 1
    }

    /// 计算两个边界框之间的距离
    fn calculate_distance(&self, bounds1: &BoundingBox, bounds2: &BoundingBox) -> u32 {
        let x1_end = bounds1.x + bounds1.width;
        let y1_end = bounds1.y + bounds1.height;
        let x2_end = bounds2.x + bounds2.width;
        let y2_end = bounds2.y + bounds2.height;
        
        let dx = if bounds1.x > x2_end {
            bounds1.x - x2_end
        } else if bounds2.x > x1_end {
            bounds2.x - x1_end
        } else {
            0
        };
        
        let dy = if bounds1.y > y2_end {
            bounds1.y - y2_end
        } else if bounds2.y > y1_end {
            bounds2.y - y1_end
        } else {
            0
        };
        
        ((dx * dx + dy * dy) as f64).sqrt() as u32
    }

    /// 合并两个组件
    fn merge_components(&self, comp1: ComponentInfo, comp2: ComponentInfo) -> ComponentInfo {
        let min_x = comp1.bounds.x.min(comp2.bounds.x);
        let min_y = comp1.bounds.y.min(comp2.bounds.y);
        let max_x = (comp1.bounds.x + comp1.bounds.width).max(comp2.bounds.x + comp2.bounds.width);
        let max_y = (comp1.bounds.y + comp1.bounds.height).max(comp2.bounds.y + comp2.bounds.height);
        
        ComponentInfo {
            label: comp1.label,
            bounds: BoundingBox {
                x: min_x,
                y: min_y,
                width: max_x - min_x,
                height: max_y - min_y,
            },
            pixel_count: comp1.pixel_count + comp2.pixel_count,
            colors: comp1.colors, // 简化处理，使用第一个组件的颜色
        }
    }

    /// 将组件信息转换为DetectedUIElement
    fn components_to_elements(&self, components: Vec<ComponentInfo>, image: &RgbaImage) -> Vec<DetectedUIElement> {
        components.into_iter().map(|comp| {
            let element_type = self.classify_element(&comp, image);
            let properties = self.analyze_element_properties(&comp, image);
            let confidence = self.calculate_confidence(&comp);
            
            DetectedUIElement {
                id: Uuid::new_v4().to_string(),
                bounds: comp.bounds,
                confidence,
                element_type,
                properties,
            }
        }).collect()
    }

    /// 分类UI元素类型
    fn classify_element(&self, component: &ComponentInfo, _image: &RgbaImage) -> UIElementType {
        let aspect_ratio = component.bounds.width as f32 / component.bounds.height as f32;
        let area = component.bounds.width * component.bounds.height;
        
        // 简单的分类逻辑，可以根据需要扩展
        if area > 10000 {
            UIElementType::Panel
        } else if aspect_ratio > 2.0 || aspect_ratio < 0.5 {
            UIElementType::Text
        } else if area < 1600 {
            UIElementType::Icon
        } else {
            UIElementType::Button
        }
    }

    /// 分析元素属性
    fn analyze_element_properties(&self, component: &ComponentInfo, _image: &RgbaImage) -> ElementProperties {
        ElementProperties {
            has_border: false, // TODO: 实现边框检测
            has_shadow: false, // TODO: 实现阴影检测
            dominant_colors: vec![], // TODO: 实现主色调提取
            pixel_count: component.pixel_count,
        }
    }

    /// 计算置信度
    fn calculate_confidence(&self, component: &ComponentInfo) -> f32 {
        let area = component.bounds.width * component.bounds.height;
        let fill_ratio = if area > 0 {
            component.pixel_count as f32 / area as f32
        } else {
            0.0
        };
        
        // 基于填充率计算置信度
        fill_ratio.min(1.0)
    }
}

/// 组件信息结构
#[derive(Debug, Clone)]
struct ComponentInfo {
    label: u32,
    bounds: BoundingBox,
    pixel_count: u32,
    colors: HashMap<[u8; 4], u32>,
}

impl ComponentInfo {
    fn new(label: u32) -> Self {
        Self {
            label,
            bounds: BoundingBox { x: u32::MAX, y: u32::MAX, width: 0, height: 0 },
            pixel_count: 0,
            colors: HashMap::new(),
        }
    }

    fn add_pixel(&mut self, x: u32, y: u32, color: &Rgba<u8>) {
        // 更新边界框
        if self.bounds.x == u32::MAX {
            self.bounds.x = x;
            self.bounds.y = y;
            self.bounds.width = 1;
            self.bounds.height = 1;
        } else {
            let min_x = self.bounds.x.min(x);
            let min_y = self.bounds.y.min(y);
            let max_x = (self.bounds.x + self.bounds.width).max(x + 1);
            let max_y = (self.bounds.y + self.bounds.height).max(y + 1);
            
            self.bounds.x = min_x;
            self.bounds.y = min_y;
            self.bounds.width = max_x - min_x;
            self.bounds.height = max_y - min_y;
        }
        
        // 统计像素和颜色
        self.pixel_count += 1;
        let color_key = [color[0], color[1], color[2], color[3]];
        *self.colors.entry(color_key).or_insert(0) += 1;
    }
}
