/**
 * Scene_Options 场景数据流定义
 */

import type { SceneDataFlow } from '../types';

/**
 * Scene_Options 场景的数据流
 */
export const SceneOptionsDataFlow: SceneDataFlow = {
  sceneName: "Scene_Options",
  dataResources: [
    {
      dataPath: "ConfigManager.alwaysDash",
      description: "常时冲刺设置"
    },
    {
      dataPath: "ConfigManager.commandRemember",
      description: "记住指令设置"
    },
    {
      dataPath: "ConfigManager.touchUI",
      description: "触摸UI设置"
    },
    {
      dataPath: "ConfigManager.bgmVolume",
      description: "BGM音量"
    },
    {
      dataPath: "ConfigManager.bgsVolume",
      description: "BGS音量"
    },
    {
      dataPath: "ConfigManager.meVolume",
      description: "ME音量"
    },
    {
      dataPath: "ConfigManager.seVolume",
      description: "SE音量"
    },
    {
      dataPath: "Graphics.boxWidth",
      description: "游戏区域宽度"
    },
    {
      dataPath: "Graphics.boxHeight",
      description: "游戏区域高度"
    }
  ],
  buttons: [
    {
      buttonName: "Cancel",
      triggerMethods: [
        "ConfigManager.save()",
        "this.popScene()"
      ]
    }
  ]
};
