<script lang="ts">
  import type { GeneratedCode } from './types';

  let {
    generatedCode
  }: {
    generatedCode: GeneratedCode | null;
  } = $props();

  function copyToClipboard() {
    if (generatedCode?.code) {
      navigator.clipboard.writeText(generatedCode.code).then(() => {
        // 可以添加复制成功的提示
        console.log('代码已复制到剪贴板');
      }).catch(err => {
        console.error('复制失败:', err);
      });
    }
  }

  function formatCode(code: string): string {
    // 简单的代码格式化
    return code
      .replace(/\./g, '.\n  ')
      .replace(/\(/g, '(\n    ')
      .replace(/\)/g, '\n  )')
      .replace(/\{/g, '{\n    ')
      .replace(/\}/g, '\n  }');
  }
</script>

<div class="code-preview">
  {#if generatedCode}
    <div class="preview-header">
      <div class="preview-info">
        <div class="preview-title">{generatedCode.item}</div>
        <div class="preview-category">{generatedCode.category}</div>
      </div>
      <button class="copy-btn" onclick={copyToClipboard} title="复制代码">
        📋
      </button>
    </div>

    <div class="code-block">
      <pre><code>{generatedCode.code}</code></pre>
    </div>

    {#if generatedCode.description}
      <div class="code-description">
        <h6>说明</h6>
        <p>{generatedCode.description}</p>
      </div>
    {/if}

    <div class="usage-tips">
      <h6>使用提示</h6>
      <ul>
        <li>此代码将在事件触发时执行</li>
        <li>确保在正确的游戏状态下调用</li>
        <li>某些方法可能需要错误处理</li>
      </ul>
    </div>
  {:else}
    <div class="empty-preview">
      <div class="empty-icon">📝</div>
      <p>选择项目后将显示生成的代码</p>
    </div>
  {/if}
</div>

<style>
  .code-preview {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 16px;
    border-bottom: 1px solid var(--border-color, #e0e0e0);
    background: var(--header-bg, #f9f9f9);
  }

  .preview-info {
    flex: 1;
  }

  .preview-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary, #333);
    margin-bottom: 2px;
  }

  .preview-category {
    font-size: 12px;
    color: var(--text-muted, #666);
  }

  .copy-btn {
    padding: 6px 8px;
    border: 1px solid var(--border-color, #ddd);
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }

  .copy-btn:hover {
    background: var(--hover-bg, #f0f0f0);
    border-color: var(--primary-color, #2196f3);
  }

  .code-block {
    flex: 1;
    padding: 16px;
    background: var(--code-bg, #f8f9fa);
    border-bottom: 1px solid var(--border-color, #e0e0e0);
    overflow-x: auto;
  }

  .code-block pre {
    margin: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    color: var(--code-color, #333);
    white-space: pre-wrap;
    word-break: break-all;
  }

  .code-block code {
    background: none;
    padding: 0;
    border: none;
    color: inherit;
  }

  .code-description,
  .usage-tips {
    padding: 16px;
    border-bottom: 1px solid var(--border-color, #e0e0e0);
  }

  .code-description:last-child,
  .usage-tips:last-child {
    border-bottom: none;
  }

  .code-description h6,
  .usage-tips h6 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-muted, #666);
    text-transform: uppercase;
  }

  .code-description p {
    margin: 0;
    font-size: 13px;
    color: var(--text-secondary, #555);
    line-height: 1.4;
  }

  .usage-tips ul {
    margin: 0;
    padding-left: 16px;
    font-size: 12px;
    color: var(--text-muted, #666);
  }

  .usage-tips li {
    margin-bottom: 4px;
    line-height: 1.3;
  }

  .empty-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-muted, #666);
    text-align: center;
  }

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-preview p {
    margin: 0;
    font-style: italic;
  }
</style>
