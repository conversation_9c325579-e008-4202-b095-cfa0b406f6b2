// 核心类对象 - 重要的游戏类

import type { RPGObject } from './types';

export const coreClasses: RPGObject[] = [
  {
    name: 'Game_Action',
    description: '行动对象 - 处理技能、物品使用的核心类',
    category: 'game',
    properties: [],
    methods: [
      { name: 'constructor', type: 'method', description: '创建行动对象', code: 'new Game_Action($gameActors.actor(1))' },
      { name: 'setItemObject', type: 'method', description: '设置物品/技能对象', code: 'action.setItemObject($dataItems[1])' },
      { name: 'isForFriend', type: 'method', description: '是否对友方使用', code: 'action.isForFriend()' },
      { name: 'isForAll', type: 'method', description: '是否对全体使用', code: 'action.isForAll()' },
      { name: 'testApply', type: 'method', description: '测试是否可以应用', code: 'action.testApply(target)' },
      { name: 'apply', type: 'method', description: '应用到目标', code: 'action.apply(target)' },
      { name: 'applyGlobal', type: 'method', description: '应用全局效果', code: 'action.applyGlobal()' },
      { name: 'numRepeats', type: 'method', description: '获取重复次数', code: 'action.numRepeats()' },
      { name: 'setAttack', type: 'method', description: '设置为攻击', code: 'action.setAttack()' },
      { name: 'setGuard', type: 'method', description: '设置为防御', code: 'action.setGuard()' },
      { name: 'setSkill', type: 'method', description: '设置技能', code: 'action.setSkill(1)' },
      { name: 'setItem', type: 'method', description: '设置物品', code: 'action.setItem(1)' },
      { name: 'setTarget', type: 'method', description: '设置目标', code: 'action.setTarget(0)' },
      { name: 'needsSelection', type: 'method', description: '是否需要选择目标', code: 'action.needsSelection()' },
      { name: 'isForOpponent', type: 'method', description: '是否对敌方使用', code: 'action.isForOpponent()' }
    ]
  },
  {
    name: 'Game_Actor',
    description: '角色对象 - 通过$gameActors.actor(id)获取',
    category: 'game',
    properties: [
      { name: 'name', type: 'property', description: '角色名称', code: '$gameActors.actor(1).name()' },
      { name: 'level', type: 'property', description: '角色等级', code: '$gameActors.actor(1).level' },
      { name: 'hp', type: 'property', description: '当前HP', code: '$gameActors.actor(1).hp' },
      { name: 'mp', type: 'property', description: '当前MP', code: '$gameActors.actor(1).mp' },
      { name: 'mhp', type: 'property', description: '最大HP', code: '$gameActors.actor(1).mhp' },
      { name: 'mmp', type: 'property', description: '最大MP', code: '$gameActors.actor(1).mmp' },
      { name: 'atk', type: 'property', description: '攻击力', code: '$gameActors.actor(1).atk' },
      { name: 'def', type: 'property', description: '防御力', code: '$gameActors.actor(1).def' },
      { name: 'mat', type: 'property', description: '魔法攻击', code: '$gameActors.actor(1).mat' },
      { name: 'mdf', type: 'property', description: '魔法防御', code: '$gameActors.actor(1).mdf' },
      { name: 'agi', type: 'property', description: '敏捷性', code: '$gameActors.actor(1).agi' },
      { name: 'luk', type: 'property', description: '幸运', code: '$gameActors.actor(1).luk' },
      { name: 'profile', type: 'property', description: '角色简介', code: '$gameActors.actor(1).profile()' }
    ],
    methods: [
      { name: 'canUse', type: 'method', description: '检查是否可以使用物品/技能', code: '$gameActors.actor(1).canUse($dataItems[1])' },
      { name: 'useItem', type: 'method', description: '使用物品', code: '$gameActors.actor(1).useItem($dataItems[1])' },
      { name: 'setLastMenuSkill', type: 'method', description: '设置最后使用的菜单技能', code: '$gameActors.actor(1).setLastMenuSkill($dataSkills[1])' },
      { name: 'setLastBattleSkill', type: 'method', description: '设置最后使用的战斗技能', code: '$gameActors.actor(1).setLastBattleSkill($dataSkills[1])' },
      { name: 'isDead', type: 'method', description: '是否死亡', code: '$gameActors.actor(1).isDead()' },
      { name: 'states', type: 'method', description: '获取状态列表', code: '$gameActors.actor(1).states()' },
      { name: 'skills', type: 'method', description: '获取技能列表', code: '$gameActors.actor(1).skills()' },
      { name: 'equips', type: 'method', description: '获取装备列表', code: '$gameActors.actor(1).equips()' },
      { name: 'equipSlots', type: 'method', description: '获取装备槽位', code: '$gameActors.actor(1).equipSlots()' },
      { name: 'canEquip', type: 'method', description: '是否可以装备', code: '$gameActors.actor(1).canEquip($dataWeapons[1])' },
      { name: 'optimizeEquipments', type: 'method', description: '最优化装备', code: '$gameActors.actor(1).optimizeEquipments()' },
      { name: 'clearEquipments', type: 'method', description: '清空装备', code: '$gameActors.actor(1).clearEquipments()' },
      { name: 'changeEquip', type: 'method', description: '更换装备', code: '$gameActors.actor(1).changeEquip(0, $dataWeapons[1])' },
      { name: 'setName', type: 'method', description: '设置角色名称', code: '$gameActors.actor(1).setName("新名字")' }
    ]
  }
];
