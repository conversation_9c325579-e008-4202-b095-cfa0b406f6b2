// 高级示例 - 复杂的系统操作

import type { RPGExample } from '../types';

export const advancedExamples: RPGExample[] = [
  {
    name: '完整存档系统',
    type: 'method',
    description: '实现完整的存档保存和加载功能',
    code: `// 完整存档系统
function saveGame(slotId) {
  if (!$gameSystem.isSaveEnabled()) {
    console.log('当前无法保存');
    return false;
  }

  $gameSystem.setSavefileId(slotId);
  $gameSystem.onBeforeSave();

  return DataManager.saveGame(slotId)
    .then(() => {
      SoundManager.playSave();
      console.log('保存成功');
      return true;
    })
    .catch(() => {
      SoundManager.playBuzzer();
      console.log('保存失败');
      return false;
    });
}

function loadGame(slotId) {
  if (!DataManager.savefileExists(slotId)) {
    console.log('存档不存在');
    return false;
  }

  return DataManager.loadGame(slotId)
    .then(() => {
      SoundManager.playLoad();
      $gameSystem.onAfterLoad();
      SceneManager.goto(Scene_Map);
      console.log('加载成功');
      return true;
    })
    .catch(() => {
      SoundManager.playBuzzer();
      console.log('加载失败');
      return false;
    });
}

// 使用示例
saveGame(1);`
  },
  {
    name: '战斗系统核心',
    type: 'method',
    description: '战斗系统的核心逻辑',
    code: `// 战斗系统核心
function initBattle(troopId) {
  // 设置敌群
  $gameTroop.setup(troopId);

  // 保存音频状态
  BattleManager.saveBgmAndBgs();

  // 播放战斗音乐
  BattleManager.playBattleBgm();

  // 开始战斗
  BattleManager.startBattle();

  console.log('战斗开始');
}

function processAction(actor, actionType, targetIndex = 0) {
  const action = BattleManager.inputtingAction();

  switch (actionType) {
    case 'attack':
      action.setAttack();
      break;
    case 'guard':
      action.setGuard();
      break;
    case 'skill':
      const skill = actor.skills()[0];
      if (skill) {
        action.setSkill(skill.id);
        actor.setLastBattleSkill(skill);
      }
      break;
  }

  if (action.needsSelection()) {
    action.setTarget(targetIndex);
  }

  console.log('行动设置完成');
}

// 使用示例
initBattle(1);`
  },
  {
    name: '商店系统完整版',
    type: 'method',
    description: '完整的商店买卖系统',
    code: `// 商店系统完整版
function buyItem(item, quantity, price) {
  const totalCost = quantity * price;
  const currentGold = $gameParty.gold();
  const currentNum = $gameParty.numItems(item);
  const maxNum = $gameParty.maxItems(item);

  // 检查条件
  if (currentGold < totalCost) {
    SoundManager.playBuzzer();
    console.log('金钱不足');
    return false;
  }

  if (currentNum + quantity > maxNum) {
    SoundManager.playBuzzer();
    console.log('背包空间不足');
    return false;
  }

  // 执行购买
  $gameParty.loseGold(totalCost);
  $gameParty.gainItem(item, quantity);
  SoundManager.playShop();

  console.log(\`购买成功: \${item.name} x\${quantity}\`);
  return true;
}

function sellItem(item, quantity) {
  const currentNum = $gameParty.numItems(item);
  const sellPrice = Math.floor(item.price / 2);
  const totalEarn = quantity * sellPrice;

  if (currentNum < quantity) {
    SoundManager.playBuzzer();
    console.log('物品数量不足');
    return false;
  }

  $gameParty.loseItem(item, quantity);
  $gameParty.gainGold(totalEarn);
  SoundManager.playShop();

  console.log(\`出售成功: \${item.name} x\${quantity}\`);
  return true;
}

// 使用示例
const item = $dataItems[1];
if (item) {
  buyItem(item, 2, item.price);
}`
  }
];
