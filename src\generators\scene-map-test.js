/**
 * Scene_Map 特殊处理测试示例
 * 展示生成的Scene_Map代码如何重写createDisplayObjects方法
 */

// ===== Scene_Map 场景立即执行方法 =====
(() => {
  "use strict";
  console.log("RPG Editor: 开始处理 Scene_Map");

  // Scene_Map 场景创建方法
  Scene_Map.prototype.create = function() {
    Scene_Base.prototype.create.call(this);
    console.log('RPG Editor: 开始创建 Scene_Map 的自定义对象');

    // 这里会添加编辑器中创建的自定义对象
    // 例如：UI组件、自定义精灵等
    
    console.log('RPG Editor: Scene_Map 自定义对象创建完成');
  };

  // ===== Scene_Map 方法重写 =====
  // 重写 createDisplayObjects - 自定义显示对象创建流程
  Scene_Map.prototype.createDisplayObjects = function() {
    console.log('RPG Editor: 使用自定义的 createDisplayObjects 方法');
    this.createSpriteset();
    this.createWindowLayer();
    // 注意：不调用 this.createAllWindows(); - 跳过原生窗口创建
    this.createButtons();
    console.log('RPG Editor: createDisplayObjects 完成，已跳过原生窗口创建');
  };

  // 重写 createSpriteset - 跳过原生精灵集创建
  Scene_Map.prototype.createSpriteset = function() {
    console.log('RPG Editor: 跳过原生精灵集创建，使用编辑器对象');
  };

  // 重写 createAllWindows - 跳过原生窗口创建
  Scene_Map.prototype.createAllWindows = function() {
    console.log('RPG Editor: 跳过原生窗口创建，使用编辑器对象');
  };

  // 重写 update - 安全检查精灵集
  Scene_Map.prototype.update = function() {
    Scene_Base.prototype.update.call(this);
    if (this._spriteset && this._spriteset.update) {
      this._spriteset.update();
    }
  };

  // 重写 updateTransferPlayer - 安全检查
  Scene_Map.prototype.updateTransferPlayer = function() {
    if ($gamePlayer.isTransferring()) {
      SceneManager.goto(Scene_Map);
    }
  };

  console.log("RPG Editor: Scene_Map 处理完成");
})();

/**
 * 使用说明：
 * 
 * 1. 原始的 Scene_Map.prototype.createDisplayObjects 方法会调用：
 *    - this.createSpriteset();
 *    - this.createWindowLayer();
 *    - this.createAllWindows();  // 这个会被跳过
 *    - this.createButtons();
 * 
 * 2. 修改后的版本跳过了 this.createAllWindows()，这样可以：
 *    - 避免创建原生的窗口系统
 *    - 使用编辑器中自定义的UI组件
 *    - 保持其他必要的初始化流程
 * 
 * 3. 这种方式的优势：
 *    - 保持了Scene_Map的基本结构
 *    - 只替换了需要自定义的部分
 *    - 避免了与原生系统的冲突
 */
