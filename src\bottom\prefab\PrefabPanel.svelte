<script lang="ts">
  /**
   * 预制体面板 - 显示所有可用的预制体
   */
  import { onMount } from 'svelte';
  import PrefabItem from './PrefabItem.svelte';
  import { dragStore } from '../../stores/dragStore';
  
  // 预制体列表
  let prefabs = $state<Array<{
    name: string;
    description: string;
    type: string;
    createdAt: number;
  }>>([]);

  // 拖拽状态
  let isDragOver = $state(false);
  let dropZoneElement: HTMLDivElement;

  // 订阅拖拽状态
  let dragState = $state<{ isDragging: boolean; draggedObject: any }>({ isDragging: false, draggedObject: null });
  dragStore.subscribe(state => {
    dragState = state;
  });

  // 刷新预制体列表
  function refreshPrefabs() {
    if ((window as any).PrefabManager) {
      prefabs = (window as any).PrefabManager.getAllPrefabs();
      console.log('🔧 PrefabPanel: 刷新预制体列表', prefabs.length);
    } else {
      console.warn('🚨 PrefabPanel: PrefabManager 未加载');
      prefabs = [];
    }
  }

  // 删除预制体
  function handleDeletePrefab(name: string) {
    const confirmed = confirm(`确定要删除预制体 "${name}" 吗？`);
    if (confirmed) {
      try {
        if ((window as any).PrefabManager) {
          (window as any).PrefabManager.unregister(name);
          refreshPrefabs(); // 立即刷新
          console.log(`✅ PrefabPanel: 预制体 ${name} 已删除`);
        } else {
          throw new Error('PrefabManager 未加载');
        }
      } catch (error) {
        console.error(`❌ PrefabPanel: 删除预制体 ${name} 失败`, error);
        alert(`删除失败: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }



  // 🔑 鼠标进入处理（类似 DropTarget）
  function handleMouseEnter() {
    if (dragState.isDragging) {
      console.log('🎯 鼠标进入预制体面板拖拽区域');
      isDragOver = true;
      document.body.style.cursor = 'copy';
    }
  }

  // 🔑 鼠标离开处理
  function handleMouseLeave() {
    if (dragState.isDragging) {
      console.log('🎯 鼠标离开预制体面板拖拽区域');
      isDragOver = false;
      document.body.style.cursor = 'grabbing';
    }
  }

  // 🔑 自定义拖拽放置事件处理
  function handleCustomDrop(event: CustomEvent) {
    console.log('🎯 PrefabPanel: 接收到自定义拖拽事件', event.detail);
    isDragOver = false;

    // 🔑 重置鼠标样式
    document.body.style.cursor = '';

    const { object } = event.detail;
    if (!object) {
      console.warn('🎯 PrefabPanel: 没有接收到有效对象');
      return;
    }

    console.log('🔧 PrefabPanel: 接收到拖拽的组件', object);

    // 创建预制体
    handleCreatePrefabFromDrop(object);
  }

  // 🔑 监听拖拽状态变化，重置鼠标样式
  $effect(() => {
    if (!dragState.isDragging) {
      // 拖拽结束时重置鼠标样式
      document.body.style.cursor = '';
      isDragOver = false;
    }
  });

  // 🔑 组件挂载后添加自定义事件监听器
  $effect(() => {
    if (dropZoneElement) {
      dropZoneElement.addEventListener('customdrop', handleCustomDrop as EventListener);

      return () => {
        dropZoneElement?.removeEventListener('customdrop', handleCustomDrop as EventListener);
      };
    }
  });

  // 🔑 从拖拽的组件创建预制体
  function handleCreatePrefabFromDrop(component: any) {
    try {
      // 获取原始组件对象
      const originalComponent = component._originalObject || component;

      if (!originalComponent) {
        throw new Error('无法获取原始组件对象');
      }

      // � 直接使用对象的名字作为预制体名称
      const name = originalComponent.name || `${originalComponent.constructor.name}_预制体`;
      const description = `基于 ${originalComponent.constructor.name} 创建的预制体`;

      // 检查名称是否已存在，如果存在则添加数字后缀
      let finalName = name;
      let counter = 1;
      while (prefabs.some(p => p.name === finalName)) {
        finalName = `${name}_${counter}`;
        counter++;
      }

      // 创建预制体
      if ((window as any).PrefabManager) {
        (window as any).PrefabManager.register(finalName, originalComponent, description);
        refreshPrefabs(); // 立即刷新
        console.log(`✅ PrefabPanel: 预制体 ${finalName} 创建成功`);
      } else {
        throw new Error('PrefabManager 未加载');
      }

    } catch (error) {
      console.error('❌ PrefabPanel: 创建预制体失败', error);
    }
  }

  // 组件挂载时刷新列表
  onMount(() => {
    refreshPrefabs();
  });
</script>

<div
  class="prefab-panel"
  class:drag-over={isDragOver}
  bind:this={dropZoneElement}
  onmouseenter={handleMouseEnter}
  onmouseleave={handleMouseLeave}
  data-drop-target="prefab-panel"
  role="region"
  aria-label="预制体面板拖拽区域"
>
  <!-- 预制体网格 -->
  <div class="prefab-grid">
    <!-- 🔑 拖拽提示区域 -->
    {#if isDragOver}
      <div class="drop-hint">
        <div class="drop-hint-icon">🎯</div>
        <p class="drop-hint-text">释放以创建预制体</p>
        <p class="drop-hint-sub">将自动弹出命名对话框</p>
      </div>
    {/if}

    {#if prefabs.length === 0 && !isDragOver}
      <div class="empty-state">
        <div class="empty-icon">🧩</div>
        <p class="empty-text">暂无预制体</p>
        <p class="empty-hint">
          拖拽组件到此处创建预制体，或右键选择"创建预制体"
        </p>
      </div>
    {:else if !isDragOver}
      {#each prefabs as prefab (prefab.name)}
        <PrefabItem
          {prefab}
          onDelete={() => handleDeletePrefab(prefab.name)}
        />
      {/each}
    {/if}
  </div>
</div>

<style>
  .prefab-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border, #e2e8f0);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
  }

  .prefab-panel.drag-over {
    border-color: var(--theme-primary, #3b82f6);
    background: var(--theme-primary-light, #eff6ff);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }

  .prefab-grid {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
    align-content: start;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    text-align: center;
    color: var(--theme-text-secondary, #718096);
  }

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
  }

  .empty-hint {
    font-size: 12px;
    margin: 0;
    max-width: 200px;
    line-height: 1.4;
  }

  /* 🔑 拖拽提示样式 */
  .drop-hint {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    text-align: center;
    background: var(--theme-primary-light, #eff6ff);
    border: 2px dashed var(--theme-primary, #3b82f6);
    border-radius: 8px;
    margin: 16px;
    animation: pulse 2s infinite;
  }

  .drop-hint-icon {
    font-size: 48px;
    margin-bottom: 16px;
    animation: bounce 1s infinite;
  }

  .drop-hint-text {
    font-size: 18px;
    font-weight: 600;
    color: var(--theme-primary, #3b82f6);
    margin: 0 0 8px 0;
  }

  .drop-hint-sub {
    font-size: 14px;
    color: var(--theme-text-secondary, #718096);
    margin: 0;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }
</style>
