// 数据对象 - 静态游戏数据（来自data文件夹的JSON）

import type { RPGObject } from './types';

export const dataObjects: RPGObject[] = [
  {
    name: '$dataSystem',
    description: '系统设置数据',
    category: 'data',
    properties: [
      { name: 'gameTitle', type: 'property', description: '游戏标题', code: '$dataSystem.gameTitle' },
      { name: 'titleBgm', type: 'property', description: '标题BGM设置', code: '$dataSystem.titleBgm' },
      { name: 'battleBgm', type: 'property', description: '战斗BGM设置', code: '$dataSystem.battleBgm' },
      { name: 'gameoverMe', type: 'property', description: '游戏结束ME音乐', code: '$dataSystem.gameoverMe' },
      { name: 'startMapId', type: 'property', description: '起始地图ID', code: '$dataSystem.startMapId' },
      { name: 'versionId', type: 'property', description: '版本ID', code: '$dataSystem.versionId' },
      { name: 'hasEncryptedImages', type: 'property', description: '是否有加密图像', code: '$dataSystem.hasEncryptedImages' },
      { name: 'hasEncryptedAudio', type: 'property', description: '是否有加密音频', code: '$dataSystem.hasEncryptedAudio' },
      { name: 'encryptionKey', type: 'property', description: '加密密钥', code: '$dataSystem.encryptionKey' }
    ],
    methods: []
  },
  {
    name: '$dataMap',
    description: '当前地图数据',
    category: 'data',
    properties: [
      { name: 'mapId', type: 'property', description: '地图ID', code: '$dataMap.mapId' },
      { name: 'displayName', type: 'property', description: '地图显示名', code: '$dataMap.displayName' },
      { name: 'events', type: 'property', description: '事件数组', code: '$dataMap.events' },
      { name: 'width', type: 'property', description: '地图宽度', code: '$dataMap.width' },
      { name: 'height', type: 'property', description: '地图高度', code: '$dataMap.height' }
    ],
    methods: []
  },
  {
    name: '$dataItems',
    description: '物品数据数组',
    category: 'data',
    properties: [
      { name: 'length', type: 'property', description: '物品总数', code: '$dataItems.length' }
    ],
    methods: [
      { name: 'access', type: 'method', description: '访问指定物品', code: '$dataItems[1]' },
      { name: 'find', type: 'method', description: '查找物品', code: '$dataItems.find(item => item && item.name === "药草")' }
    ]
  },
  {
    name: '$dataWeapons',
    description: '武器数据数组',
    category: 'data',
    properties: [
      { name: 'length', type: 'property', description: '武器总数', code: '$dataWeapons.length' }
    ],
    methods: [
      { name: 'access', type: 'method', description: '访问指定武器', code: '$dataWeapons[1]' },
      { name: 'find', type: 'method', description: '查找武器', code: '$dataWeapons.find(weapon => weapon && weapon.name === "铜剑")' }
    ]
  },
  {
    name: '$dataArmors',
    description: '防具数据数组',
    category: 'data',
    properties: [
      { name: 'length', type: 'property', description: '防具总数', code: '$dataArmors.length' }
    ],
    methods: [
      { name: 'access', type: 'method', description: '访问指定防具', code: '$dataArmors[1]' },
      { name: 'find', type: 'method', description: '查找防具', code: '$dataArmors.find(armor => armor && armor.name === "皮甲")' }
    ]
  },
  {
    name: '$dataSkills',
    description: '技能数据数组',
    category: 'data',
    properties: [
      { name: 'length', type: 'property', description: '技能总数', code: '$dataSkills.length' }
    ],
    methods: [
      { name: 'access', type: 'method', description: '访问指定技能', code: '$dataSkills[1]' },
      { name: 'find', type: 'method', description: '查找技能', code: '$dataSkills.find(skill => skill && skill.name === "治疗")' }
    ]
  }
];
