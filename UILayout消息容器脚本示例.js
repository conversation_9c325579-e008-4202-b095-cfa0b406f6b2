/**
 * 🎨 UILayout消息容器脚本示例 - 基于回调机制
 *
 * 这个脚本应该写在编辑器中UILayout组件的脚本面板中
 * 展示如何使用MessageInterceptor的回调机制处理消息显示
 */

/**
 * 🚀 onStart - 组件启动时执行
 * 初始化消息容器的状态和属性，注册回调函数
 */
function onStart() {
    console.log("消息容器启动:", self.name || "MessageContainer");

    // 🔑 初始化容器状态
    self.visible = false;  // 初始隐藏
    self.currentMessageLabel = null;  // 当前消息标签
    self.currentChoiceButtons = [];   // 当前选择按钮数组
    self.isProcessingMessage = false; // 是否正在处理消息

    // 🔑 设置容器样式（如果需要）
    if (self.setBackgroundImage) {
        self.setBackgroundImage("MessageBox.png");
    }

    // 🔑 注册消息拦截器回调
    if (window.MessageInterceptor) {
        // 定义回调函数
        self.messageCallback = function(type, data) {
            console.log("📞 收到消息回调:", type, data);

            switch (type) {
                case 'message':
                    self.showCustomMessage(data);
                    break;
                case 'choice':
                    self.showCustomChoices(data);
                    break;
                default:
                    console.warn("⚠️ 未知的消息类型:", type);
            }
        };

        // 注册回调
        window.MessageInterceptor.addCallback(self.messageCallback);
        console.log("✅ 消息回调已注册");
    } else {
        console.warn("⚠️ MessageInterceptor 未找到");
    }

    console.log("✅ 消息容器初始化完成");
}

/**
 * 📝 显示自定义消息
 * @param {Object} messageData - 消息数据
 */
self.showCustomMessage = function(messageData) {
    if (self.isProcessingMessage) {
        console.log("⚠️ 正在处理消息，跳过重复请求");
        return;
    }

    self.isProcessingMessage = true;
    console.log("📝 显示自定义消息:", messageData);

    // 🔑 显示容器
    self.visible = true;

    // 🔑 清除之前的内容
    self.clearDynamicContent();

    // 🔑 创建消息文本标签
    if (window.UILabel) {
        self.currentMessageLabel = new UILabel({
            text: messageData.text,
            x: 20,
            y: 20,
            width: self.width - 40,
            height: self.height - 40,
            fontSize: 18,
            color: "#ffffff",
            wordWrap: true,
            name: "DynamicMessageText"
        });

        // 添加到容器中
        self.addChild(self.currentMessageLabel);

        // 🔑 添加打字机效果（如果需要）
        if (window.UIMask) {
            const typewriterMask = new UIMask({
                target: self.currentMessageLabel,
                maskType: "typewriter",
                speed: 50,
                width: self.currentMessageLabel.width,
                height: self.currentMessageLabel.height,
                name: "TypewriterMask"
            });

            if (typewriterMask.startEffect) {
                typewriterMask.startEffect();
            }
        }
    }

    console.log("✅ 消息显示完成");
};

/**
 * 🎯 显示自定义选择项
 * @param {Object} choiceData - 选择数据
 */
self.showCustomChoices = function(choiceData) {
    console.log("🎯 显示自定义选择项:", choiceData);

    // 🔑 显示容器
    self.visible = true;

    // 🔑 清除之前的选择按钮
    self.clearChoiceButtons();

    // 🔑 创建选择按钮
    if (window.UIButton && choiceData.choices) {
        choiceData.choices.forEach((choice, index) => {
            const button = new UIButton({
                text: choice,
                x: 20,
                y: 100 + index * 45,  // 在消息下方排列
                width: self.width - 40,
                height: 35,
                fontSize: 14,
                backgroundImage: "ChoiceButton.png",
                name: `DynamicChoiceButton_${index}`,
                // 🔑 点击事件处理
                onClick: function() {
                    self.onChoiceButtonClick(index);
                }
            });

            // 添加到容器中
            self.addChild(button);
            self.currentChoiceButtons.push(button);
        });
    }

    console.log("✅ 选择项显示完成，共", choiceData.choices.length, "个选项");
};

/**
 * 🎯 处理选择按钮点击
 * @param {number} index - 选择的索引
 */
self.onChoiceButtonClick = function(index) {
    console.log("选择按钮被点击:", index);

    // 🔑 调用拦截器的选择处理方法
    if (window.MessageInterceptor) {
        window.MessageInterceptor.onChoiceSelected(index);
    }

    // 🔑 隐藏容器和清理内容
    self.hideAndClear();
};

/**
 * 🧹 清除动态内容
 */
self.clearDynamicContent = function() {
    // 清除消息标签
    if (self.currentMessageLabel && self.currentMessageLabel.parent) {
        self.removeChild(self.currentMessageLabel);
        self.currentMessageLabel = null;
    }

    // 清除选择按钮
    self.clearChoiceButtons();

    // 清除打字机遮罩
    const typewriterMask = self.children.find(child => child.name === "TypewriterMask");
    if (typewriterMask) {
        self.removeChild(typewriterMask);
    }
};

/**
 * 🧹 清除选择按钮
 */
self.clearChoiceButtons = function() {
    self.currentChoiceButtons.forEach(button => {
        if (button.parent) {
            self.removeChild(button);
        }
    });
    self.currentChoiceButtons = [];
};

/**
 * 🎭 隐藏容器并清理内容
 */
self.hideAndClear = function() {
    self.visible = false;
    self.clearDynamicContent();
    self.isProcessingMessage = false;

    console.log("📦 消息容器已隐藏并清理");
};

/**
 * 💥 onDestroy - 组件销毁时执行
 * 清理资源和回调
 */
function onDestroy() {
    console.log("消息容器销毁:", self.name || "MessageContainer");

    // 🔑 移除消息拦截器回调
    if (window.MessageInterceptor && self.messageCallback) {
        window.MessageInterceptor.removeCallback(self.messageCallback);
        console.log("✅ 消息回调已移除");
    }

    // 清理所有动态内容
    self.clearDynamicContent();

    console.log("✅ 消息容器资源已清理");
}
