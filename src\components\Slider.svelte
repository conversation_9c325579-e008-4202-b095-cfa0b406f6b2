<script lang="ts">
  /**
   * Slider 滑动条组件
   * 基于原生 input[type="range"] 的简化实现，支持历史记录
   */

  import { historyManager } from '../historyManager';

  // Props using Svelte 5 syntax
  let {
    value = $bindable(0),
    min = 0,
    max = 100,
    step = 1,
    disabled = false,
    size = 'md',
    variant = 'default',
    id = '',
    name = '',
    ariaLabel = '',
    onInput = () => {},
    onChange = () => {},
    onMove = undefined,
    // 历史记录相关
    targetObject = undefined,
    fieldName = undefined,
    enableHistory = true
  }: {
    value?: number;
    min?: number;
    max?: number;
    step?: number;
    disabled?: boolean;
    size?: 'sm' | 'md' | 'lg';
    variant?: 'default' | 'success' | 'warning' | 'error';
    id?: string;
    name?: string;
    ariaLabel?: string;
    onInput?: (value: number, event: Event) => void;
    onChange?: (value: number, event: Event) => void;
    onMove?: (value: number, initialValue?: number) => void; // 拖拽过程中的实时回调
    // 历史记录相关
    targetObject?: any;
    fieldName?: string;
    enableHistory?: boolean;
  } = $props();

  // 内部状态
  let sliderElement: HTMLInputElement;

  // 滑块拖拽的历史记录状态
  let isDragging = false;
  let initialValue: number | null = null;

  // 处理拖拽开始（记录初始值）
  function handleMouseDown(_event: Event) {
    if (enableHistory && historyManager.isRecording()) {
      isDragging = true;
      initialValue = value;
      console.log("🎚️ Slider: 开始拖拽，记录初始值:", initialValue);
    }
  }

  // 处理拖拽结束（记录历史）
  function handleMouseUp(_event: Event) {
    if (isDragging && enableHistory && initialValue !== null) {
      const finalValue = value;

      console.log("🎚️ Slider: 拖拽结束，检查历史记录:", {
        enableHistory,
        initialValue,
        finalValue,
        hasChanged: initialValue !== finalValue,
        targetObject: targetObject?.className || 'none',
        fieldName: fieldName || 'none'
      });

      // 只有当值真正发生变化时才记录
      if (initialValue !== finalValue && historyManager.isRecording()) {
        if (targetObject && fieldName) {
          // 记录到模型对象
          historyManager.recordChange(targetObject, fieldName, initialValue, finalValue);
          console.log("📝 HistoryManager: 已记录滑块变更");
        } else {
          // 如果没有指定目标对象，创建一个虚拟对象来记录
          const virtualObject = {
            className: `Slider_${name || id || 'unnamed'}`,
            [fieldName || 'value']: finalValue
          };
          historyManager.recordChange(virtualObject, fieldName || 'value', initialValue, finalValue);
          console.log("📝 HistoryManager: 已记录滑块虚拟对象变更");
        }
      }

      // 清理状态
      isDragging = false;
      initialValue = null;

      // 🎯 拖拽完成后主动失焦，确保快捷键能正常工作
      setTimeout(() => {
        if (sliderElement) {
          sliderElement.blur();
          console.log("📝 Slider: 拖拽完成后主动失焦");
        }
      }, 100);
    }
  }

  // 处理输入事件
  function handleInput(event: Event) {
    const target = event.target as HTMLInputElement;
    const newValue = Number(target.value);
    console.log('Slider handleInput:', newValue);
    value = newValue;
    onInput(newValue, event);

    // 🔑 如果正在拖拽且有 onMove 回调，调用它进行实时同步
    if (isDragging && onMove && initialValue !== null) {
      onMove(newValue, initialValue);
    }
  }

  function handleChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const newValue = Number(target.value);
    console.log('Slider handleChange:', newValue);
    value = newValue;
    // 🔑 传递初始值作为第三个参数，用于多选同步
    (onChange as any)(newValue, event, initialValue);
  }

  // 获取容器样式类
  function getContainerClass() {
    const baseClass = 'slider-container';
    const sizeClass = size === 'sm' ? 'component-sm' : size === 'lg' ? 'component-lg' : '';
    const variantClass = variant === 'success' ? 'component-success' :
                        variant === 'warning' ? 'component-warning' :
                        variant === 'error' ? 'component-error' : '';
    const disabledClass = disabled ? 'slider-disabled' : '';

    return [baseClass, sizeClass, variantClass, disabledClass]
      .filter(Boolean)
      .join(' ');
  }

  // 公开方法
  export function focus() {
    sliderElement?.focus();
  }

  export function blur() {
    sliderElement?.blur();
  }
</script>

<div class={getContainerClass()}>
  <input
    bind:this={sliderElement}
    type="range"
    {id}
    {name}
    {min}
    {max}
    {step}
    {value}
    {disabled}
    aria-label={ariaLabel}
    class="slider-input"
    onmousedown={handleMouseDown}
    onmouseup={handleMouseUp}
    oninput={handleInput}
    onchange={handleChange}
  />
</div>

<style>
  @import './shared-styles.css';

  .slider-container {
    position: relative;
    width: 100%;
    padding: 8px 0;
    font-size: var(--component-font-size);
    font-family: var(--component-font-family);
  }

  .slider-container.slider-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .slider-input {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0 !important;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
    transition: var(--component-transition);
  }

  /* WebKit 浏览器样式 */
  .slider-input::-webkit-slider-track {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0 !important;
    border: none;
  }

  .slider-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: rgba(64, 105, 240, 0.9);
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .slider-input::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .slider-input::-webkit-slider-thumb:active {
    transform: scale(1.2);
  }

  /* Firefox 浏览器样式 */
  .slider-input::-moz-range-track {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0 !important;
    border: none;
  }

  .slider-input::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: rgba(64, 105, 240, 0.9);
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .slider-input::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .slider-input::-moz-range-thumb:active {
    transform: scale(1.2);
  }

  /* 禁用状态 */
  .slider-disabled .slider-input {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .slider-disabled .slider-input::-webkit-slider-thumb {
    cursor: not-allowed;
  }

  .slider-disabled .slider-input::-moz-range-thumb {
    cursor: not-allowed;
  }

  /* 尺寸变体 */
  .slider-sm .slider-input {
    height: 4px;
  }

  .slider-sm .slider-input::-webkit-slider-track {
    height: 4px;
  }

  .slider-sm .slider-input::-moz-range-track {
    height: 4px;
  }

  .slider-sm .slider-input::-webkit-slider-thumb {
    width: 14px;
    height: 14px;
  }

  .slider-sm .slider-input::-moz-range-thumb {
    width: 14px;
    height: 14px;
  }

  .slider-lg .slider-input {
    height: 8px;
  }

  .slider-lg .slider-input::-webkit-slider-track {
    height: 8px;
  }

  .slider-lg .slider-input::-moz-range-track {
    height: 8px;
  }

  .slider-lg .slider-input::-webkit-slider-thumb {
    width: 22px;
    height: 22px;
  }

  .slider-lg .slider-input::-moz-range-thumb {
    width: 22px;
    height: 22px;
  }

  /* 状态变体 */
  .slider-success .slider-input::-webkit-slider-thumb {
    background: #22c55e;
  }

  .slider-success .slider-input::-moz-range-thumb {
    background: #22c55e;
  }

  .slider-warning .slider-input::-webkit-slider-thumb {
    background: #f59e0b;
  }

  .slider-warning .slider-input::-moz-range-thumb {
    background: #f59e0b;
  }

  .slider-error .slider-input::-webkit-slider-thumb {
    background: #ef4444;
  }

  .slider-error .slider-input::-moz-range-thumb {
    background: #ef4444;
  }

  /* 焦点样式 */
  .slider-input:focus {
    outline: none;
  }

  .slider-input:focus::-webkit-slider-thumb {
    outline: 2px solid rgba(64, 105, 240, 0.3);
    outline-offset: 2px;
  }

  .slider-input:focus::-moz-range-thumb {
    outline: 2px solid rgba(64, 105, 240, 0.3);
    outline-offset: 2px;
  }
</style>
