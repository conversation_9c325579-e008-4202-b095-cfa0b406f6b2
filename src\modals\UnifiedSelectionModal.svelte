<script lang="ts">
  import Modal from './Modal.svelte';
  import DataContainer from './unified/containers/DataContainer.svelte';
  import FieldContainer from './unified/containers/FieldContainer.svelte';

  // 导入服务和类型
  import { getDynamicDataCategoriesWithCache } from './unified/dynamicDataService';
  import { detectItemType } from './unified/services/bindingRuleService';
  import type { Category, DataItem, Method, GeneratedCode, DataField } from './unified/types';
  import type {
    DataContainerItem,
    FieldContainerItem,
    ContainerPanel,
    ItemType
  } from './unified/types/containerTypes';

  let {
    isOpen = $bindable(false),
    onConfirm = () => {},
    mode = 'method', // 'method' | 'data' | 'event'
    initialPath = ''
  }: {
    isOpen: boolean;
    onConfirm: (code: string) => void;
    mode?: 'method' | 'data' | 'event';
    initialPath?: string;
  } = $props();

  // 状态管理
  let selectedCategory = $state<Category | null>(null);
  let selectedItem = $state<DataItem | Method | null>(null);
  let selectedFieldPath = $state<string>('');
  let selectedField = $state<DataField | null>(null);
  let parameters = $state<Record<string, any>>({});
  let generatedCode = $state<GeneratedCode | null>(null);

  // 新的模块化面板状态
  let containerPanels = $state<ContainerPanel[]>([]);
  let dataContainerItems = $state<DataContainerItem[]>([]);
  let selectedDataItem = $state<DataContainerItem | null>(null);

  // 数据转换函数 - 延迟类型检测避免无限循环
  function convertToDataContainerItems(categories: Record<string, Category>): DataContainerItem[] {
    console.log('🔄 convertToDataContainerItems called with categories:', Object.keys(categories));
    const items: DataContainerItem[] = [];

    Object.values(categories).forEach(category => {
      console.log(`🔄 Processing category: ${category.label}, items count: ${Object.keys(category.items).length}`);

      Object.values(category.items).forEach(item => {
        if ('path' in item) {
          console.log(`🔄 Processing item: ${item.label} (${item.path})`);

          // 延迟类型检测，避免在初始化时访问 window 对象
          let itemType: ItemType = 'object'; // 默认为对象类型

          try {
            // 只在安全的情况下检测类型
            if (typeof window !== 'undefined' && item.path) {
              const value = getFieldValueByPath(item.path);
              itemType = detectItemType(value);
              console.log(`🔄 Item ${item.path} type detected: ${itemType}`);
            }
          } catch (error) {
            console.warn(`Error detecting type for ${item.path}:`, error);
            itemType = 'object'; // 出错时默认为对象
          }

          const containerItem = {
            id: item.id,
            name: item.label,
            label: item.label,
            type: itemType,
            path: item.path,
            description: item.description,
            value: undefined // 延迟获取值
          };

          console.log(`🔄 Created container item:`, containerItem);
          items.push(containerItem);
        }
      });
    });

    console.log(`🔄 convertToDataContainerItems completed, total items: ${items.length}`);
    return items;
  }

  function convertToFieldContainerItems(value: any, basePath: string): FieldContainerItem[] {
    console.log('🔧 convertToFieldContainerItems called with basePath:', basePath, 'value type:', typeof value);
    const items: FieldContainerItem[] = [];

    if (!value || typeof value !== 'object') {
      console.log('🔧 Value is not an object, returning empty array');
      return items;
    }

    try {
      if (Array.isArray(value)) {
        // 处理数组：显示所有元素
        for (let i = 0; i < value.length; i++) {
          const item = value[i];
          if (item !== null && item !== undefined) {
            const itemType = detectItemType(item);
            const fieldPath = `${basePath}[${i}]`;

            items.push({
              id: `${basePath}[${i}]`,
              name: `[${i}]`,
              label: `[${i}] (${itemType})`,
              type: itemType,
              path: fieldPath,
              description: `${fieldPath} - ${itemType}类型`,
              value: item,
              parentPath: basePath
            });
          }
        }
      } else {
        // 处理对象：显示所有属性
        const keys = Object.keys(value);

        console.log('🔧 Object keys found:', keys.length, 'keys:', keys.slice(0, 10));

        for (const key of keys) {
          try {
            const fieldValue = value[key];

            // 跳过函数和特殊属性（但保留 RPG Maker MZ 的 _ 开头属性）
            if (typeof fieldValue === 'function' || key === 'constructor') {
              console.log('🔧 Skipping function/constructor:', key);
              continue;
            }

            // 跳过一些特殊的内部属性，但保留有用的 _ 开头属性
            if (key.startsWith('__') || key === '_events' || key === '_eventsCount') {
              console.log('🔧 Skipping special internal property:', key);
              continue;
            }

            const itemType = detectItemType(fieldValue);
            const fieldPath = `${basePath}.${key}`;

            console.log('🔧 Adding field:', key, 'type:', itemType, 'value:', fieldValue);

            items.push({
              id: fieldPath,
              name: key,
              label: `${key} (${itemType})`,
              type: itemType,
              path: fieldPath,
              description: `${fieldPath} - ${itemType}类型`,
              value: fieldValue,
              parentPath: basePath
            });
          } catch (error) {
            console.warn(`Error processing field ${key}:`, error);
          }
        }

        console.log('🔧 Total fields generated:', items.length);
      }
    } catch (error) {
      console.warn(`Error processing value at ${basePath}:`, error);
    }

    return items;
  }


  // 获取当前模式的数据和错误状态 - 只在模态框打开时执行
  let dataResult = $derived.by(() => {
    // 只有在模态框打开时才获取数据
    if (!isOpen) {
      return { data: {}, error: null };
    }

    if (mode === 'method') {
      // 方法模式暂时不支持，返回错误
      return {
        data: {},
        error: '❌ 方法模式暂未实现，请使用数据模式。'
      };
    } else if (mode === 'data') {
      try {
        // 使用动态数据获取
        const dynamicCategories = getDynamicDataCategoriesWithCache();

        // 检查是否有有效的 RPG Maker MZ 数据
        const hasRpgMakerData = Object.keys(dynamicCategories).length > 0 &&
          Object.values(dynamicCategories).some(category =>
            Object.values(category.items).some(item =>
              'path' in item && item.path.startsWith('$')
            )
          );

        if (!hasRpgMakerData) {
          return {
            data: {},
            error: '❌ 未检测到 RPG Maker MZ 数据！请确保在 RPG Maker MZ 环境中使用此功能。'
          };
        }

        return { data: dynamicCategories, error: null };
      } catch (error) {
        return {
          data: {},
          error: `❌ 获取动态数据时出错: ${error instanceof Error ? error.message : String(error)}`
        };
      }
    } else {
      // event 模式暂时不支持
      return {
        data: {},
        error: '❌ 事件模式暂未实现，请使用数据模式。'
      };
    }
  });

  // 从结果中提取数据和错误
  let currentData = $derived(dataResult.data);
  let currentError = $derived(dataResult.error);

  // 初始化数据容器项 - 只在模态框打开时执行
  let initializedData = $derived.by(() => {
    // 只有在模态框打开时才初始化数据
    if (!isOpen || mode !== 'data' || !currentData || Object.keys(currentData).length === 0) {
      return { items: [], panels: [] };
    }

    const items = convertToDataContainerItems(currentData);
    const panels = [{
      id: 'data-objects',
      title: '📊 数据对象',
      type: 'data' as const,
      path: '',
      items: items
    }];
    return { items, panels };
  });

  // 跟踪是否已经执行过自动导航
  let hasAutoNavigated = $state(false);

  // 同步到状态变量 - 添加调试和防止无限循环
  $effect(() => {
    console.log('🔍 UnifiedSelectionModal effect - isOpen:', isOpen, 'mode:', mode, 'items count:', initializedData.items.length, 'initialPath:', initialPath);

    // 只在模态框打开且是数据模式时更新
    if (isOpen && mode === 'data') {
      dataContainerItems = initializedData.items;
      containerPanels = initializedData.panels;

      // 如果有初始路径且还没有执行过自动导航，则执行自动定位
      if (initialPath && !hasAutoNavigated && initializedData.items.length > 0) {
        hasAutoNavigated = true;
        setTimeout(() => {
          autoNavigateToPath(initialPath);
        }, 100);
      }
    } else if (!isOpen) {
      // 模态框关闭时清理状态
      dataContainerItems = [];
      containerPanels = [];
      selectedDataItem = null;
      selectedFieldPath = '';
      selectedField = null;
      generatedCode = null;
      hasAutoNavigated = false; // 重置自动导航标志
    }
  });

  // 自动导航到指定路径
  function autoNavigateToPath(path: string) {
    try {
      console.log('🎯 自动导航到路径:', path);

      // 清理路径（移除 window. 前缀和 {{}} 包装）
      let cleanPath = path.replace(/^window\./, '').replace(/^\{\{|\}\}$/g, '');

      // 分割路径为段
      const pathSegments = cleanPath.split(/\.|\[|\]/).filter(segment => segment !== '');

      if (pathSegments.length === 0) return;

      // 找到根数据对象
      const rootPath = pathSegments[0];
      const rootItem = dataContainerItems.find(item =>
        item.path === rootPath || item.path === `$${rootPath}`
      );

      if (rootItem) {
        console.log('🎯 找到根对象:', rootItem);

        // 选择根对象
        handleDataItemSelect(rootItem);

        // 如果有更多路径段，继续导航
        if (pathSegments.length > 1) {
          setTimeout(() => {
            navigateToSubPath(pathSegments.slice(1), rootItem.path);
          }, 100);
        }
      } else {
        console.warn('🚨 未找到根对象:', rootPath);
      }
    } catch (error) {
      console.error('🚨 自动导航失败:', error);
    }
  }

  // 导航到子路径
  function navigateToSubPath(remainingSegments: string[], currentPath: string) {
    if (remainingSegments.length === 0) return;

    const nextSegment = remainingSegments[0];
    const isArrayIndex = nextSegment.match(/^\d+$/);
    const nextPath = isArrayIndex
      ? `${currentPath}[${nextSegment}]`  // 数组索引
      : `${currentPath}.${nextSegment}`;   // 对象属性

    console.log('🎯 导航到子路径:', nextPath, '查找段:', nextSegment, '是数组索引:', !!isArrayIndex);

    // 在当前面板中查找对应的字段
    const currentPanel = containerPanels[containerPanels.length - 1];
    if (currentPanel && currentPanel.type === 'field') {
      console.log('🔍 当前面板项目:', currentPanel.items.map(item => ({ name: item.name, path: item.path })));

      let targetItem;
      if (isArrayIndex) {
        // 对于数组索引，查找名称为 [index] 的项目
        targetItem = currentPanel.items.find(item =>
          item.name === `[${nextSegment}]`
        );
      } else {
        // 对于对象属性，查找名称匹配的项目
        targetItem = currentPanel.items.find(item =>
          item.name === nextSegment
        );
      }

      if (targetItem) {
        console.log('🎯 找到目标字段:', targetItem);

        // 选择该字段
        handleFieldItemSelect(targetItem as FieldContainerItem, containerPanels.length - 1);

        // 如果还有更多路径段，继续导航
        if (remainingSegments.length > 1) {
          setTimeout(() => {
            navigateToSubPath(remainingSegments.slice(1), targetItem.path);
          }, 200);
        }
      } else {
        console.warn('🚨 未找到目标字段:', nextSegment, '在当前面板中');
        console.warn('🔍 可用字段:', currentPanel.items.map(item => item.name));
      }
    } else {
      console.warn('🚨 当前面板不是字段面板或不存在');
    }
  }

  // 新的事件处理函数
  function handleDataItemSelect(item: DataContainerItem) {
    console.log('🎯 handleDataItemSelect called with item:', item);

    selectedDataItem = item;
    selectedFieldPath = item.path;

    // 设置选中的字段信息用于代码生成
    selectedField = {
      name: item.name,
      label: item.label,
      type: item.type as any,
      description: item.description || ''
    };

    console.log('🎯 Getting value for path:', item.path);

    // 创建字段容器面板
    try {
      const value = getFieldValueByPath(item.path);
      console.log('🎯 Retrieved value:', value, 'type:', typeof value);

      if (value) {
        console.log('🎯 Converting to field container items...');
        const fieldItems = convertToFieldContainerItems(value, item.path);
        console.log('🎯 Generated field items:', fieldItems.length, 'items');

        // 替换或添加字段面板
        containerPanels = [
          containerPanels[0], // 保留数据对象面板
          {
            id: `fields-${item.id}`,
            title: `🔍 ${item.name}`,
            type: 'field',
            path: item.path,
            items: fieldItems
          }
        ];

        console.log('🎯 Updated container panels:', containerPanels.length, 'panels');
      } else {
        console.warn('🚨 No value found for path:', item.path);
      }
    } catch (error) {
      console.error('🚨 Error in handleDataItemSelect:', error);
    }

    // 为数据对象生成代码
    updateGeneratedCodeForField();
  }

  function handleFieldItemSelect(item: FieldContainerItem, panelIndex: number) {
    selectedFieldPath = item.path;
    selectedField = {
      name: item.name,
      label: item.label,
      type: item.type as any,
      description: item.description || ''
    };

    // 更新当前面板的选中项
    if (containerPanels[panelIndex]) {
      containerPanels[panelIndex].selectedItem = item;
    }

    // 如果选择的是对象或数组，添加新的面板
    if (item.type === 'object' || item.type === 'array') {
      const value = getFieldValueByPath(item.path);
      if (value) {
        const fieldItems = convertToFieldContainerItems(value, item.path);

        // 添加新面板，移除后续面板
        const newPanels = containerPanels.slice(0, panelIndex + 1);
        newPanels.push({
          id: `fields-${item.id}`,
          title: `🔍 ${item.name}`,
          type: 'field',
          path: item.path,
          items: fieldItems
        });

        containerPanels = newPanels;
      }
    } else {
      // 如果是基础字段，移除后续面板
      containerPanels = containerPanels.slice(0, panelIndex + 1);
    }

    // 为字段生成代码
    updateGeneratedCodeForField();
  }

  function handleDoubleClick(item?: DataContainerItem | FieldContainerItem) {
    if (generatedCode) {
      onConfirm(generatedCode.code);
      isOpen = false;
    }
  }



  // 监听模态框打开状态（只监听 isOpen 变化）
  $effect(() => {
    console.log('🔍 UnifiedSelectionModal effect - isOpen:', isOpen);
    if (isOpen) {
      console.log('🔍 Modal opened, initializing...');
      // 只重置选择状态，不重置所有状态
      selectedCategory = null;
      selectedItem = null;
      selectedFieldPath = '';
      selectedField = null;
      parameters = {};
      generatedCode = null;
    }
  });

  // 监听数据变化并设置默认类别（只在模态框打开且有数据时执行）
  $effect(() => {
    if (isOpen && currentData && Object.keys(currentData).length > 0) {
      console.log('🔍 Data available, setting default category...');
      const categories = Object.values(currentData);
      console.log('🔍 Available categories:', categories.length);

      // 只在没有选择类别时设置默认类别
      if (!selectedCategory && categories.length > 0) {
        selectedCategory = categories[0];
        console.log('🔍 Selected first category:', selectedCategory?.label);
      }
    } else if (isOpen && currentError) {
      console.log('🔍 Error state:', currentError);
    }
  });



  // 选择类别
  function selectCategory(category: Category) {
    selectedCategory = category;
    selectedItem = null;
    parameters = {};
    generatedCode = null;
  }





  // 根据路径获取字段值
  function getFieldValueByPath(fieldPath: string): any {
    try {
      let path = fieldPath;

      // 处理以 window. 开头的路径
      if (path.startsWith('window.')) {
        path = path.substring(7);
      }

      // 分割路径，处理数组索引
      const parts = path.split(/\.|\[|\]/).filter(part => part !== '');
      let current: any = window;

      for (const part of parts) {
        if (current && typeof current === 'object') {
          if (Array.isArray(current) && /^\d+$/.test(part)) {
            current = current[parseInt(part)];
          } else if (part in current) {
            current = current[part];
          } else {
            return null;
          }
        } else {
          return null;
        }
      }

      return current;
    } catch (error) {
      console.warn(`Error getting value for ${fieldPath}:`, error);
      return null;
    }
  }

  // 从值动态生成字段
  function getDynamicFieldsFromValue(value: any, basePath: string): DataField[] {
    const fields: DataField[] = [];

    if (!value || typeof value !== 'object') {
      return fields;
    }

    try {
      if (Array.isArray(value)) {
        // 处理数组：显示所有元素
        for (let i = 0; i < value.length; i++) {
          const item = value[i];
          if (item !== null && item !== undefined) {
            const itemType = detectFieldType(item);
            fields.push({
              name: `[${i}]`,
              label: `[${i}] (${itemType})`,
              type: itemType,
              description: `${basePath}[${i}] - ${itemType}类型`
            });
          }
        }
      } else {
        // 处理对象：显示所有属性
        const keys = Object.keys(value);

        for (const key of keys) {
          try {
            const fieldValue = value[key];

            // 跳过函数和特殊属性
            if (typeof fieldValue === 'function' || key.startsWith('_') || key === 'constructor') {
              continue;
            }

            const fieldType = detectFieldType(fieldValue);

            fields.push({
              name: key,
              label: `${key} (${fieldType})`,
              type: fieldType,
              description: `${basePath}.${key} - ${fieldType}类型`
            });
          } catch (error) {
            console.warn(`Error processing field ${key}:`, error);
          }
        }
      }
    } catch (error) {
      console.warn(`Error processing value at ${basePath}:`, error);
    }

    return fields;
  }

  // 检测字段类型
  function detectFieldType(value: any): 'string' | 'number' | 'boolean' | 'object' | 'array' {
    if (value === null || value === undefined) return 'string';
    if (Array.isArray(value)) return 'array';
    if (typeof value === 'object') return 'object';
    if (typeof value === 'boolean') return 'boolean';
    if (typeof value === 'number') return 'number';
    return 'string';
  }

  // 更新参数
  function updateParameters(newParams: Record<string, any>) {
    parameters = { ...parameters, ...newParams };
    updateGeneratedCode();
  }

  // 更新生成的代码
  function updateGeneratedCode() {
    if (!selectedItem) {
      generatedCode = null;
      return;
    }

    if (mode === 'method' && 'template' in selectedItem) {
      // 方法模式：生成方法调用代码
      let code = selectedItem.template;

      // 替换参数占位符
      if ('params' in selectedItem && selectedItem.params) {
        selectedItem.params.forEach((param) => {
          const paramValue = parameters[param.name] || param.defaultValue || 'itemId';
          code = code.replace(new RegExp(`\\{${param.name}\\}`, 'g'), paramValue);
        });
      }

      generatedCode = {
        code,
        description: selectedItem.description,
        category: selectedCategory?.label || '',
        item: selectedItem.label
      };
    } else if (mode === 'data' && 'path' in selectedItem) {
      // 数据模式：生成数据访问代码
      let code = selectedItem.path;
      let description = selectedItem.description;

      // 如果有索引参数
      if ('hasIndex' in selectedItem && selectedItem.hasIndex) {
        const indexValue = parameters.index || 'itemId';
        code = code.replace('{index}', indexValue);
      }

      // 根据访问类型决定如何生成代码
      if (parameters.accessType === 'field' && parameters.field) {
        // 字段访问模式
        code += `.${parameters.field}`;
        description += ` - 访问 ${parameters.field} 字段`;
      } else {
        // 完整数据访问模式（默认）
        if ('hasIndex' in selectedItem && selectedItem.hasIndex) {
          // 如果是数组类型，保持原样（返回完整对象）
          description += ' - 获取完整数据对象';
        } else {
          // 如果不是数组类型，返回整个数据结构
          description += ' - 获取完整数据结构';
        }
      }

      generatedCode = {
        code,
        description,
        category: selectedCategory?.label || '',
        item: selectedItem.label
      };
    }
  }

  // 为字段选择更新生成的代码
  function updateGeneratedCodeForField() {
    if (!selectedField || !selectedFieldPath) {
      generatedCode = null;
      return;
    }

    // 构建完整的访问路径
    let code = `window.${selectedFieldPath}`;

    generatedCode = {
      code,
      description: `访问 ${selectedFieldPath} - ${selectedField.type}类型`,
      category: selectedCategory?.label || '动态字段',
      item: selectedField.name
    };
  }


</script>

<Modal bind:isOpen>
  <div class="unified-modal-container">
    <div class="modal-header">
      <h3>
        {#if mode === 'method'}
          🔧 选择方法
        {:else if mode === 'data'}
          📊 选择数据
        {:else}
          ⚡ 选择事件
        {/if}
      </h3>
    </div>

    <div class="unified-modal">
      {#if mode === 'data'}
        <!-- 数据选择区域 -->
        <div class="data-selection-area">
          {#if currentError}
          <!-- 错误显示 -->
          <div class="error-panel">
            <div class="error-content">
              <div class="error-icon">⚠️</div>
              <div class="error-message">{currentError}</div>
              <div class="error-description">
                请确保您在 RPG Maker MZ 游戏环境中使用此功能，或者检查游戏数据是否已正确加载。
              </div>
            </div>
          </div>
        {:else}
          <!-- 模块化容器面板 -->
          <div class="container-panels">
            {#each containerPanels as panel, panelIndex}
              {#if panel.type === 'data'}
                <DataContainer
                  title={panel.title}
                  items={panel.items}
                  selectedItem={panel.selectedItem}
                  onItemSelect={handleDataItemSelect}
                  onItemDoubleClick={handleDoubleClick}
                />
              {:else if panel.type === 'field'}
                <FieldContainer
                  title={panel.title}
                  items={panel.items}
                  selectedItem={panel.selectedItem}
                  onItemSelect={(item) => handleFieldItemSelect(item, panelIndex)}
                  onItemDoubleClick={handleDoubleClick}
                />
              {/if}
            {/each}

            {#if containerPanels.length === 0}
              <div class="empty-state">
                <p>正在加载数据...</p>
              </div>
            {/if}
          </div>
        {/if}


        </div>

        <!-- 代码预览 - 放在整个模态框的最下面 -->
        {#if generatedCode}
          <div class="bottom-preview">
            <code class="preview-code">{generatedCode.code}</code>
          </div>
        {/if}
      {:else}
        <!-- 方法/事件模式：保持原有三栏布局 -->
        <!-- 左侧：类别选择 -->
        <div class="category-panel">
          <h4>
            {#if mode === 'method'}
              � 方法类别
            {:else}
              ⚡ 事件类别
            {/if}
          </h4>
          <CategorySelector
            categories={Object.values(currentData)}
            selectedCategory={selectedCategory}
            onSelect={selectCategory}
          />
        </div>

        <!-- 中间：项目选择 -->
        <div class="item-panel">
          <h4>
            {selectedCategory?.label || '选择类别'}
            {#if selectedCategory}
              <span class="item-count">({Object.keys(selectedCategory.items).length})</span>
            {/if}
          </h4>
          {#if selectedCategory}
            <ItemSelector
              items={Object.values(selectedCategory.items)}
              selectedItem={selectedItem}
              onSelect={selectItem}
              mode={mode}
            />
          {:else}
            <div class="empty-state">
              <p>请先选择一个类别</p>
            </div>
          {/if}
        </div>

        <!-- 右侧：参数配置和代码预览 -->
        <div class="config-panel">
          <h4>配置和预览</h4>

          {#if selectedItem}
            <!-- 参数配置 -->
            {#if (mode === 'method' && 'params' in selectedItem && selectedItem.params && selectedItem.params.length > 0)}
              <div class="parameter-section">
                <h5>参数配置</h5>
                <ParameterConfig
                  item={selectedItem}
                  parameters={parameters}
                  onUpdate={updateParameters}
                  mode={mode}
                />
              </div>
            {/if}

            <!-- 代码预览 -->
            <div class="preview-section">
              <h5>代码预览</h5>
              <CodePreview
                generatedCode={generatedCode}
              />
            </div>
          {:else}
            <div class="empty-state">
              <p>请选择一个项目</p>
            </div>
          {/if}
        </div>
      {/if}


  </div>
</Modal>

<style>
  .unified-modal-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 700px;
    min-width: 1200px;
    max-width: 1400px;
  }

  .modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--theme-border);
    background: var(--theme-surface);
    flex-shrink: 0;
  }

  .modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--theme-text);
  }

  .unified-modal {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 0;
    padding: 0;
    flex: 1;
    overflow: hidden;
    background: var(--theme-background);
    color: var(--theme-text);
  }

  .category-panel,
  .item-panel,
  .config-panel,
  .object-panel,
  .field-panel {
    display: flex;
    flex-direction: column;
    border: 1px solid var(--theme-border);
    border-radius: 8px;
    overflow: hidden;
    background: var(--theme-surface);
  }

  .category-panel {
    flex: 0 0 240px;
    min-width: 240px;
  }

  .item-panel {
    flex: 2;
    min-width: 450px;
  }

  .config-panel {
    flex: 1;
    min-width: 350px;
  }

  /* 数据模式的布局 */
  .data-selection-area {
    flex: 1;
    display: flex;
    gap: 16px;
    padding: 16px;
    overflow: hidden;
  }

  .container-panels {
    flex: 1;
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding: 6px;
  }

  .container-panels > :global(*) {
    flex: 0 0 280px;
    min-width: 280px;
    max-height: 500px;
  }

  .category-panel h4,
  .item-panel h4,
  .config-panel h4,
  .field-panel h4 {
    margin: 0;
    padding: 12px 16px;
    background: var(--theme-surface-light);
    border-bottom: 1px solid var(--theme-border);
    font-size: 14px;
    font-weight: 600;
    color: var(--theme-text);
  }

  .item-count {
    color: var(--theme-text-secondary);
    font-weight: normal;
    font-size: 12px;
  }

  .parameter-section,
  .preview-section {
    padding: 16px;
    border-bottom: 1px solid var(--theme-border);
  }

  .parameter-section:last-child,
  .preview-section:last-child {
    border-bottom: none;
  }

  .parameter-section h5,
  .preview-section h5 {
    margin: 0 0 12px 0;
    font-size: 12px;
    font-weight: 600;
    color: var(--theme-text-secondary);
    text-transform: uppercase;
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--theme-text-secondary);
    font-style: italic;
  }



  /* 对象列表样式 */
  .object-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .category-group {
    margin-bottom: 16px;
  }

  .category-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: var(--theme-surface-light);
    border-radius: 4px;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 600;
    color: var(--theme-text);
  }

  .category-icon {
    margin-right: 8px;
    font-size: 14px;
  }

  .object-item {
    display: block;
    width: 100%;
    padding: 12px;
    margin-bottom: 4px;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    background: var(--theme-surface);
    cursor: pointer;
    text-align: left;
    transition: all 0.2s;
  }

  .object-item:hover {
    background: var(--theme-surface-hover);
    border-color: var(--theme-primary);
  }

  .object-item.selected {
    background: var(--theme-surface-light);
    border-color: var(--theme-primary);
  }

  .object-name {
    font-size: 13px;
    font-weight: 600;
    color: var(--theme-text);
    margin-bottom: 4px;
  }

  .object-path {
    font-size: 11px;
    color: var(--theme-text-secondary);
    font-family: monospace;
    background: var(--theme-surface-light);
    padding: 2px 6px;
    border-radius: 2px;
  }

  /* 错误面板样式 */
  .error-panel {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
  }

  .error-content {
    text-align: center;
    max-width: 500px;
  }

  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .error-message {
    font-size: 16px;
    font-weight: 600;
    color: var(--error-color, #dc2626);
    margin-bottom: 12px;
    line-height: 1.4;
  }

  .error-description {
    font-size: 14px;
    color: var(--text-secondary, #666);
    line-height: 1.5;
  }

  /* 字段列表样式 */
  .field-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .field-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 12px;
    margin-bottom: 4px;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    background: var(--theme-surface);
    cursor: pointer;
    text-align: left;
    transition: all 0.2s;
  }

  .field-item:hover {
    background: var(--theme-surface-hover);
    border-color: var(--theme-primary);
  }

  .field-item.selected {
    background: var(--theme-surface-light);
    border-color: var(--theme-primary);
  }

  .field-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .field-name {
    font-size: 13px;
    font-weight: 600;
    color: var(--theme-text);
  }

  .field-type {
    font-size: 11px;
    color: var(--theme-text-secondary);
    font-family: monospace;
    background: var(--theme-surface-light);
    padding: 2px 6px;
    border-radius: 2px;
  }

  .expand-indicator {
    font-size: 12px;
    color: var(--theme-text-secondary);
  }

  /* 底部代码预览样式 */
  .bottom-preview {
    border-top: 1px solid var(--theme-border);
    padding: 12px 16px;
    background: var(--theme-surface-dark);
    width: 100%;
    flex-shrink: 0;
    height: 40px;
    display: flex;
    align-items: center;
    overflow: hidden;
  }

  .preview-code {
    font-family: var(--font-family-mono, 'Consolas', 'Monaco', 'Courier New', monospace);
    font-size: 12px;
    color: var(--theme-text);
    background: none;
    border: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
</style>
