<script lang="ts">
  /**
   * 右侧面板组件
   * 使用 Tab 组件管理多个页面
   */

  import Tab from '../components/Tab.svelte';
  import type { TabItem } from '../components/Tab.svelte';
  import PropertyPanel from './propertyPanel/propertyPanel.svelte';
  import ScriptPanel from './scripts/ScriptPanel.svelte';
  import { sceneModelState } from '../stores/sceneModelStore';

  // Tab 配置
  const tabs: TabItem[] = [
    { id: 'properties', label: '对象属性', icon: '📋' },
    { id: 'scripts', label: '脚本管理', icon: '📝' },
    { id: 'events', label: '事件监听', icon: '⚡' },
    { id: 'tools', label: '工具', icon: '🔧' },
    { id: 'settings', label: '设置', icon: '⚙️' }
  ];

  // 获取当前选中的对象
  let selectedObjects = $derived($sceneModelState.selectedObjects);
  let primaryObject = $derived(
    selectedObjects.length > 0 && $sceneModelState.primarySelectedIndex >= 0
      ? selectedObjects[$sceneModelState.primarySelectedIndex]
      : null
  );

  let activeTab = $state('properties');

  function handleTabChange(tabId: string) {
    activeTab = tabId;
  }
</script>

<div class="right-panel">
  <div class="header">
    <h2>开发工具</h2>
  </div>

  <div class="content">
    <Tab
      {tabs}
      {activeTab}
      ontabchange={handleTabChange}
    >
      {#snippet children(currentTab: string)}
        {#if currentTab === 'properties'}
          <PropertyPanel />
        {:else if currentTab === 'scripts'}
          <ScriptPanel selectedObject={primaryObject} />
        {:else if currentTab === 'events'}
          <div class="placeholder">事件监听功能开发中...</div>
        {:else if currentTab === 'tools'}
          <div class="placeholder">工具功能开发中...</div>
        {:else if currentTab === 'settings'}
          <div class="placeholder">设置功能开发中...</div>
        {/if}
      {/snippet}
    </Tab>
  </div>
</div>

<style>
  .right-panel {
    height: 100%;
    background: var(--theme-surface);
    color: var(--theme-text);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .header {
    padding: var(--spacing-3);
    border-bottom: 1px solid var(--theme-border);
    background: var(--theme-background);
  }

  .header h2 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-text);
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .placeholder {
    padding: var(--spacing-4);
    text-align: center;
    color: var(--theme-text-muted);
    font-style: italic;
  }
</style>
