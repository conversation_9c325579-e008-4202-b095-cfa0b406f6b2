<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { modalService } from '../stores/modalStore.svelte';

  // 组件属性
  export let bindingMode: 'none' | 'global' | 'parent' = 'none';
  export let globalDataPath: string = '';
  export let selectedField: string = '';
  export let parentPath: string = ''; // 🔑 父级的绑定路径

  // 事件派发器
  const dispatch = createEventDispatcher();

  // 绑定模式变化
  function handleModeChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    const newMode = target.value as 'none' | 'global' | 'parent';
    
    bindingMode = newMode;
    
    // 清理不相关的数据
    if (newMode !== 'global') {
      globalDataPath = '';
    }
    if (newMode !== 'parent') {
      selectedField = '';
    }

    dispatch('change', {
      mode: bindingMode,
      path: globalDataPath,
      field: selectedField
    });
  }

  // 打开数据选择模态框
  function openDataSelector() {
    console.log('🔧 DataBindingSelector: 打开数据选择模态框', bindingMode);

    if (bindingMode === 'global') {
      // 全局数据使用 UnifiedSelectionModal
      modalService.openDataBinding(
        {
          mode: 'data',
          initialPath: globalDataPath
        },
        (selectedPath: string) => {
          handleDataSelection(selectedPath);
        }
      );
    } else if (bindingMode === 'parent') {
      // 🔑 检查父级路径
      console.log('🔧 DataBindingSelector: 检查父级路径', {
        parentPath,
        hasParentPath: !!parentPath
      });

      if (!parentPath) {
        console.warn('🚨 DataBindingSelector: 父级组件没有绑定数据');
        alert('父级组件没有绑定数据，无法使用父级绑定');
        return;
      }

      console.log('✅ DataBindingSelector: 父级路径检查通过，打开模态框');

      // 父级数据使用专门的 ParentDataModal，传递父级路径
      modalService.openParentData(
        {
          parentPath: parentPath
        },
        (selectedField: string) => {
          handleDataSelection(selectedField);
        }
      );
    }
  }

  // 处理数据选择
  function handleDataSelection(selectedPath: string) {
    console.log('🔧 DataBindingSelector: 处理数据选择', selectedPath);

    if (bindingMode === 'global') {
      globalDataPath = selectedPath;
    } else if (bindingMode === 'parent') {
      selectedField = selectedPath;
    }

    dispatch('change', {
      mode: bindingMode,
      path: globalDataPath,
      field: selectedField
    });
  }

  // 获取显示文本
  function getDisplayText() {
    if (bindingMode === 'none') return '未绑定';
    if (bindingMode === 'global') return globalDataPath || '选择全局数据';
    if (bindingMode === 'parent') return selectedField || '选择字段';
    return '';
  }

  // 获取状态样式
  function getStatusClass() {
    if (bindingMode === 'none') return 'none';
    if (bindingMode === 'global' && globalDataPath) return 'active';
    if (bindingMode === 'parent' && selectedField) return 'active';
    return 'pending';
  }
</script>

<div class="data-binding-selector">
  <!-- 绑定模式选择 -->
  <div class="mode-row">
    <label for="binding-mode-select">数据绑定：</label>
    <select id="binding-mode-select" bind:value={bindingMode} on:change={handleModeChange}>
      <option value="none">不绑定</option>
      <option value="global">全局数据</option>
      <option value="parent">父级数据</option>
    </select>
  </div>

  <!-- 数据选择 -->
  {#if bindingMode !== 'none'}
    <div class="data-row">
      <button
        class="data-selector {getStatusClass()}"
        on:click={openDataSelector}
      >
        {getDisplayText()}
      </button>
    </div>
  {/if}
</div>

<!--
  数据选择模态框现在由全局模态框服务管理
  不需要在这里渲染模态框组件
-->

<style>
  .data-binding-selector {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .mode-row {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .mode-row label {
    color: #fff;
    font-size: 12px;
    min-width: 60px;
  }

  .mode-row select {
    flex: 1;
    padding: 4px 8px;
    background: #333;
    color: #fff;
    border: 1px solid #555;
    border-radius: 3px;
    font-size: 12px;
  }

  .data-row {
    margin-left: 68px;
  }

  .data-selector {
    width: 100%;
    padding: 6px 12px;
    background: #333;
    color: #fff;
    border: 1px solid #555;
    border-radius: 3px;
    font-size: 11px;
    text-align: left;
    cursor: pointer;
    font-family: monospace;
  }

  .data-selector:hover {
    background: #3a3a3a;
  }

  .data-selector.active {
    border-color: #4CAF50;
    color: #4CAF50;
  }

  .data-selector.pending {
    border-color: #f39c12;
    color: #f39c12;
  }

  .data-selector.none {
    border-color: #666;
    color: #999;
  }

  .data-selector:disabled {
    background: #4b5563;
    color: #9ca3af;
    cursor: not-allowed;
  }

  .hint-text {
    font-size: 11px;
    color: #9ca3af;
    font-style: italic;
    padding: 4px 8px;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 4px;
    border-left: 3px solid #ef4444;
  }
</style>
