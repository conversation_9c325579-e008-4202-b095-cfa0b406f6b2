import { BaseObjectModel } from '../baseObjectModel.svelte';
import type { Script } from '../../historyManager/types';

/**
 * UIContainer模型类 - 专门的容器UI组件模型
 * 对应UIContainer.js，具有UIComponent和脚本功能
 */
export class UIContainerModel extends BaseObjectModel {
    // 组件类型
    public readonly componentType = 'UIContainer';

    // 容器特有属性
    containerWidth = $state(100);
    containerHeight = $state(100);

    // 背景相关属性
    backgroundColor = $state<string | null>(null);
    backgroundAlpha = $state(1);
    borderColor = $state<string | null>(null);
    borderWidth = $state(0);
    borderRadius = $state(0);

    // 布局相关属性
    padding = $state(0);
    margin = $state(0);

    // 交互属性
    interactive = $state(true);
    interactiveChildren = $state(true);

    // 🔑 统一的脚本系统
    componentScripts = $state<Script[]>([]);

    constructor(container: any = {}) {
        super(container);

        // 初始化UIContainer特有属性
        this.containerWidth = container.containerWidth || container.width || 100;
        this.containerHeight = container.containerHeight || container.height || 100;

        // 同步到基类的width和height
        this.width = this.containerWidth;
        this.height = this.containerHeight;
        this.backgroundColor = container.backgroundColor || null;
        this.backgroundAlpha = container.backgroundAlpha || 1;
        this.borderColor = container.borderColor || null;
        this.borderWidth = container.borderWidth || 0;
        this.borderRadius = container.borderRadius || 0;
        this.padding = container.padding || 0;
        this.margin = container.margin || 0;
        this.interactive = container.interactive !== false;
        this.interactiveChildren = container.interactiveChildren !== false;

        // 🔑 统一的脚本系统 - 从原始对象获取脚本数组
        this.componentScripts = container.componentScripts || [];

        // 🔑 如果没有脚本，添加默认脚本
        if (this.componentScripts.length === 0) {
            this.componentScripts = [{
                id: 'default_lifecycle',
                name: '生命周期脚本',
                type: 'lifecycle',
                enabled: true,
                description: '默认的生命周期脚本',
                code: `/**
 * 🚀 onStart - 对象启动生命周期
 * 触发时机: 对象创建并添加到父容器时自动触发
 * 作用: 初始化对象状态、设置默认值、绑定数据等
 * 配合方法: 无需手动调用，系统自动触发
 */
function onStart() {
  console.log("容器启动:", self.name || "unnamed");
  // 在这里添加初始化逻辑
  // 例如: 设置初始背景、配置布局、初始化子对象等
}

/**
 * 🔄 onUpdate - 每帧更新生命周期
 * 触发时机: 每帧自动触发（约60FPS），只要对象存在就会持续调用
 * 作用: 实现动画、实时数据更新、状态检查等
 * 配合方法: 无需手动调用，系统自动触发
 * 注意: 避免在此方法中执行耗时操作
 */
// function onUpdate() {
//   console.log("每帧更新:", self.name);
//   // 在这里添加每帧更新逻辑
//   // 例如: 动画更新、布局调整、状态检查等
// }

/**
 * 📝 onFieldUpdate - 字段更新生命周期
 * 触发时机: 调用 updateFieldsToChildren(ctx) 或 onFieldUpdate(ctx) 时触发
 * 作用: 响应数据变化、更新UI显示、同步状态等
 * 配合方法: parentObject.updateFieldsToChildren(ctx) 或 object.onFieldUpdate(ctx)
 * 参数: ctx - 包含字段更新上下文信息的对象
 */
// function onFieldUpdate(ctx) {
//   console.log("字段更新:", self.name, ctx);
//   // 在这里添加字段更新时的处理逻辑
//   // ctx 包含: { field, oldValue, newValue, source, timestamp 等 }
//   // 例如: 根据 ctx.field 判断更新哪个属性
// }

/**
 * 🗑️ onDestroy - 对象销毁生命周期
 * 触发时机: 对象从父容器移除时自动触发
 * 作用: 清理资源、保存数据、解除绑定等
 * 配合方法: 无需手动调用，系统自动触发
 */
// function onDestroy() {
//   console.log("对象销毁:", self.name);
//   // 在这里添加清理逻辑
//   // 例如: 清理定时器、保存状态、解除事件绑定等
// }`
            }];
        }

        console.log('📦 UIContainerModel: 创建容器模型', {
            size: { width: this.width, height: this.height },
            backgroundColor: this.backgroundColor,
            childrenCount: this.children.length,
            hasOriginalScripts: !!container.componentScripts,
            scriptsCount: this.componentScripts.length,
            scriptNames: this.componentScripts.map(s => s.name)
        });
    }

    /**
     * 设置UIContainer特有属性同步（重写基类方法）
     */
    protected setupSpecificSync(): void {
        try {
            // 同步容器尺寸
            if (this._originalObject.updateSize && typeof this._originalObject.updateSize === 'function') {
                this._originalObject.updateSize(this.width, this.height);
            } else {
                this._originalObject.containerWidth = this.width;
                this._originalObject.containerHeight = this.height;
            }

            // 同步背景属性
            this._originalObject.backgroundColor = this.backgroundColor;
            this._originalObject.backgroundAlpha = this.backgroundAlpha;
            this._originalObject.borderColor = this.borderColor;
            this._originalObject.borderWidth = this.borderWidth;
            this._originalObject.borderRadius = this.borderRadius;

            // 同步布局属性
            this._originalObject.padding = this.padding;
            this._originalObject.margin = this.margin;

            // 同步交互属性
            this._originalObject.interactive = this.interactive;
            this._originalObject.interactiveChildren = this.interactiveChildren;

            // 同步脚本系统
            this._originalObject.componentScripts = this.componentScripts;

            // 如果背景属性发生变化，重新创建背景
            if (this.backgroundColor && this._originalObject.createBackground) {
                this._originalObject.createBackground();
            }

            console.log('🔧 UIContainerModel: 容器特有属性已同步');
        } catch (error) {
            console.error('UIContainerModel: 同步失败', error);
        }
    }



    /**
     * 设置背景色
     */
    setBackgroundColor(color: string | null, alpha: number = 1): void {
        this.backgroundColor = color;
        this.backgroundAlpha = alpha;
    }

    /**
     * 设置边框
     */
    setBorder(color: string | null, width: number, radius: number = 0): void {
        this.borderColor = color;
        this.borderWidth = width;
        this.borderRadius = radius;
    }

    /**
     * 获取容器信息用于显示
     */
    getContainerInfo(): {
        size: { width: number; height: number };
        background: { color: string | null; alpha: number };
        border: { color: string | null; width: number; radius: number };
        layout: { padding: number; margin: number };
        interaction: { interactive: boolean; interactiveChildren: boolean };
        scripts: { count: number; names: string[] };
    } {
        return {
            size: {
                width: this.width,
                height: this.height
            },
            background: {
                color: this.backgroundColor,
                alpha: this.backgroundAlpha
            },
            border: {
                color: this.borderColor,
                width: this.borderWidth,
                radius: this.borderRadius
            },
            layout: {
                padding: this.padding,
                margin: this.margin
            },
            interaction: {
                interactive: this.interactive,
                interactiveChildren: this.interactiveChildren
            },
            scripts: {
                count: this.componentScripts.length,
                names: this.componentScripts.map(script => script.name || 'unnamed')
            }
        };
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 创建 UIContainer 对象
        codes.push(`${indent}const ${varName} = new UIContainer({`);

        // 基础属性
        if (this.x !== 0) {
            codes.push(`${indent}    x: ${this.x},`);
        }
        if (this.y !== 0) {
            codes.push(`${indent}    y: ${this.y},`);
        }
        if (this.width !== 100) {
            codes.push(`${indent}    width: ${this.width},`);
        }
        if (this.height !== 100) {
            codes.push(`${indent}    height: ${this.height},`);
        }

        // 🔑 生成组件脚本数组（如果有的话）
        if (this.componentScripts && this._hasNonEmptyScripts()) {
            codes.push(`${indent}    componentScripts: ${JSON.stringify(this.componentScripts, null, 8).replace(/\n/g, `\n${indent}`)},`);
        }

        // 背景属性
        if (this.backgroundColor) {
            codes.push(`${indent}    backgroundColor: '${this.backgroundColor}',`);
            if (this.backgroundAlpha !== 1) {
                codes.push(`${indent}    backgroundAlpha: ${this.backgroundAlpha},`);
            }
        }

        // 边框属性
        if (this.borderColor && this.borderWidth > 0) {
            codes.push(`${indent}    borderColor: '${this.borderColor}',`);
            codes.push(`${indent}    borderWidth: ${this.borderWidth},`);
            if (this.borderRadius > 0) {
                codes.push(`${indent}    borderRadius: ${this.borderRadius},`);
            }
        }

        // 布局属性
        if (this.padding !== 0) {
            codes.push(`${indent}    padding: ${this.padding},`);
        }
        if (this.margin !== 0) {
            codes.push(`${indent}    margin: ${this.margin},`);
        }

        // 交互属性
        if (!this.interactive) {
            codes.push(`${indent}    interactive: false,`);
        }
        if (!this.interactiveChildren) {
            codes.push(`${indent}    interactiveChildren: false,`);
        }

        // 名称属性
        if (this.name && this.name.trim() !== '') {
            codes.push(`${indent}    name: ${JSON.stringify(this.name)},`);
        }

        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 🔑 检查是否有非空的脚本内容
     */
    private _hasNonEmptyScripts(): boolean {
        if (!this.componentScripts || this.componentScripts.length === 0) return false;

        // 检查是否有启用且有代码的脚本
        return this.componentScripts.some(script =>
            script.enabled && script.code && script.code.trim().length > 0
        );
    }

    /**
     * 克隆UIContainer对象（实现抽象方法）
     * @returns 克隆的UIContainerModel实例
     */
    clone(): UIContainerModel {
        console.log('🔄 UIContainerModel: 开始克隆UIContainer对象');

        // 获取原始UIContainer对象
        const originalContainer = this.getOriginalObject();
        if (!originalContainer || typeof originalContainer.clone !== 'function') {
            console.error('❌ UIContainerModel: 原始对象不存在或没有clone方法');
            throw new Error('UIContainer 原始对象不存在或没有clone方法');
        }

        // 调用原始对象的clone方法
        const clonedContainer = originalContainer.clone();

        // 创建新的模型实例
        const clonedModel = new UIContainerModel(clonedContainer);

        console.log('✅ UIContainerModel: 克隆完成，包含', clonedModel.children.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UIContainer 特有属性
            backgroundColor: this.backgroundColor,
            backgroundAlpha: this.backgroundAlpha,
            borderColor: this.borderColor,
            borderWidth: this.borderWidth,
            borderRadius: this.borderRadius,
            padding: this.padding,
            margin: this.margin,
            interactive: this.interactive,
            interactiveChildren: this.interactiveChildren,

            // UIComponent 属性
            name: this.name || '',

            // 脚本数组
            componentScripts: this.componentScripts ?
                this.componentScripts.map(script => ({ ...script })) : []
        };
    }
}

// 注册UIContainerModel到基类容器
BaseObjectModel.registerModel('UIContainer', UIContainerModel);
BaseObjectModel.registerModel('Container', UIContainerModel); // 也注册为Container，替换原来的ContainerModel
