<script lang="ts">
    import { PropertyContainer } from '../../../components/accordionPanel';
    import SafeInput from '../../../components/SafeInput.svelte';
    import Select from '../../../components/Select.svelte';
    import Label from '../../../components/Label.svelte';
    import type { MaskModel } from '../../../type/ui/maskModel.svelte';

    interface Props {
        model: MaskModel;
    }

    let { model }: Props = $props();

    // 遮罩类型选项
    const maskTypeOptions = [
        { value: 'rectangle', label: '矩形遮罩' },
        { value: 'circle', label: '圆形遮罩' },
        // { value: 'image', label: '图片遮罩' }
    ];

    // 是否显示图片相关选项
    let showImageOptions = $derived(model?.maskType === 'image');

    // 获取子对象信息
    let childrenInfo = $derived(() => {
        if (!model || !model.children) return [];
        return model.children.map((child, index) => ({
            index,
            name: child.name || `子对象${index + 1}`,
            type: child.className || child.constructor?.name || '未知类型',
            visible: child.visible,
            model: child
        }));
    });

    // 应用遮罩到所有子对象
    function applyMaskToAllChildren() {
        if (model) {
            model.applyMaskToAllChildren();
            console.log('🎭 MaskPropertyPanel: 已对所有子对象应用遮罩');
        }
    }

    // 重置遮罩设置
    function resetMask() {
        if (!model) return;

        console.log('🎭 MaskPropertyPanel: 重置遮罩设置');

        model.maskType = 'rectangle';
        model.maskImage = '';
        model.offsetX = 0;
        model.offsetY = 0;
        model.maskWidth = 200;
        model.maskHeight = 200;
    }
</script>

<!-- 遮罩类型 -->
<PropertyContainer>
    <Label text="遮罩类型:" />
    <Select
        options={maskTypeOptions}
        bind:value={model.maskType}
    />
</PropertyContainer>

<!-- 图片路径（仅当类型为image时显示） -->
{#if showImageOptions}
    <PropertyContainer>
        <Label text="图片路径:" />
        <SafeInput
            value={model.maskImage}
            type="text"
            placeholder="输入图片路径"
            onchange={(e: any) => model.maskImage = e.target.value}
        />
    </PropertyContainer>
{/if}

<!-- 遮罩尺寸 - v3.0版本：直接使用width和height -->
<PropertyContainer>
    <Label text="遮罩宽度:" />
    <SafeInput
        value={model.width.toString()}
        type="number"
        onchange={(e: any) => model.width = parseInt(e.target.value) || 1}
    />
</PropertyContainer>

<PropertyContainer>
    <Label text="遮罩高度:" />
    <SafeInput
        value={model.height.toString()}
        type="number"
        onchange={(e: any) => model.height = parseInt(e.target.value) || 1}
    />
</PropertyContainer>

<!-- 子对象信息 -->
<div class="children-section">
    <h4>� 遮罩子对象：</h4>

    <div class="children-status">
        <div class="status-header">
            <span class="status-label">子对象数量:</span>
            <span class="status-badge {childrenInfo().length > 0 ? 'has-children' : 'no-children'}">
                {childrenInfo().length} 个
            </span>
            {#if childrenInfo().length > 0}
                <button class="apply-mask-btn" onclick={applyMaskToAllChildren} title="重新应用遮罩">
                    🎭 应用遮罩
                </button>
            {/if}
        </div>

        {#if childrenInfo().length > 0}
            <div class="children-list">
                {#each childrenInfo() as child (child.index)}
                    <div class="child-item">
                        <span class="child-type">{child.type}</span>
                        <span class="child-name">{child.name}</span>
                        <span class="child-visible {child.visible ? 'visible' : 'hidden'}">
                            {child.visible ? '👁️' : '🙈'}
                        </span>
                    </div>
                {/each}
            </div>
        {:else}
            <div class="no-children-hint">
                <p>💡 将 UI 对象添加为子对象即可自动应用遮罩效果</p>
                <p>支持同时遮罩多个对象</p>
            </div>
        {/if}
    </div>
</div>

<!-- 操作按钮 -->
<div class="mask-actions">
    <button class="reset-button" onclick={resetMask}>
        重置设置
    </button>
</div>

<style>
    .children-section {
        margin: 15px 0;
        padding: 10px;
        background: #333;
        border-radius: 4px;
    }

    .children-section h4 {
        margin: 0 0 10px 0;
        color: #ffffff;
        font-size: 12px;
        font-weight: bold;
    }

    .children-status {
        margin-bottom: 10px;
    }

    .status-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .status-label {
        color: #ffffff;
        font-size: 11px;
        font-weight: bold;
    }

    .status-badge {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
        text-transform: uppercase;
    }

    .status-badge.has-children {
        background: #4CAF50;
        color: white;
    }

    .status-badge.no-children {
        background: #666;
        color: #ccc;
    }

    .apply-mask-btn {
        background: #2196F3;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 4px 8px;
        font-size: 10px;
        cursor: pointer;
        margin-left: 5px;
    }

    .apply-mask-btn:hover {
        background: #1976D2;
    }

    .children-list {
        background: #2a2a2a;
        border-radius: 3px;
        padding: 5px;
        max-height: 150px;
        overflow-y: auto;
    }

    .child-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px 6px;
        margin-bottom: 2px;
        background: #3a3a3a;
        border-radius: 2px;
        font-size: 10px;
    }

    .child-type {
        color: #81C784;
        font-weight: bold;
        min-width: 60px;
    }

    .child-name {
        color: #ffffff;
        flex: 1;
        margin: 0 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .child-visible {
        font-size: 12px;
    }

    .child-visible.visible {
        opacity: 1;
    }

    .child-visible.hidden {
        opacity: 0.5;
    }

    .no-children-hint {
        background: #2a2a2a;
        padding: 10px;
        border-radius: 3px;
        border-left: 3px solid #FF9800;
    }

    .no-children-hint p {
        font-size: 10px;
        color: #ccc;
        margin: 2px 0;
        font-style: italic;
    }

    .mask-actions {
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px solid #444;
    }

    .reset-button {
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 6px 12px;
        font-size: 11px;
        cursor: pointer;
        width: 100%;
    }

    .reset-button:hover {
        background: #c0392b;
    }
</style>
