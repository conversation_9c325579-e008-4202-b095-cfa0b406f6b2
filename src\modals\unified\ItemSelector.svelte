<script lang="ts">
  import type { DataItem, Method, ModalMode } from './types';

  let {
    items,
    selectedItem,
    onSelect,
    mode
  }: {
    items: (DataItem | Method)[];
    selectedItem: DataItem | Method | null;
    onSelect: (item: DataItem | Method) => void;
    mode: ModalMode;
  } = $props();

  function handleSelect(item: DataItem | Method) {
    onSelect(item);
  }

  function getItemIcon(item: DataItem | Method): string {
    if (mode === 'method') {
      return '🔧';
    } else if (mode === 'data') {
      if ('hasIndex' in item && item.hasIndex) {
        return '📊';
      } else {
        return '📋';
      }
    }
    return '⚡';
  }

  function getItemSubtitle(item: DataItem | Method): string {
    if (mode === 'method' && 'params' in item) {
      const paramCount = item.params?.length || 0;
      return paramCount > 0 ? `${paramCount} 个参数` : '无参数';
    } else if (mode === 'data' && 'path' in item) {
      return item.path;
    }
    return '';
  }
</script>

<div class="item-selector">
  {#each items as item}
    <button
      class="item-card"
      class:selected={selectedItem?.id === item.id}
      onclick={() => handleSelect(item)}
      title={item.description}
    >
      <div class="item-header">
        <span class="item-icon">{getItemIcon(item)}</span>
        <div class="item-title">
          <div class="item-label">{item.label}</div>
          <div class="item-subtitle">{getItemSubtitle(item)}</div>
        </div>
      </div>
      <div class="item-description">
        {item.description}
      </div>
      {#if mode === 'method' && 'example' in item && item.example}
        <div class="item-example">
          <code>{item.example}</code>
        </div>
      {/if}
    </button>
  {/each}
</div>

<style>
  .item-selector {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .item-card {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 16px;
    margin-bottom: 8px;
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
    text-align: left;
  }

  .item-card:hover {
    border-color: var(--primary-color, #2196f3);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
  }

  .item-card.selected {
    border-color: var(--primary-color, #2196f3);
    background: var(--primary-bg, #e3f2fd);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
  }

  .item-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
  }

  .item-icon {
    font-size: 18px;
    flex-shrink: 0;
    margin-top: 2px;
  }

  .item-title {
    flex: 1;
    min-width: 0;
  }

  .item-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary, #333);
    margin-bottom: 2px;
  }

  .item-subtitle {
    font-size: 12px;
    color: var(--text-muted, #666);
    font-family: 'Consolas', 'Monaco', monospace;
  }

  .item-description {
    font-size: 13px;
    color: var(--text-secondary, #555);
    line-height: 1.4;
  }

  .item-example {
    padding: 8px;
    background: var(--code-bg, #f5f5f5);
    border-radius: 4px;
    border-left: 3px solid var(--primary-color, #2196f3);
  }

  .item-example code {
    font-size: 12px;
    font-family: 'Consolas', 'Monaco', monospace;
    color: var(--code-color, #d63384);
  }

  .item-card.selected .item-label {
    color: var(--primary-color, #2196f3);
  }

  .item-card.selected .item-example {
    background: rgba(33, 150, 243, 0.1);
  }
</style>
