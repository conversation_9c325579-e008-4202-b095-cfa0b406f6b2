// RPG Maker MZ API 参考数据 - 兼容性文件
// 使用我们已经写好的模块化数据

// 导入模块化数据
import { dataObjects as dataObjectsNew } from './rpgData/dataObjects';
import { gameObjects as gameObjectsNew } from './rpgData/gameObjects';
import { managerObjects as managerObjectsNew } from './rpgData/managers';
import { coreClasses } from './rpgData/coreClasses';
import { basicExamples } from './rpgData/examples/basic';
import { advancedExamples } from './rpgData/examples/advanced';
import { utilityExamples } from './rpgData/examples/utils';
import { objectExamples } from './rpgData/objectExamples';

// 保持原有的接口定义（向后兼容）
export interface RPGItem {
  name: string;
  type: 'property' | 'method';
  description: string;
  code: string;
  returnType?: string;
  parameters?: string[];
}

export interface RPGObject {
  name: string;
  description: string;
  category: 'data' | 'game' | 'manager' | 'scene';
  properties: RPGItem[];
  methods: RPGItem[];
}

// 导出模块化数据
export const dataObjects: RPGObject[] = dataObjectsNew;
export const gameObjects: RPGObject[] = [...gameObjectsNew, ...coreClasses];
export const managerObjects: RPGObject[] = managerObjectsNew;
export const examples = [...basicExamples, ...advancedExamples, ...utilityExamples];
export { objectExamples };

// 默认导出
export default {
  dataObjects,
  gameObjects,
  managerObjects,
  examples,
  objectExamples
};
