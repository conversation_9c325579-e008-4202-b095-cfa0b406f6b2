/**
 * Scene_Menu 场景数据流定义
 */

import type { SceneDataFlow } from '../types';

/**
 * Scene_Menu 场景的数据流
 */
export const SceneMenuDataFlow: SceneDataFlow = {
  sceneName: "Scene_Menu",
  buttons: [
    {
      buttonName: "Item",
      triggerMethods: [
        "SceneManager.push(Scene_Item)"
      ]
    },
    {
      buttonName: "Skill", 
      triggerMethods: [
        "SceneManager.push(Scene_Skill)"
      ]
    },
    {
      buttonName: "Equip",
      triggerMethods: [
        "SceneManager.push(Scene_Equip)"
      ]
    },
    {
      buttonName: "Status",
      triggerMethods: [
        "SceneManager.push(Scene_Status)"
      ]
    },
    {
      buttonName: "Formation",
      triggerMethods: [
        "$gameParty.makeMenuActorNext()",
        "$gameParty.makeMenuActorPrevious()"
      ]
    },
    {
      buttonName: "Options",
      triggerMethods: [
        "SceneManager.push(Scene_Options)"
      ]
    },
    {
      buttonName: "Save",
      triggerMethods: [
        "SceneManager.push(Scene_Save)"
      ]
    },
    {
      buttonName: "Game End",
      triggerMethods: [
        "SceneManager.push(Scene_GameEnd)"
      ]
    }
  ]
};
