/**
 * 容器相关类型定义
 */

// 项目类型枚举
export enum ItemType {
  OBJECT = 'object',  // 蓝色
  ARRAY = 'array',    // 绿色  
  FIELD = 'field'     // 橙色（string/number/boolean等）
}

// 绑定规则接口
export interface BindingRule {
  type: ItemType;
  color: string;
  allowedComponents: string[];
  hint: string;
}

// 数据项接口
export interface DataContainerItem {
  id: string;
  name: string;
  label: string;
  type: ItemType;
  path: string;
  description?: string;
  value?: any;
}

// 字段项接口
export interface FieldContainerItem {
  id: string;
  name: string;
  label: string;
  type: ItemType;
  path: string;
  description?: string;
  value?: any;
  parentPath?: string;
}

// 容器面板接口
export interface ContainerPanel {
  id: string;
  title: string;
  type: 'data' | 'field';
  path: string;
  items: (DataContainerItem | FieldContainerItem)[];
  selectedItem?: DataContainerItem | FieldContainerItem;
}

// 绑定规则配置
export const BINDING_RULES: Record<ItemType, BindingRule> = {
  [ItemType.OBJECT]: {
    type: ItemType.OBJECT,
    color: 'blue',
    allowedComponents: ['UIItem'],
    hint: '只能绑定 UIItem'
  },
  [ItemType.ARRAY]: {
    type: ItemType.ARRAY, 
    color: 'green',
    allowedComponents: ['UILayout'],
    hint: '只能绑定 UILayout'
  },
  [ItemType.FIELD]: {
    type: ItemType.FIELD,
    color: 'orange', 
    allowedComponents: ['UILabel', 'UIImage'],
    hint: '可绑定 UILabel/UIImage'
  }
};

// 颜色主题配置
export const COLOR_THEMES = {
  blue: {
    background: 'rgba(59, 130, 246, 0.1)',
    border: '#3b82f6',
    text: '#1e40af'
  },
  green: {
    background: 'rgba(34, 197, 94, 0.1)',
    border: '#22c55e',
    text: '#15803d'
  },
  orange: {
    background: 'rgba(249, 115, 22, 0.1)',
    border: '#f97316',
    text: '#ea580c'
  }
};
