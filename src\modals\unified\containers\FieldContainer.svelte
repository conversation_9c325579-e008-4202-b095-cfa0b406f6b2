<script lang="ts">
  import type { FieldContainerItem } from '../types/containerTypes';
  import { getTypeClassName, getTypeIcon, formatBindingHint } from '../services/bindingRuleService';

  interface Props {
    title: string;
    items: FieldContainerItem[];
    selectedItem?: FieldContainerItem;
    onItemSelect: (item: FieldContainerItem) => void;
    onItemDoubleClick?: (item: FieldContainerItem) => void;
  }

  let { 
    title, 
    items, 
    selectedItem, 
    onItemSelect, 
    onItemDoubleClick 
  }: Props = $props();
</script>

<div class="field-container">
  <div class="container-header">
    <h4>{title}</h4>
    <span class="item-count">({items.length})</span>
  </div>
  
  <div class="container-content">
    {#if items.length > 0}
      <div class="field-list">
        {#each items as item}
          <button
            class="field-item {getTypeClassName(item.type)}"
            class:selected={selectedItem === item}
            onclick={() => onItemSelect(item)}
            ondblclick={() => onItemDoubleClick?.(item)}
          >
            <div class="field-main">
              <div class="field-icon">{getTypeIcon(item.type)}</div>
              <div class="field-info">
                <div class="field-name">{item.name}</div>
                <div class="field-type">{item.type}</div>
              </div>
              {#if item.type === 'object' || item.type === 'array'}
                <div class="expand-indicator">▶</div>
              {/if}
            </div>
            <div class="field-hint">
              {formatBindingHint(item.type)}
            </div>
          </button>
        {/each}
      </div>
    {:else}
      <div class="empty-state">
        <div class="empty-icon">📂</div>
        <div class="empty-text">暂无字段</div>
      </div>
    {/if}
  </div>
</div>

<style>
  .field-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: 8px;
    overflow: hidden;
  }

  .container-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--theme-surface-light);
    border-bottom: 1px solid var(--theme-border);
  }

  .container-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--theme-text);
  }

  .item-count {
    font-size: 12px;
    color: var(--theme-text-secondary);
    background: var(--theme-surface-dark);
    padding: 2px 6px;
    border-radius: 10px;
  }

  .container-content {
    flex: 1;
    overflow-y: auto;
    padding: 6px;
  }

  .field-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .field-item {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 8px;
    border: 1px solid var(--theme-border);
    border-radius: 6px;
    background: var(--theme-surface);
    cursor: pointer;
    text-align: left;
    transition: all 0.2s ease;
  }

  .field-item:hover {
    background: var(--theme-surface-hover);
    border-color: var(--theme-primary);
    transform: translateY(-1px);
  }

  .field-item.selected {
    background: var(--theme-primary-light, rgba(59, 130, 246, 0.15));
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2), 0 2px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  .field-main {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
  }

  .field-icon {
    font-size: 14px;
    flex-shrink: 0;
  }

  .field-info {
    flex: 1;
    min-width: 0;
  }

  .field-name {
    font-size: 13px;
    font-weight: 600;
    color: var(--theme-text);
    margin-bottom: 2px;
  }

  .field-type {
    font-size: 10px;
    color: var(--theme-text-secondary);
    font-family: monospace;
    background: var(--theme-surface-dark);
    padding: 1px 4px;
    border-radius: 2px;
    text-transform: uppercase;
  }

  .expand-indicator {
    font-size: 10px;
    color: var(--theme-text-secondary);
    opacity: 0.7;
  }

  .field-hint {
    font-size: 9px;
    color: var(--theme-text-secondary);
    font-style: italic;
    text-align: center;
    padding: 3px;
    background: var(--theme-surface-dark);
    border-radius: 3px;
  }

  /* 类型特定样式 */
  .field-item.item-object {
    border-left: 4px solid #3b82f6;
  }

  .field-item.item-object .field-hint {
    background: rgba(59, 130, 246, 0.1);
    color: #1e40af;
  }

  .field-item.item-array {
    border-left: 4px solid #22c55e;
  }

  .field-item.item-array .field-hint {
    background: rgba(34, 197, 94, 0.1);
    color: #15803d;
  }

  .field-item.item-field {
    border-left: 4px solid #f97316;
  }

  .field-item.item-field .field-hint {
    background: rgba(249, 115, 22, 0.1);
    color: #ea580c;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--theme-text-secondary);
  }

  .empty-icon {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 14px;
    font-style: italic;
  }
</style>
