// 管理器对象 - 全局管理器和工具类

import type { RPGObject } from './types';

export const managerObjects: RPGObject[] = [
  {
    name: 'SceneManager',
    description: '场景管理器',
    category: 'manager',
    properties: [],
    methods: [
      { name: 'goto', type: 'method', description: '跳转到场景', code: 'SceneManager.goto(Scene_Map)' },
      { name: 'push', type: 'method', description: '推入场景', code: 'SceneManager.push(Scene_Menu)' },
      { name: 'pop', type: 'method', description: '弹出场景', code: 'SceneManager.pop()' },
      { name: 'call', type: 'method', description: '调用场景', code: 'SceneManager.call(Scene_Shop)' },
      { name: 'isNextScene', type: 'method', description: '检查下一个场景', code: 'SceneManager.isNextScene(Scene_Title)' },
      { name: 'isCurrentScene', type: 'method', description: '检查当前场景', code: 'SceneManager.isCurrentScene(Scene_Map)' },
      { name: 'isSceneChanging', type: 'method', description: '是否正在切换场景', code: 'SceneManager.isSceneChanging()' }
    ]
  },
  {
    name: 'DataManager',
    description: '数据管理器',
    category: 'manager',
    properties: [],
    methods: [
      { name: 'setupNewGame', type: 'method', description: '完整的新游戏初始化（推荐用于新游戏按钮）', code: 'DataManager.setupNewGame()' },
      { name: 'createGameObjects', type: 'method', description: '仅创建游戏对象（用于测试、加载存档等场景）', code: 'DataManager.createGameObjects()' },
      { name: 'saveGame', type: 'method', description: '保存游戏', code: 'DataManager.saveGame(1)' },
      { name: 'loadGame', type: 'method', description: '加载游戏', code: 'DataManager.loadGame(1)' },
      { name: 'savefileExists', type: 'method', description: '存档是否存在', code: 'DataManager.savefileExists(1)' },
      { name: 'savefileInfo', type: 'method', description: '获取存档信息', code: 'DataManager.savefileInfo(1)' },
      { name: 'maxSavefiles', type: 'method', description: '最大存档数', code: 'DataManager.maxSavefiles()' },
      { name: 'isThisGameFile', type: 'method', description: '是否为当前游戏文件', code: 'DataManager.isThisGameFile(1)' },
      { name: 'isTitleSkip', type: 'method', description: '是否跳过标题', code: 'DataManager.isTitleSkip()' },
      { name: 'loadMapData', type: 'method', description: '加载地图数据', code: 'DataManager.loadMapData(1)' },
      { name: 'isMapLoaded', type: 'method', description: '地图是否已加载', code: 'DataManager.isMapLoaded()' },
      { name: 'loadAllSavefileImages', type: 'method', description: '加载所有存档图像', code: 'DataManager.loadAllSavefileImages()' },
      { name: 'latestSavefileId', type: 'method', description: '获取最新存档ID', code: 'DataManager.latestSavefileId()' }
    ]
  },
  {
    name: 'AudioManager',
    description: '音频管理器',
    category: 'manager',
    properties: [],
    methods: [
      { name: 'playBgm', type: 'method', description: '播放BGM', code: 'AudioManager.playBgm($dataSystem.titleBgm)' },
      { name: 'playBgs', type: 'method', description: '播放BGS', code: 'AudioManager.playBgs({name: "River", volume: 90, pitch: 100, pan: 0})' },
      { name: 'playSe', type: 'method', description: '播放SE', code: 'AudioManager.playSe({name: "Cursor1", volume: 90, pitch: 100, pan: 0})' },
      { name: 'stopMe', type: 'method', description: '停止ME', code: 'AudioManager.stopMe()' },
      { name: 'fadeOutBgm', type: 'method', description: 'BGM淡出', code: 'AudioManager.fadeOutBgm(3)' },
      { name: 'fadeOutBgs', type: 'method', description: 'BGS淡出', code: 'AudioManager.fadeOutBgs(3)' },
      { name: 'fadeOutMe', type: 'method', description: 'ME淡出', code: 'AudioManager.fadeOutMe(3)' },
      { name: 'checkErrors', type: 'method', description: '检查音频错误', code: 'AudioManager.checkErrors()' },
      { name: 'isCurrentBgm', type: 'method', description: '检查是否为当前BGM', code: 'AudioManager.isCurrentBgm($gameSystem.battleBgm())' },
      { name: 'stopSe', type: 'method', description: '停止SE', code: 'AudioManager.stopSe()' },
      { name: 'stopBgm', type: 'method', description: '停止BGM', code: 'AudioManager.stopBgm()' },
      { name: 'stopBgs', type: 'method', description: '停止BGS', code: 'AudioManager.stopBgs()' },
      { name: 'playMe', type: 'method', description: '播放ME音乐', code: 'AudioManager.playMe($dataSystem.gameoverMe)' },
      { name: 'stopAll', type: 'method', description: '停止所有音频', code: 'AudioManager.stopAll()' }
    ]
  },
  {
    name: 'ImageManager',
    description: '图像管理器',
    category: 'manager',
    properties: [
      { name: 'faceHeight', type: 'property', description: '脸图高度', code: 'ImageManager.faceHeight' },
      { name: 'faceWidth', type: 'property', description: '脸图宽度', code: 'ImageManager.faceWidth' }
    ],
    methods: [
      { name: 'isReady', type: 'method', description: '是否准备就绪', code: 'ImageManager.isReady()' },
      { name: 'loadSystem', type: 'method', description: '加载系统图像', code: 'ImageManager.loadSystem("GameOver")' },
      { name: 'loadBitmap', type: 'method', description: '加载位图', code: 'ImageManager.loadBitmap("img/system/", "GameOver")' },
      { name: 'clear', type: 'method', description: '清除图像缓存', code: 'ImageManager.clear()' }
    ]
  },
  {
    name: 'Graphics',
    description: '图形系统',
    category: 'manager',
    properties: [
      { name: 'boxWidth', type: 'property', description: 'UI区域宽度', code: 'Graphics.boxWidth' },
      { name: 'boxHeight', type: 'property', description: 'UI区域高度', code: 'Graphics.boxHeight' },
      { name: 'width', type: 'property', description: '画面宽度', code: 'Graphics.width' },
      { name: 'height', type: 'property', description: '画面高度', code: 'Graphics.height' }
    ],
    methods: []
  },
  {
    name: 'Input',
    description: '输入管理器',
    category: 'manager',
    properties: [],
    methods: [
      { name: 'isPressed', type: 'method', description: '检查按键是否按下', code: 'Input.isPressed("ok")' },
      { name: 'isTriggered', type: 'method', description: '检查按键是否触发', code: 'Input.isTriggered("cancel")' },
      { name: 'isRepeated', type: 'method', description: '检查按键是否重复', code: 'Input.isRepeated("down")' },
      { name: 'isLongPressed', type: 'method', description: '检查按键是否长按', code: 'Input.isLongPressed("ok")' }
    ]
  },
  {
    name: 'TouchInput',
    description: '触摸输入管理器',
    category: 'manager',
    properties: [
      { name: 'x', type: 'property', description: '触摸X坐标', code: 'TouchInput.x' },
      { name: 'y', type: 'property', description: '触摸Y坐标', code: 'TouchInput.y' }
    ],
    methods: [
      { name: 'isPressed', type: 'method', description: '是否正在触摸', code: 'TouchInput.isPressed()' },
      { name: 'isTriggered', type: 'method', description: '是否触发触摸', code: 'TouchInput.isTriggered()' },
      { name: 'isRepeated', type: 'method', description: '是否重复触摸', code: 'TouchInput.isRepeated()' },
      { name: 'isLongPressed', type: 'method', description: '是否长按', code: 'TouchInput.isLongPressed()' }
    ]
  },
  {
    name: 'SoundManager',
    description: '音效管理器',
    category: 'manager',
    properties: [],
    methods: [
      { name: 'playCursor', type: 'method', description: '播放光标音效', code: 'SoundManager.playCursor()' },
      { name: 'playOk', type: 'method', description: '播放确定音效', code: 'SoundManager.playOk()' },
      { name: 'playCancel', type: 'method', description: '播放取消音效', code: 'SoundManager.playCancel()' },
      { name: 'playBuzzer', type: 'method', description: '播放错误音效', code: 'SoundManager.playBuzzer()' },
      { name: 'playUseItem', type: 'method', description: '播放使用物品音效', code: 'SoundManager.playUseItem()' },
      { name: 'playUseSkill', type: 'method', description: '播放使用技能音效', code: 'SoundManager.playUseSkill()' },
      { name: 'playEquip', type: 'method', description: '播放装备音效', code: 'SoundManager.playEquip()' },
      { name: 'playSave', type: 'method', description: '播放保存音效', code: 'SoundManager.playSave()' },
      { name: 'playLoad', type: 'method', description: '播放加载音效', code: 'SoundManager.playLoad()' },
      { name: 'playShop', type: 'method', description: '播放商店音效', code: 'SoundManager.playShop()' }
    ]
  },
  {
    name: 'BattleManager',
    description: '战斗管理器 - 控制整个战斗流程',
    category: 'manager',
    properties: [],
    methods: [
      { name: 'saveBgmAndBgs', type: 'method', description: '保存BGM和BGS', code: 'BattleManager.saveBgmAndBgs()' },
      { name: 'playBattleBgm', type: 'method', description: '播放战斗BGM', code: 'BattleManager.playBattleBgm()' },
      { name: 'startBattle', type: 'method', description: '开始战斗', code: 'BattleManager.startBattle()' },
      { name: 'update', type: 'method', description: '更新战斗', code: 'BattleManager.update(true)' },
      { name: 'isActiveTpb', type: 'method', description: '是否为时间进度战斗', code: 'BattleManager.isActiveTpb()' },
      { name: 'isInputting', type: 'method', description: '是否正在输入', code: 'BattleManager.isInputting()' },
      { name: 'actor', type: 'method', description: '获取当前行动角色', code: 'BattleManager.actor()' },
      { name: 'inputtingAction', type: 'method', description: '获取当前输入的行动', code: 'BattleManager.inputtingAction()' },
      { name: 'processEscape', type: 'method', description: '处理逃跑', code: 'BattleManager.processEscape()' },
      { name: 'selectNextCommand', type: 'method', description: '选择下一个命令', code: 'BattleManager.selectNextCommand()' },
      { name: 'selectPreviousCommand', type: 'method', description: '选择上一个命令', code: 'BattleManager.selectPreviousCommand()' },
      { name: 'isBattleEnd', type: 'method', description: '战斗是否结束', code: 'BattleManager.isBattleEnd()' },
      { name: 'setLogWindow', type: 'method', description: '设置日志窗口', code: 'BattleManager.setLogWindow(logWindow)' },
      { name: 'setSpriteset', type: 'method', description: '设置精灵集', code: 'BattleManager.setSpriteset(spriteset)' }
    ]
  },
  {
    name: 'ConfigManager',
    description: '配置管理器 - 管理游戏设置和选项',
    category: 'manager',
    properties: [
      { name: 'alwaysDash', type: 'property', description: '始终跑步', code: 'ConfigManager.alwaysDash' },
      { name: 'commandRemember', type: 'property', description: '记住指令', code: 'ConfigManager.commandRemember' },
      { name: 'touchUI', type: 'property', description: '触碰界面', code: 'ConfigManager.touchUI' },
      { name: 'bgmVolume', type: 'property', description: '背景音乐音量', code: 'ConfigManager.bgmVolume' },
      { name: 'bgsVolume', type: 'property', description: '背景声音音量', code: 'ConfigManager.bgsVolume' },
      { name: 'seVolume', type: 'property', description: '音效音量', code: 'ConfigManager.seVolume' },
      { name: 'meVolume', type: 'property', description: '声效音量(ME)', code: 'ConfigManager.meVolume' }
    ],
    methods: [
      { name: 'load', type: 'method', description: '加载配置', code: 'ConfigManager.load()' },
      { name: 'save', type: 'method', description: '保存配置', code: 'ConfigManager.save()' },
      { name: 'makeData', type: 'method', description: '创建配置数据', code: 'ConfigManager.makeData()' },
      { name: 'applyData', type: 'method', description: '应用配置数据', code: 'ConfigManager.applyData(data)' }
    ]
  }
];
