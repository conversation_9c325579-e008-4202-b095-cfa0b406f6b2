// 基础示例 - 常用的基本操作

import type { RPGExample } from '../types';

export const basicExamples: RPGExample[] = [
  {
    name: '物品使用基础',
    type: 'method',
    description: '展示如何使用物品的基本流程',
    code: `// 物品使用基础示例
const actor = $gameActors.actor(1);
const item = $dataItems[1];

// 检查是否可以使用
if (actor.canUse(item)) {
  // 播放音效
  SoundManager.playUseItem();

  // 使用物品
  actor.useItem(item);

  // 减少物品数量
  $gameParty.loseItem(item, 1);

  console.log('物品使用成功');
} else {
  SoundManager.playBuzzer();
  console.log('无法使用该物品');
}`
  },
  {
    name: '场景跳转基础',
    type: 'method',
    description: '展示基本的场景切换操作',
    code: `// 场景跳转基础示例

// 跳转到菜单
SceneManager.goto(Scene_Menu);

// 推入场景（可以返回）
SceneManager.push(Scene_Shop);

// 弹出当前场景
SceneManager.pop();

// 调用场景
SceneManager.call(Scene_Save);

console.log('场景切换完成');`
  },
  {
    name: '音频播放基础',
    type: 'method',
    description: '展示基本的音频播放操作',
    code: `// 音频播放基础示例

// 播放BGM
AudioManager.playBgm($dataSystem.titleBgm);

// 播放音效
SoundManager.playOk();
SoundManager.playCursor();

// 停止音频
AudioManager.stopBgm();

// 淡出BGM
AudioManager.fadeOutBgm(3);

console.log('音频操作完成');`
  },
  {
    name: '角色信息获取',
    type: 'method',
    description: '获取角色的基本信息',
    code: `// 角色信息获取示例
const actor = $gameActors.actor(1);

console.log('=== 角色信息 ===');
console.log('姓名:', actor.name());
console.log('等级:', actor.level);
console.log('HP:', \`\${actor.hp}/\${actor.mhp}\`);
console.log('MP:', \`\${actor.mp}/\${actor.mmp}\`);
console.log('攻击力:', actor.atk);
console.log('防御力:', actor.def);

// 检查状态
if (actor.isDead()) {
  console.log('角色已死亡');
} else {
  console.log('角色存活');
}`
  },
  {
    name: '队伍管理基础',
    type: 'method',
    description: '基本的队伍操作',
    code: `// 队伍管理基础示例

// 获取队伍信息
const members = $gameParty.members();
console.log('队伍成员数:', members.length);
console.log('队伍金钱:', $gameParty.gold());

// 添加/移除角色
$gameParty.addActor(2);
console.log('添加角色2到队伍');

// 金钱操作
$gameParty.gainGold(1000);
console.log('获得1000金币');

// 物品操作
$gameParty.gainItem($dataItems[1], 5);
console.log('获得5个物品');`
  }
];
