<script lang="ts">
  /**
   * 响应式开关属性面板 - Switch组件专用
   * 直接使用响应式模型对象，无需额外的函数
   */

  import {
    sceneModelState
  } from '../../../stores/sceneModelStore';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import DropTarget from '../../../components/drop/DropTarget.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import LabelInput from '../../../components/LabelInput.svelte';
  import EventSelectionModal from '../../../modals/EventSelectionModal.svelte';
  import type { SwitchModel } from '../../../type/ui/switchModel.svelte';
  import type { BaseObjectModel } from '../../../type/baseObjectModel.svelte';

  // 监听选中的对象 - 使用第一个选中的对象
  let currentState = $derived($sceneModelState);
  let switchObj = $derived(currentState.selectedObjects[0] as SwitchModel | null);

  // 面板展开状态
  let isExpanded = $state(true);
  let isLayoutExpanded = $state(true);

  // 🔑 删除bindingInfo，直接使用模型对象中的字段

  // ==================== 🎯 布局控制函数 ====================

  /**
   * 🎯 计算并应用Switch布局 - 直接设置绑定对象的坐标
   */
  function performSwitchLayout() {
    if (!switchObj) {
      console.warn('🚨 Switch面板: 没有选中的Switch对象');
      return;
    }

    // 🔑 直接使用模型对象中的绑定字段
    const backgroundModel = switchObj.boundBackgroundSprite;
    const knobModel = switchObj.boundKnobSprite;

    if (!backgroundModel || !knobModel) {
      console.warn('🚨 Switch面板: 缺少必要的绑定组件');
      return;
    }

    // 计算布局坐标
    const trackWidth = backgroundModel.width || 100;
    const trackHeight = backgroundModel.height || 40;
    const knobWidth = knobModel.width || 20;
    const knobHeight = knobModel.height || 20;

    // 🔑 设置背景模型对象的坐标（会自动同步到原始对象并保存）
    backgroundModel.x = 0;
    backgroundModel.y = 0;

    // 🔑 设置滑块模型对象的坐标（根据当前状态）
    const knobOffX = 0;
    const knobOnX = Math.max(0, trackWidth - knobWidth);
    const knobY = (trackHeight - knobHeight) / 2;

    // 根据当前开关状态设置滑块位置
    knobModel.x = switchObj.isOn ? knobOnX : knobOffX;
    knobModel.y = knobY;

    // 🔑 不再需要布局缓存，坐标直接设置到模型对象上

    console.log('🎯 Switch面板: 布局完成', {
      background: `(${backgroundModel.x}, ${backgroundModel.y})`,
      knobOff: `(${knobOffX}, ${knobY})`,
      knobOn: `(${knobOnX}, ${knobY})`,
      currentKnob: `(${knobModel.x}, ${knobModel.y})`
    });
  }

  /**
   * 🎯 获取当前绑定对象的坐标信息
   */
  function getCurrentCoordinates() {
    if (!switchObj?.boundBackgroundSprite || !switchObj?.boundKnobSprite) {
      return null;
    }

    const backgroundModel = switchObj.boundBackgroundSprite;
    const knobModel = switchObj.boundKnobSprite;

    return {
      backgroundX: backgroundModel.x || 0,
      backgroundY: backgroundModel.y || 0,
      knobX: knobModel.x || 0,
      knobY: knobModel.y || 0
    };
  }

  /**
   * 🎯 设置绑定对象的坐标
   */
  function setCoordinate(objectType: 'background' | 'knob', axis: 'x' | 'y', value: number) {
    if (!switchObj) return;

    const backgroundModel = switchObj.boundBackgroundSprite;
    const knobModel = switchObj.boundKnobSprite;

    if (objectType === 'background' && backgroundModel) {
      backgroundModel[axis] = value;
    } else if (objectType === 'knob' && knobModel) {
      knobModel[axis] = value;
    }
  }

  // 响应式获取当前坐标
  let currentCoords = $derived(getCurrentCoordinates());

  // ==================== 🔗 组件绑定处理 ====================

  /**
   * 处理背景轨道拖拽绑定
   */
  function handleBackgroundDrop(droppedObject: BaseObjectModel) {
    if (!switchObj) return;

    console.log('🔘 Switch面板: 尝试绑定背景轨道', droppedObject.className);

    // 检查是否为UIImage类型
    if (droppedObject.className !== 'UIImage') {
      console.warn('🚨 Switch面板: 背景轨道只能绑定UIImage对象');
      return;
    }

    // 🔑 绑定模型对象，不是原始对象
    switchObj.boundBackgroundSprite = droppedObject;

    // 🔑 确保绑定的对象是Switch的子对象（用于代码生成）
    if (!switchObj.children.includes(droppedObject)) {
      switchObj.addChild(droppedObject);
      console.log('🔗 Switch面板: 将背景轨道添加为子对象');
    }

    console.log('✅ Switch面板: 背景轨道绑定成功');
  }

  /**
   * 处理滑块按钮拖拽绑定
   */
  function handleKnobDrop(droppedObject: BaseObjectModel) {
    if (!switchObj) return;

    console.log('🔘 Switch面板: 尝试绑定滑块按钮', droppedObject.className);

    // 检查是否为UIImage类型
    if (droppedObject.className !== 'UIImage') {
      console.warn('🚨 Switch面板: 滑块按钮只能绑定UIImage对象');
      return;
    }

    // 🔑 绑定模型对象，不是原始对象
    switchObj.boundKnobSprite = droppedObject;

    // 🔑 确保绑定的对象是Switch的子对象（用于代码生成）
    if (!switchObj.children.includes(droppedObject)) {
      switchObj.addChild(droppedObject);
      console.log('🔗 Switch面板: 将滑块按钮添加为子对象');
    }

    console.log('✅ Switch面板: 滑块按钮绑定成功');
  }

  // ==================== 🔓 解除绑定处理 ====================

  /**
   * 解除背景轨道绑定
   */
  function unbindBackground() {
    if (!switchObj) return;
    switchObj.boundBackgroundSprite = null;
    console.log('🔘 Switch面板: 背景轨道绑定已解除');
  }

  /**
   * 解除滑块按钮绑定
   */
  function unbindKnob() {
    if (!switchObj) return;
    switchObj.boundKnobSprite = null;
    console.log('🔘 Switch面板: 滑块按钮绑定已解除');
  }

</script>

{#if switchObj}
  <AccordionPanel
    title="开关属性"
    icon="🔘"
    badge="Switch"
    badgeVariant="info"
    bind:expanded={isExpanded}
  >
    <!-- 状态设置 -->
    <PropertyContainer>
        <Label text="启用状态:" />
        <Checkbox
          checked={switchObj.enabled}
          targetObject={switchObj}
          fieldName="enabled"
          enableHistory={true}
          name="启用状态"
          onChange={(checked) => {
            console.log("🔧 Switch面板: 启用状态变化", {from: switchObj.enabled, to: checked});
            switchObj.enabled = checked;
          }}
        />
        <Label text={switchObj.enabled ? '启用' : '禁用'} size="sm" variant="secondary" />
      </PropertyContainer>

      <!-- 🔑 当前值设置 -->
      <PropertyContainer>
        <Label text="当前值:" />
        <Checkbox
          checked={switchObj.isOn}
          targetObject={switchObj}
          fieldName="isOn"
          enableHistory={true}
          name="当前值"
          onChange={(checked) => {
            console.log("🔧 Switch面板: 当前值变化", {from: switchObj.isOn, to: checked});
            switchObj.isOn = checked;
            switchObj.value = checked; // 🔑 同步 value 属性
          }}
        />
        <Label text={switchObj.isOn ? '开启' : '关闭'} size="sm" variant="secondary" />
      </PropertyContainer>

      <!-- 🔑 编辑器事件控制 -->
      <PropertyContainer>
        <Label text="编辑器中执行事件:" />
        <Checkbox
          checked={switchObj.executeEventsInEditor}
          targetObject={switchObj}
          fieldName="executeEventsInEditor"
          enableHistory={true}
          name="编辑器中执行事件"
          onChange={(checked) => {
            console.log("🔧 Switch面板: 编辑器事件执行变化", {from: switchObj.executeEventsInEditor, to: checked});
            switchObj.executeEventsInEditor = checked;
          }}
        />
        <Label text={switchObj.executeEventsInEditor ? '启用' : '禁用'} size="sm" variant="secondary" />
      </PropertyContainer>

      <PropertyContainer>
        <Label text="动画时长:" />
        <LabelInput
          bind:value={switchObj.animationDuration}
          type="number"
          min={0}
          max={2000}
          step={50}
          targetObject={switchObj}
          fieldName="animationDuration"
          enableHistory={true}
          name="动画时长"
        />
        <Label text="ms" size="sm" variant="secondary" />
      </PropertyContainer>

    <!-- 🔑 子组件绑定状态 -->
    <div class="property-section">
      <h4>🔗 子组件绑定</h4>

      <!-- 背景轨道绑定 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">背景轨道 (UIImage) - 开关外观:</span>
          <span class="binding-status-badge {switchObj.boundBackgroundSprite ? 'bound' : 'unbound'}">
            {switchObj.boundBackgroundSprite ? '已绑定' : '未绑定'}
          </span>
          {#if switchObj.boundBackgroundSprite}
            <button class="unbind-btn" onclick={unbindBackground} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if switchObj.boundBackgroundSprite}
          <div class="bound-object-info">
            已绑定: {switchObj.boundBackgroundSprite?.className || '未知对象'}
          </div>
        {:else}
          <DropTarget
            onDrop={handleBackgroundDrop}
            placeholder="拖拽 UIImage 对象到此处绑定为背景轨道"
            targetObject={switchObj}
            fieldName="boundBackgroundSprite"
            enableHistory={true}
            operationName="绑定开关背景轨道"
          />
        {/if}
      </div>

      <!-- 滑块按钮绑定 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">滑块按钮 (UIImage) - 可移动按钮:</span>
          <span class="binding-status-badge {switchObj.boundKnobSprite ? 'bound' : 'unbound'}">
            {switchObj.boundKnobSprite ? '已绑定' : '未绑定'}
          </span>
          {#if switchObj.boundKnobSprite}
            <button class="unbind-btn" onclick={unbindKnob} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if switchObj.boundKnobSprite}
          <div class="bound-object-info">
            已绑定: {switchObj.boundKnobSprite?.className || '未知对象'}
          </div>
        {:else}
          <DropTarget
            onDrop={handleKnobDrop}
            placeholder="拖拽 UIImage 对象到此处绑定为滑块按钮"
            targetObject={switchObj}
            fieldName="boundKnobSprite"
            enableHistory={true}
            operationName="绑定开关滑块按钮"
          />
        {/if}
      </div>
    </div>
  </AccordionPanel>
{/if}

<!-- 🎯 布局控制面板 -->
{#if switchObj}
  <AccordionPanel title="🎯 布局控制" bind:expanded={isLayoutExpanded}>
    <div class="layout-section">
      <h4>🎯 自动布局</h4>

      <PropertyContainer>
        <button
          class="layout-btn primary"
          onclick={performSwitchLayout}
          disabled={!switchObj.boundBackgroundSprite || !switchObj.boundKnobSprite}
        >
          🎯 执行布局
        </button>
      </PropertyContainer>

      {#if !switchObj.boundBackgroundSprite || !switchObj.boundKnobSprite}
        <div class="layout-warning">
          ⚠️ 需要绑定背景轨道和滑块按钮才能执行布局
        </div>
      {/if}
    </div>

    {#if currentCoords}
      <div class="layout-section">
        <h4>📍 当前坐标</h4>

        <!-- 背景坐标 -->
        <PropertyContainer>
          <Label text="背景位置:" />
          <div class="coordinate-inputs">
            <input
              type="number"
              value={currentCoords.backgroundX}
              onchange={(e) => setCoordinate('background', 'x', Number((e.target as HTMLInputElement)?.value || 0))}
              placeholder="X"
            />
            <input
              type="number"
              value={currentCoords.backgroundY}
              onchange={(e) => setCoordinate('background', 'y', Number((e.target as HTMLInputElement)?.value || 0))}
              placeholder="Y"
            />
          </div>
        </PropertyContainer>

        <!-- 滑块坐标 -->
        <PropertyContainer>
          <Label text="滑块位置:" />
          <div class="coordinate-inputs">
            <input
              type="number"
              value={currentCoords.knobX}
              onchange={(e) => setCoordinate('knob', 'x', Number((e.target as HTMLInputElement)?.value || 0))}
              placeholder="X"
            />
            <input
              type="number"
              value={currentCoords.knobY}
              onchange={(e) => setCoordinate('knob', 'y', Number((e.target as HTMLInputElement)?.value || 0))}
              placeholder="Y"
            />
          </div>
        </PropertyContainer>
      </div>
    {/if}
  </AccordionPanel>
{/if}


<style>
  /* 基础样式 */
  .property-section {
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 4px;
    padding: 8px;
    border: 1px solid var(--theme-border-light, #e9ecef);
    margin-bottom: 8px;
  }

  .property-section h4 {
    margin: 0 0 8px 0;
    font-size: 11px;
    font-weight: 600;
    color: var(--theme-text, #1a202c);
  }



 

  /* 绑定相关样式 */
  .binding-item {
    margin-bottom: 12px;
    padding: 8px;
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 4px;
  }

  .binding-item:last-child {
    margin-bottom: 0;
  }

  .binding-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
  }

  .binding-label {
    font-size: 10px;
    font-weight: 500;
    color: var(--theme-text, #1a202c);
    flex: 1;
  }

  .binding-status-badge {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .binding-status-badge.bound {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .binding-status-badge.unbound {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
  }

  .unbind-btn {
    width: 16px;
    height: 16px;
    border: none;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    font-size: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .unbind-btn:hover {
    background: #c82333;
    transform: scale(1.1);
  }

  .bound-object-info {
    font-size: 9px;
    color: var(--theme-text-secondary, #718096);
    padding: 4px 8px;
    background: var(--theme-surface-variant, #f5f5f5);
    border-radius: 3px;
    border: 1px solid var(--theme-border-light, #e9ecef);
  }

  /* 布局控制样式 */
  .layout-section {
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 4px;
    padding: 8px;
    border: 1px solid var(--theme-border-light, #e9ecef);
    margin-bottom: 8px;
  }

  .layout-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    margin-right: 8px;
    transition: all 0.2s ease;
  }

  .layout-btn.primary {
    background: #007bff;
    color: white;
  }

  .layout-btn.primary:hover:not(:disabled) {
    background: #0056b3;
  }

  .layout-btn:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
  }

  .layout-warning {
    background: #fff3cd;
    color: #856404;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ffeaa7;
    font-size: 10px;
    margin-top: 8px;
  }

  .coordinate-inputs {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .coordinate-inputs input {
    flex: 1;
    min-width: 60px;
    padding: 4px 8px;
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 4px;
    font-size: 11px;
  }

</style>
