<script lang="ts">
  /**
   * 预制体项目组件 - 使用 DragSource 实现拖拽
   */
  import DragSource from '../../components/drop/DragSource.svelte';

  interface Props {
    prefab: {
      name: string;
      description: string;
      type: string;
      createdAt: number;
    };
    onDelete?: () => void;
  }

  let { prefab, onDelete }: Props = $props();

  // 🔧 创建预制体拖拽对象
  let prefabDragObject = $derived({
    className: `Prefab_${prefab.name}`,
    name: prefab.name,
    prefabName: prefab.name,
    prefabType: prefab.type,
    description: prefab.description
  } as any);

  // 🔧 拖拽开始处理
  function handleDragStart() {
    console.log('🔧 PrefabItem: 开始拖拽预制体', prefab.name);
  }

  // 格式化创建时间
  function formatDate(timestamp: number): string {
    const date = new Date(timestamp);
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // 获取类型图标
  function getTypeIcon(type: string): string {
    const iconMap: Record<string, string> = {
      'UILabel': '🏷️',
      'UIButton': '🔘',
      'UIContainer': '📦',
      'UIImage': '🖼️',
      'UIPanel': '🪟'
    };
    return iconMap[type] || '🧩';
  }
</script>

<DragSource object={prefabDragObject} ondragstart={handleDragStart}>
  {#snippet children()}
    <div class="prefab-item" role="button" tabindex="0">
  <!-- 预制体信息 -->
  <div class="prefab-info">
    <div class="prefab-header">
      <span class="prefab-icon">{getTypeIcon(prefab.type)}</span>
      <div class="prefab-details">
        <div class="prefab-name" title={prefab.name}>
          {prefab.name}
        </div>
        <div class="prefab-type">
          {prefab.type}
        </div>
      </div>
    </div>
    
    {#if prefab.description}
      <div class="prefab-description" title={prefab.description}>
        {prefab.description}
      </div>
    {/if}
    
    <div class="prefab-meta">
      <span class="prefab-date">
        {formatDate(prefab.createdAt)}
      </span>
    </div>
  </div>

  <!-- 操作按钮 -->
  <div class="prefab-actions">
    <button
      class="action-btn delete-btn"
      onclick={(e) => { e.stopPropagation(); onDelete?.(); }}
      title="删除预制体"
    >
      🗑️
    </button>
  </div>

      <!-- 拖拽提示 -->
      <div class="drag-hint">
        拖拽到场景创建实例
      </div>
    </div>
  {/snippet}
</DragSource>

<style>
  .prefab-item {
    display: flex;
    flex-direction: column;
    padding: 12px;
    margin-bottom: 8px;
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border, #e2e8f0);
    border-radius: 6px;
    cursor: grab;
    transition: all 0.2s ease;
    user-select: none;
    position: relative;
  }

  .prefab-item:hover {
    border-color: var(--theme-primary, #3b82f6);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }

  .prefab-info {
    flex: 1;
  }

  .prefab-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
  }

  .prefab-icon {
    font-size: 16px;
    flex-shrink: 0;
  }

  .prefab-details {
    flex: 1;
    min-width: 0;
  }

  .prefab-name {
    font-size: 13px;
    font-weight: 600;
    color: var(--theme-text-primary, #1a202c);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .prefab-type {
    font-size: 11px;
    color: var(--theme-text-secondary, #718096);
  }

  .prefab-description {
    font-size: 11px;
    color: var(--theme-text-secondary, #718096);
    margin-bottom: 6px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .prefab-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .prefab-date {
    font-size: 10px;
    color: var(--theme-text-tertiary, #a0aec0);
  }

  .prefab-actions {
    display: flex;
    gap: 4px;
    margin-top: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .prefab-item:hover .prefab-actions {
    opacity: 1;
  }

  .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: var(--theme-surface-light, #f8f9fa);
    color: var(--theme-text-secondary, #718096);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 10px;
  }

  .action-btn:hover {
    background: var(--theme-surface-hover, #e2e8f0);
    color: var(--theme-text-primary, #1a202c);
  }

  .delete-btn:hover {
    background: #fee2e2;
    color: #dc2626;
  }

  .drag-hint {
    position: absolute;
    bottom: 4px;
    right: 8px;
    font-size: 9px;
    color: var(--theme-text-tertiary, #a0aec0);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .prefab-item:hover .drag-hint {
    opacity: 1;
  }
</style>
